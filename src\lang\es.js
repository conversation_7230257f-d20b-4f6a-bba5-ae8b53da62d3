// 西班牙
const es = {
  login: {
    login: "Acceso",
    changelanguage: "cambiar de idioma",
    index: "Indice",
    title: "Sistema de consulta de clasificación COGY",
    Success: "Iniciar sesión exitosamente",
    PleaseInputUserName: "Por favor ingrese el nombre de usuario",
    plspassword: "Por favor ingrese la contraseña",
    plscode: "Por favor ingrese la contraseña del código de verificación",
    Code: "Código de verificación",
       chinese: "中文",
    english: "english",
    thai: "ภาษาไทย",
    ms: "Bahasa Melayu",
    vi: "Tiếng Việt",
    es: "Español",
    pt: "Bahasa Portugis",
    id: "Bahasa Indonesia",
    ph: "Filipino",
    SwipeRight: "deslizar hacia la derecha",
  },
  welcome: {
    title: "Bienvenido al sistema de consulta de registros de clasificación",
  },
  headers: {
    title: "Ordenar el sistema de consulta de datos.",
    loginout: "Salir del sistema",
  },
  sidebar: {
    Home: "página delantera",
    Syscfg: "Configuración del sistema",
    Userinfosup: "Para la gestión de usuarios de paquetes",
    User: "Gestión de usuarios",
    AddRole: "Agregar rol",
    Menu: "menú",
    Sysparam: "Parámetros del sistema",
    Errchute: "Cuadrícula anormal",
    Sysurl: "Número de días para guardar registros",
    Sortingplan: "plan de clasificación",
    Datamgr: "Gestión de datos",
    Runninglog: "Ejecutar registro",
    Locklog: "registro de bloqueo",
    Sortingefficiency: "Estadísticas de eficiencia",
    Packagenumberlog: "Número de paquete histórico",
    Sortingrepush: "Datos complementarios",
    Sortingreport: "Reportar estadísticas",
    Chutereport: "Volumen de clasificación de cuadrícula",
    Sortingcountsup: "Estadísticas de cantidad (para cenas privadas)",
    Sortingcountuser: "Estadísticas de cantidad (número de trabajo)",
    Packagenumbercount: "Estadísticas generales de contratación",
    Sortingefficiency: "Estadísticas de eficiencia",
    Errorreport: "Tipo de estadísticas",
    Monitor: "Monitoreo del sistema",
    Scada: "Monitoreo SCADA",
    Job: "tareas programadas",
    Config: "Configuración de parámetros",
    Dict: "Gestión de diccionarios",
  },
  page: {
    homepage: {
      boardingcount: "Número de carros",
      scancount: "Cantidad de escaneo",
      cellcount: "numero de cajas",
      Cancellation: "Cancelación",
      rabbitinterceptions: "Número total de intercepciones de Jitu",
      unmatchedcodes: "El código de tres segmentos no coincide con el patrón.",
      overbounds: "Número total de súper vueltas",
      anomalies: "Número total de excepciones",
      sortingvolume: "Volumen de clasificación",
      monday: "los lunes",
      tuesday: "Martes",
      wednesday: "Miércoles",
      thursday: "Jueves",
      friday: "Viernes",
      saturday: "Sábado",
      sunday: "Domingo",
    },
    Serviceusermanagement: {
      Refresh: "refrescar",
      AddUser: "Agregar nuevo usuario",
      BulkDelete: "Eliminación por lotes",
      FullName: "Nombre",
      EmployeeID: "Número de trabajo",
      Query: "Consulta",
      Import: "importar",
      OnlyExcel: "(Solo se pueden importar archivos en formato excel.xlsx)",
      SerialNumber: "número de serie",
      UserEmployeeID: "ID de usuario",
      UserName: "Nombre de usuario",
      UserID: "ID de usuario",
      Creator: "Creador",
      CreationTime: "tiempo de creación",
      Operation: "funcionar",
      UserPassword: "Contraseña de usuario",
      ConfirmPassword: "confirmar Contraseña",
      NewPassword: "Nueva contraseña",
      Save: "ahorrar",
      Cancel: "Cancelar",
      UpdateUser: "Modificar usuario",
    },
    usermanagement: {
      UserID: "Número de usuario",
      UserName: "Nombre de usuario",
      UserAccount: "Cuenta de usuario",
      PlaseUserAccount: "Por favor ingrese la cuenta de usuario",
      PhoneNumber: "Número de teléfono",
      PleasePhoneNumber: "Ingrese el número de teléfono",
      Status: "Estado",
      PleaseStatus: "Por favor ingresa el estado",
      StartDate: "fecha de inicio",
      EndDate: "fecha de finalización",
      CreationTime: "tiempo de creación",
      Operation: "funcionar",
      UserGender: "Género del usuario",
      UserNickname: "Apodo de usuario",
      Email: "Correo",
      UserPassword: "Contraseña de usuario",
      Active: "Normal",
      Inactive: "desactivar",
      Role: "Role",
      Note: "Observación",
      Search: "Buscar",
      Reset: "Reiniciar",
      Add: "Nuevo",
      Delete: "borrar",
      Update: "Revisar",
      More: "Más",
      ResetPassword: "restablecer contraseña",
    },
  },
  common: {
    DerivedData: "Exportar datos",
    ViewDWSPeakEffect: "Vea la máxima eficiencia de DWS",
    peakEffect: "máxima eficiencia",
    SearchData: "Datos de búsqueda",
    NoData: "Aún no hay datos",
    Arrival: "Llegada",
    Departure: "Salidas",
    ArrivalAndDeparture: "Dentro y fuera del puerto",
    CloseOther: "Cerrar otro",
    CloseAll: "Cerrar todo",
    RoleName: "Nombre del personaje",
    PlaseRoleName: "Por favor ingrese un nombre de rol",
    PermissionCharacters: "caracteres de permiso",
    PlasePermissionCharacters: "Por favor ingrese caracteres de permiso",
    Order: "orden",
    Status: "estado",
    Search: "buscar",
    Reset: "reiniciar",
    Delete: "borrar",
    Update: "Revisar",
    More: "Más",
    CreationTime: "tiempo de creación",
    Operation: "funcionar",
    Add: "Nuevo",
    UpdateCache: "Actualizar caché",
    Query: "Consulta",
    Import: "importar",
    Export: "Exportar",
    SerialNumber: "número de serie",
    RoleNumber: "número de rol",
    Memo: "Observación",
    UpdateDate: "Hora de actualización",
    ParameterName: "Nombre del parámetro",
    ParameterValue: "Valor del parámetro",
    PleaseSelectARole: "Por favor seleccione un rol",
    PleaseInputnickname: "Por favor ingrese el apodo del usuario",
    PleaseInputPhoneNumber: "Por favor introduce el número de teléfono móvil",
    PleaseInputEmail: "Por favor ingrese el correo electrónico",
    PleaseInputUserName: "Por favor ingrese el nombre de usuario",
    PleaseInputPassWord: "Por favor ingrese la contraseña",
    RoleStatus: "estado del personaje",
    RoleCode: "Número de usuario",
    PlaseInputRoleCode: "Por favor ingrese el número de usuario",
    CreatorID: "Número de creador",
    Determine: "Seguro",
    Cancellation: "Cancelar",
    Task: "Nombre de la tarea",
    PlaseTask: "Por favor ingresa un nombre de tarea",
    TaskCode: "Número de tarea",
    PlaseTaskCode: "Por favor ingrese el número de tarea",
    MenuName: "Nombre del menú",
    PlaseMenuName: "Por favor ingresa un nombre de menú",
    MenuStatus: "Estado del menú",
    ExpandAndCollapse: "expandir/contraer",
    SelectAllDonteSelectAll: "Seleccionar todo/Deseleccionar todo",
    ParentChildLinkage: "Vinculación padre e hijo",
    MenuPermissions: "Permisos de menú",
    PleaseEnterContent: "Por favor ingresa el contenido",
    Icon: "icono",
    ComponentPath: "ruta del componente",
    SchemeID: "ID del esquema",
    ModeType: "Tipo de clasificación",
    PlanName: "Nombre del esquema",
    PlanCode: "Número de plan",
    PlanDesc: "Descripción de la solución",
    PlanFlag: "Tipo de plan",
    IsSelected: "Ya sea para habilitar",
    UpdateDate: "tiempo de modificación",
    Detail: "Detalles",
    Enable: "permitir",
    Date: "tiempo",
    Grade: "calificación",
    Source: "fuente",
    Message: "información",
    ExtraData: "datos extra",
    plcType: "capa",
    Number: "número de serie",
    Component: "parte",
    Chute: "boca de rejilla",
    Supply: "tabla de paquetes",
    dwsNo: "número DWS",
    BigBarRate: "Proporción de paquetes grandes",
    Quantity: "cantidad",
    SmallBarRate: "Proporción de paquete pequeño",
    ExceptionCode: "Código de excepción",
    Code: "Número de orden",
    ScanTime: "tiempo de escaneo",
    BoardingTime: "hora de embarque",
    DropTime: "tiempo de caída",
    NextPieceTime: "Tiempo empaquetado",
    passBackTime: "Hora de regreso",
    arrivalTime: "Hora de llegada",
    PacketNumber: "Número de paquete",
    TimeType: "tipo de tiempo",
    turns: "numero de vueltas",
    Image: "imagen",
    UpperLevel: "Nivel Superior",
    LowerLevel: "Nivel Inferior",
    LayerNumber: "Número de Nivel",
    PleaseEnterTheDictionaryLabel:
      "Por favor, ingrese la etiqueta del diccionario",
    DictionaryName: "Nombre del Diccionario",
    DictionaryId: "ID del Diccionario",
    DictionaryType: "Tipo de Diccionario",
    PleaseEnterTheDictionaryType: "Por favor, ingrese el tipo de diccionario",
    PleaseEnterTheDictionaryName:
      "Por favor, ingrese el nombre del diccionario",
    BagBindingBfficer: "Personal de Empaque",
    DictionaryEncoding: "Codificación del Diccionario",
    DictionaryTag: "Etiqueta del Diccionario",
    DictionaryValue: "Valor del Diccionario",
    DictionarySort: "Orden del Diccionario",
    DictionaryRemark: "Observación",
    DictionaryCreateTime: "Fecha de Creación",
    DataTag: "Etiqueta de Datos",
    PleaseEnterTheDataLabel: "Por favor, ingrese la etiqueta de datos",
    DataKey: "Clave de Datos",
    StyleAttribute: "Atributo de Estilo",
    DisplayOrder: "Orden de Visualización",
    EchoStyle: "Estilo de Respuesta",
    ListClass: "Clase de Lista",
    PleaseEnterTheDataKey: "Por favor, ingrese la clave de datos",
    PleaseEnterTheStyleAttribute: "Por favor, ingrese el atributo de estilo",
    PleaseEnterTheDisplayOrder: "Por favor, ingrese el orden de visualización",
    PleaseEnterTheEchoStyle: "Por favor, ingrese el estilo de respuesta",
    PleaseEnterTheListClass: "Por favor, ingrese la clase de lista",
    PleaseEnterTheRemark: "Por favor, ingrese una observación",
    PleaseEnterTheContent: "Por favor, ingrese el contenido",
    TheAddressNeedsToBehttp:
      "Si selecciona una enlace externo, la dirección debe comenzar con `http(s)://`",
    TheAddressNeedsToBehttpUser:
      "Dirección de ruta de acceso, como: `user`. Si es una dirección de red externa que necesita acceso interno, debe comenzar con `http(s)://`",
    TheAddressNeedsToBehttpCatalogue:
      "Ruta de acceso del componente, como: `system/user/index`. Por defecto, está en el directorio `views`",
    TheDefaultPassingParametersForTheRoute:
      "Parámetros de paso por defecto para la ruta, como: `{'id': 1, 'name': 'ry'}`",
    TheComponentWillBeCachedByKeepAlive:
      "Si selecciona sí, será almacenado en caché por `keep-alive`. Necesita que el `name` del componente coincida con la dirección",
    SelectHiddenThenTheRoute:
      "Si selecciona oculto, la ruta no aparecerá en la barra lateral, pero podrá ser accedida",
    SelectDisableThenTheRouteSidebar:
      "Si selecciona deshabilitado, la ruta no aparecerá en la barra lateral y tampoco podrá ser accedida",
    PleaseEnterTheRouteParameters:
      "Por favor, ingrese los parámetros de la ruta",
    Yes: "Sí",
    No: "No",
    PermissionCharactersString: "String de permisos definidos en el controlador, por ejemplo: @PreAuthorize(`@ss.hasRole('admin')`)",
    Cache1: "Caché",
    NoCache: "Sin Caché",
    AddUser: "Agregar Usuario",
    BatchCancelAuthorization: "Cancelar Autorización en Lote",
    Close: "Cerrar",
    CancelAuthorization: "Cancelar Autorización",
    View: "Ver",
    UserType: "Tipo de Usuario",
    PleaseSelectUserType: "Por favor, seleccione el tipo de usuario",
    Forward: "Girar hacia adelante",
    Reverse: "Girar hacia atrás",
    Lock: "Bloquear",
    Unlock: "Desbloquear",
    SendMessage: "Enviar Mensaje",
    TaskGroup: "Nombre del Grupo de Tareas",
    PleaseSelectTaskGroup:
      "Por favor, seleccione el nombre del grupo de tareas",
    TaskStatus: "Estado de la Tarea",
    PleaseSelectTaskStatus: "Por favor, seleccione el estado de la tarea",
    Log: "Registro",
    InvokeTarget: "Cadena de Objetivos de Llamada",
    CronExpression: "Expresión de Ejecución Cron",
    ExecuteOnce: "Ejecutar una Vez",
    TaskDetails: "Detalles de la Tarea",
    DispatchLog: "Registro de Distribución",
    InvokeTarget: "Método de Llamada",
    BeanCallExample: "Ejemplo de Llamada a Bean: ryTask.ryParams('ry')",
    ClassCallExample:
      "Ejemplo de Llamada a Clase: com.ruoyi.quartz.task.RyTask.ryParams('ry')",
    ParameterDescription:
      "Descripción de Parámetros: Compatible con cadenas, tipo booleano, long, float, int",
    PleaseInputInvokeTarget:
      "Por favor, ingrese la cadena de objetivos de llamada",
    PleaseInputCronExpression:
      "Por favor, ingrese la expresión de ejecución Cron",
    GenerateExpression: "Generar Expresión",
    ExecuteStrategy: "Estrategia de Ejecución",
    MisfirePolicy: "Política de Falta de Ejecución",
    ImmediateExecution: "Ejecución Inmediata",
    DelayExecution: "Ejecución con Retraso",
    AbandonExecution: "Abandonar Ejecución",
    PleaseSelectExecuteStrategy:
      "Por favor, seleccione la estrategia de ejecución",
    Concurrent: "Concurrente",
    Allow: "Permitir",
    Prohibit: "Prohibir",
    PleaseSelectConcurrent: "Por favor, seleccione si es concurrente",
    CronExpressionGenerator: "Generador de Expresiones Cron",
    NextExecutionTime: "Próxima Hora de Ejecución",
    TaskDetails: "Detalles de la Tarea",
    TaskGroup1: "Grupo de Tareas",
    DefaultStrategy: "Estrategia por Defecto",
    ExecuteStatus: "Estado de Ejecución",
    PleaseSelectExecuteStatus: "Por favor, seleccione el estado de ejecución",
    ExecutionTime: "Hora de Ejecución",
    PleaseSelectExecutionTime: "Por favor, seleccione la hora de ejecución",
    Clear: "Borrar",
    JobLogId: "ID del Registro de Trabajo",
    JobMessage: "Mensaje del Trabajo",
    Detail1: "Detalle",
    DispatchLogDetails: "Detalles del Registro de Distribución",
    PleaseSelect: "Por favor, seleccione",
    SelectStartTime: "Seleccione la Hora de Inicio",
    SelectEndTime: "Seleccione la Hora de Finalización",
    DebugStart: "Depurar (Iniciar)",
    DebugClose: "Depurar (Cerrar)",
    ClearPacketNumber: "Borrar Número de Paquete",
    Upload: "Subir",
    TotalQuery: "Se encontraron en total",
    property: "imagenes",
    all: "todos",
    UnloadingToDeliveryScanning: "Escánear Descarga a Entrega",
    BuildingPackageScanning: "Escánear Empaque",

    SelectUser: "Seleccionar Usuario",
    InvokeTargetMethod: "Método de Llamada de Objetivo",
    PleaseSelectTaskGroup: "Por favor, seleccione el grupo de tareas",
    CronExpression1: "cron expression",
    ExceptionInfo: "Información de Excepción",

    Edit: "Editar",
    ScopeOfAuthority: "Alcance de Autoridad",
    DataPermission: "Permiso de Datos",
    Confirm: "Confirmar",
    StartDate: "Fecha de Inicio",
    EndDate: "Fecha de Finalización",
    Weight: "peso",
    length: "largo",
    width: "Ancho",
    heigth: "alto",
    CarNumber: "numero de auto",
    RequestedGate: "celosía solicitada",
    PhysicalGrid: "La red física devuelta por el PLC",
    taskNo: "Número de tarea",
    TerminalDispatchCode: "código de tres párrafos",
    FallingGridTime: "Tiempo necesario para entrar en la casilla",
    PackageGrade: "Número de paquete",
    ChipNumber: "número de chip",
    BindingTime: "tiempo de agrupación",
    BindingPersonnel: "secuestradores",
    ScanType: "Tipo de escaneo",
    NextStationNumber: "Número de próxima parada",
    Rfid: "Etiqueta de equipaje electrónica",
    TimeInterval: "intervalo de tiempo",
    SortingQuantity: "Cantidad de clasificación",
    TotalSortingWeight: "Peso total de clasificación (KG)",
    NumberOfScannedBarcodeRecords:
      "Escanear cantidad de registros de códigos de barras",
    RecordTheNumberOfPackagesLoadedOntoTheVehicle:
      "Número récord de paquetes a bordo",
    NumberOfDropFeedbackRecords: "Número de registros de comentarios directos",
    scanQuantity: "Cantidad de escaneo",
    arrivalQuantity: "Cantidad de llegada",
    passBackQuantity: "Cantidad de devolución",
    TotalNumberOfPackages: "Cantidad total del paquete",
    Packagenumb: "Número de paquete (número de piezas dentro de este período)",
    Type: "tipo",
    Count: "cantidad",
    SystemType: "Tipo de sistema",
    EmployeeID: "Por favor ingrese su número de trabajo",
    FullName: "Por favor ingrese el nombre",
    Password: "Por favor ingrese la contraseña",
    ConfirmPassword: "Por favor ingrese la contraseña nuevamente",
    SelectGender: "Por favor seleccione género",
    Active: "normal",
    Inactive: "desactivar",
    Male: "masculino",
    Female: "femenino",
    Unknown: "desconocido",
    RoleOrder: "Orden de roles",
    Show: "espectáculo",
    Hide: "esconder",
    Default: "por defecto",
    System: "sistema",
    Success: "éxito",
    Failure: "fallar",
    AddMenu: "Agregar menú",
    EditMenu: "Modificar menú",
    ParentMenu: "Menú anterior",
    MenuType: "Tipo de menú",
    Directory: "Tabla de contenido",
    Menu: "menú",
    Button: "botón",
    MenuIcon: "icono de menú",
    SelectIcon: "Seleccionar icono",
    RouteAddress: "dirección de ruta",
    DisplayOrder: "Mostrar ordenar",
    ExternalLink: "Ya sea para enlace externo",
    DisplayStatus: "Mostrar estado",
    MenuStatus: "Estado del menú",
    RouteParameters: "parámetros de enrutamiento",
    Cache: "Ya sea para almacenar en caché",
    ComponentPath: "ruta del componente",
    AddRole: "Agregar rol",
    EditRole: "Modificar rol",
    AddPlan: "Agregar nuevo plan",
    Cancel: "Cancelar",
    FirstSegmentCode: "primer párrafo del código",
    SecondSegmentCode: "Segundo párrafo del código",
    ThirdSegmentCode: "El tercer párrafo del código",
    NextStopCode: "Número de próxima parada",
    NextStopName: "Nombre de la próxima parada",
    ModificationTime: "tiempo de modificación",
    BulkDelete: "Eliminación por lotes",
    AddDetails: "Agregar detalles",
    ByServicePackage: "Según la tabla",
    ByDwsNo: "Presione el número de serie de DWS",
    Granularity: "granularidad",
    LogicCode: "código lógico",
    PacketType: "tipo de paquete",
    IsUpload: "Ya sea para cargar",
    AddSuccess: "Agregado exitosamente",
    EditSuccess: "Modificación exitosa",
    DelSuccess: "Eliminar exitosamente",
    ImportSuccessful: "Importación exitosa",
    BeginExport: "Iniciar exportación",
    ModificationFailed: "La modificación falló",
    AddFailed: "No se pudo agregar",
    OperationSuccessful: "Operación exitosa",
    OperationFailed: "Operación fallida",
    OperationCancellation: "Operación cancelada",
    ExportFailed: "Exportación fallida",
    LoginOut:
      "Has cerrado tu sesión. Puedes cancelar y permanecer en esta página o iniciar sesión nuevamente.",
    ConfirmLogout: "Aceptar para cerrar sesión",
    LogAgain: "Inicia sesión nuevamente",
    Remark: "Observación",
    DwsNo: "número de serie del DWS",
    Notempty:
      "¡La contraseña de la cuenta o el código de verificación no pueden estar vacíos!",
    Notpassword: "¡La contraseña de la cuenta no puede estar vacía!",
    Id: "Clave primaria del parámetro",
    Parameter: "Nombre del parámetro",
    PlParameter: "Por favor ingrese el nombre del parámetro",
    ParameterKey: "Nombre de clave de parámetro",
    PlParameterKey: "Por favor ingrese el nombre de la clave del parámetro",
    ParameterValue: "Valor clave del parámetro",
    PlParameterValue: "Por favor ingrese el valor de la clave del parámetro",
    Group: "Nombre del grupo",
    PlGroup: "Por favor seleccione un nombre de grupo",
  },
  scada: {
    DeviceRunningStatus: "Estado de funcionamiento del equipo",
    DeviceStopped: "El dispositivo deja de funcionar",
    DeviceRunning: "El equipo esta funcionando",
    StartTime: "Iniciar tiempo de ejecución",
    RunningSpeed: "velocidad de carrera",
    CartOccupancyRate: "coche compartido",
    TotalDistanceTraveledByDevice:
      "Kilómetros totales de funcionamiento del equipo",
    DistanceTraveledInCurrentRun: "Número de kilómetros recorridos esta vez",
    TotalDistanceTraveled: "Kilómetros recorridos totales",
    Scans: "Número de escaneos",
    PendingStart: "Esperando empezar a correr",
    FullScreen: "Visualización en pantalla completa",
    ExitFull: "Salir de pantalla completa",
    UpperLevelRunningSpeed: "Velocidad de carrera de la capa superior",
    UpperLevelDeviceRunningStatus: "Estado operativo del dispositivo superior",
    UpperLevelStartTime:
      "Tiempo de inicio de funcionamiento de la capa superior",
    UpperLevelCartOccupancyRate: "Tasa de ocupación de vehículos más alta",
    UpperLevelByDevice:
      "Kilómetros operativos totales de equipos de nivel superior",
    LowerLevelDeviceRunningStatus: "Estado operativo inferior del dispositivo",
    UpperLayerPLCDisconnect: "El PLC superior está desconectado anormalmente",
    LowerLevelPLCDisconnect: "El PLC inferior está desconectado anormalmente",
    UpperLayerPLCConnectionStatus:
      "Estado de conexión del PLC de capa superior",
    LowerLevelPLCConnectionStatus: "Estado de conexión inferior del PLC",
    LowerLevelStartTime:
      "Tiempo de inicio de funcionamiento de la capa inferior",
    LowerLevelRunningSpeed: "Velocidad de carrera de la capa inferior",
    LowerLevelCartOccupancyRate: "Menor tasa de ocupación de vehículos",
    LowerLevelTotalDistanceTraveledByDevice:
      "Kilómetros operativos totales de equipos de nivel inferior",
    UpperLevelScans: "Número de escaneos de la capa superior",
    LowerLevelScans: "Número de escaneos de capa inferior",
    AbnormalQuantity: "Cantidad anormal",
    NumberOfSlotsOccupied: "numero de cajas",
    FailedRepushQuantity: "Número de devoluciones fallidas",
    InterceptedQuantity: "Número de intercepciones",
    ExcessiveCirclesQuantity: "número de súper vueltas",
    UnconfiguredThreeSegmentCodeSlots:
      "El puerto de código de tres segmentos no está configurado",
    ComprehensiveExceptionSlots: "Cuadrícula anormal completa",
    CancelledItems: "Cancelación",
    UnobtainedThreeSegmentCodeInformation:
      "Información del código de tres segmentos no obtenida",
    WebSocketStatus: "Estado del WebSocket",
    WCSCommunicationStatus: "Estado de comunicación WCS",
    Connected: "Conectado",
    NotConnected: "No conectado",
    SortingStatus: "Estado de clasificación",
    Idle: "inactivo",
    Loaded: "Carga",
    CartStatus: "Estado del carro",
    LittleThingsAreQuick:'Manos rápidas pequeñas',
    Headscratcher: 'Barrido superior',
    OnLine:'En línea',
    Offline:'Fuera de línea',
    Locked: "cierre",
    FullPackage: "paquete completo",
    SlotStatus: "Estado del compartimento",
    InterceptedItem: "Interceptador",
    ExceptionSlot: "Puerto anormal",
    PendingCommunication: "Esperando comunicación",
    Max: "ciclo máximo",
    Cancellation: "Cancelación",
    UnProgramme: "Sin plan",
    UnThree: "No equipado con código de tres segmentos",
    CartOperation: "Operación del carro",
    OneKeyUnlock: "Un clic para desbloquear",
    OneKeyLockSlot: "Bloqueo con un clic",
    CartNumber: "numero de auto",
    FloorNumber: "Número de capas",
    UpperLevel: "capa superior",
    LowerLevel: "nivel inferior",
    Lock: "cierre",
    Unlock: "Descubrir",
    ManualSwitchStart: "Conmutación manual (activación)",
    ManualSwitchClose: "Interruptor manual (apagado)",
    Run: "correr",
    Close: "cierre",
    DisabledList: "Lista de discapacitados",
    AbnormalAlarm: "alarma anormal",
    DisableCart: "desactivar carrito",
    UpperLevelCart: "Coche superior",
    LowerLevelCart: "carro inferior",
    VerificationPassword: "Por favor ingrese la contraseña de verificación",
    Confirm: "confirmar",
    ClearTheKilometers: "Borrar kilómetros",
    PleaseSelectClearKilometers: "Seleccione kilómetros claros",
    FaultLevel: "Nivel de falla",
    StartTime: "hora de inicio",
    EndTime: "tiempo final",
    CriticalAlarm: "alarma grave",
    GeneralAlarm: "alarma general",
    MinorAlarm: "Alarma menor",
    SearchCriteria: "Criterios de búsqueda",
    Time: "tiempo",
    Query: "Consulta",
    PleaseEnterContent: "Por favor ingresa el contenido",
    SerialNumber: "número de serie",
    Operation: "funcionar",
    AlarmHelpLink: "Documentación de ayuda",
    AlarmType: "Tipo de alarma",
    AlarmSource: "Fuente de alarma",
    Content: "contenido",
  },
};
export default es;
