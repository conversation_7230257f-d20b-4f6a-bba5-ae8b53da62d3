/* Dialog 相关样式 */
::v-deep .el-dialog__header {
  border-bottom: 1px solid #e5e5e5 !important;
}
::v-deep .el-dialog {
  background: url("~@/assets/images/scada/forewarningDialog.png") no-repeat
    center center;
  background-size: 100% 100%;
}

/* 状态栏样式 */
.status-bottom {
  position: absolute;
  left: 0;
  width: 81%;
  transition: bottom 0.3s;
}

.status-bar {
  display: flex;
  flex-wrap: wrap;
  padding: 8px 15px;
  background-color: #ffffff;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  font-size: 13px;
}

.status-row {
  display: flex;
  width: 100%;
  gap: 20px;
  padding: 5px 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: fit-content;
  color: #171717;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin: 0 2px;
}

/* 状态点颜色 */
.connected {
  background-color: #52c41a;
} /* 绿色 */
.disconnected {
  background-color: #f5222d;
} /* 红色 */
.pending {
  background-color: #722ed1;
} /* 紫色 */
.warning {
  background-color: #faad14;
} /* 黄色 */
.neutral {
  background-color: #bfbfbf;
} /* 灰色 */
.active {
  background-color: #1890ff;
} /* 蓝色 */
.black {
  background-color: #262626;
} /* 黑色 */

/* 布局样式 */
.FaultLevelSty {
  display: flex !important;
  justify-content: space-between;
  align-items: start;
}

.fontWidth {
  font-weight: 600;
}

.AbnormalStyle {
  margin-bottom: 20px;
}

/* 文本样式 */
text {
  font-family: "微软雅黑";
  transition: font-size 0.3s;
  user-select: none;
  pointer-events: none;
}

text:hover {
  font-size: 14px;
  cursor: pointer;
}

/* 页面布局 */
.page {
  width: 100vw;
  height: 90vh;
  background-image: url("~@/assets/images/scada/scadacarbg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
  box-sizing: border-box;
  padding: 20px 0px 0 20px;
}

.content-wrapper {
  display: flex;
  gap: 20px;
  height: 100vh;
}

/* 左侧容器 */
.left-container {
  flex: 1.9;
  min-width: 0;
}

/* 右侧容器 */
.right-container {
  flex: 1.1;
  width: 60%;
  min-width: 300px;
  height: 100%;
  overflow: auto;
  transition: all 0.3s ease;

  // 添加弹性布局
  display: flex;
  flex-direction: column;
  gap: 1rem;
  .white-box {
    height: auto;
    min-height: 200px;
    max-height: calc(100vh - 2rem);
  }

  .upper-box,
  .lower-box {
    height: auto;
    min-height: 150px;
  }
}

/* 响应式布局 */
/* 大屏幕 */
@media screen and (min-width: 1440px) {
  .right-container {
    width: 65%;
    max-width: 800px;
  }
}

/* 中等屏幕 */
@media screen and (max-width: 1200px) {
  .right-container {
    width: 55%;
    min-width: 280px;
  }
}

/* 平板 */
@media screen and (max-width: 992px) {
  .right-container {
    width: 50%;
    min-width: 260px;
  }
}

/* 手机端 */
@media screen and (max-width: 768px) {
  .right-container {
    width: 100%;
    min-width: 100%;
    height: auto;
    margin-top: 1rem;
  }

  .content-wrapper {
    flex-direction: column;
  }
  
  .page {
    padding: 15px;
    height: 85vh;
  }

  .content-wrapper {
    flex-direction: column;
    gap: 10px;
    height: calc(85vh - 30px);
  }

  .right-container {
    min-width: 100%;
    height: 30vh;
  }

  .left-container {
    height: 50vh;
  }
  
  .control-buttons {
    flex-wrap: wrap;
    justify-content: center;
    width: 90%;
  }

  .control-buttons button {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* 盒子样式 */
.white-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.upper-box {
  flex: 2;
  width: 98%;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.lower-box {
  flex: 1;
  background-color: #ffffff;
  border-radius: 6px;
}

/* SVG 元素样式 */
rect {
  transition: fill 0.3s ease;
}

rect:hover {
  cursor: pointer;
}

/* 圆角点位的特殊样式 */
g rect {
  stroke: white;
  stroke-width: 1;
  transition: all 0.3s ease;
  will-change: transform;
}

g rect:hover {
  cursor: pointer;
}

/* 控制按钮样式 */
.control-buttons {
  background: "#fff !important";
  display: flex;
  justify-content: end;
  gap: 1rem;
  border: none;
  border-radius: 6px;
  padding-top: 0.8rem;
  position: absolute;
  top: 5.5rem;
  right: 20px;
  font-weight: 500;
  letter-spacing: 0.5px;
  overflow: hidden;
  width: 100%;
  z-index: 1000; /* 提高按钮层级 */
  pointer-events: auto; /* 添加此行 */
  
  // 按钮文字样式
  span {
    color: #fff;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
  &.fullscreen {
    width: auto;
    right: 20px;
    top: 20px;
  }
}

.btnIcon {
  width: 1.2rem; /* 根据需要调整图片大小 */
  height: 1.2rem; /* 根据需要调整图片大小 */
  margin-right: 6px;
}

.control-buttons button {
  padding: 8px 16px;
  white-space: nowrap; /* 防止文本换行 */
  width: fit-content; /* 使按钮宽度自适应内容 */
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: space-between;
  background: linear-gradient(to bottom right, #2d89ff, #0053cd) !important;
  font-size: 16px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
  & span {
    display: flex;
    justify-content: space-between;
  }
}

.control-buttons button:hover {
  background-color: #357abd;
}

/* 不规则矩形样式 */
.irregular-rect {
  fill: rgb(99, 99, 99);
  stroke: rgb(99, 99, 99);
  stroke-width: 2;
  stroke-dasharray: 16, 5; /* 创建短划线效果 */
  stroke-linecap: round; /* 使短划线末端变圆 */
}

.irregular-rect1 {
  stroke: rgb(255, 255, 255);
  stroke-width: 3;
  stroke-linecap: round; /* 使短划线末端变圆 */
}

.center-text {
  width: 25px;
  height: 25px;
  font-weight: bold;
  fill: white;
  font-size: 1.3rem;
  text-align: center;
  dominant-baseline: middle; /* 垂直居中 */
}

/* 暂停播放组样式 */
.pause-play-group:hover {
  /* 移除缩放效果 */
  transform: scale(1.1);
}

.control-rect {
  /* 检查并调整 transition 属性 */
  transition: fill 0.3s ease;
}

/* SVG 图片样式 */
.svgPic {
  position: absolute;
  width: 150%;
  overflow: hidden;
  height: 60vh;
  top: 10rem;
  user-select: none;
  pointer-events: auto;
  transform-origin: center center;
}

.svgPic svg {
  pointer-events: auto;
  width: 150%;
  height: 100%;
  transform-origin: center center;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000;
  will-change: transform;
}

/* 组样式 */
#outerGroup,
#innerGroup {
  pointer-events: auto;
  visibility: visible;
}

/* 点样式 */
.dot-red {
  transition: all 0.3s ease;
  pointer-events: auto;
}

/* 路径样式 */
#outerPath,
#innerPath {
  stroke-width: 1;
  visibility: visible;
}

/* 按钮样式 */
.btnSty {
  position: absolute;
  top: 6.5rem;
  padding: 0 20px;
  display: flex;
  align-items: center;
}

/* 覆盖层样式 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

/* 退出全屏按钮 */
.exit-fullscreen-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  margin: 40px 40px 0px 0px;
  z-index: 1000;
  background: linear-gradient(to bottom right, #2d89ff, #0053cd) !important;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.exit-fullscreen-btn:hover {
  background-color: #357abd;
}

/* 单元格样式 */
.outer-cell,
.inner-cell {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  will-change: transform;
}

/* 矩形样式 */
.outer-rect {
  fill: #0370fa;
  opacity: 0.8;
  filter: drop-shadow(0 0 2px rgba(3, 112, 250, 0.5));
}

.inner-rect {
  fill: #0370fa;
  opacity: 0.8;
  filter: drop-shadow(0 0 2px rgba(3, 112, 250, 0.5));
}

/* 播放按钮 */
.play-button {
  position: fixed;
  top: 20px;
  left: 20px;
  padding: 10px 20px;
  background-color: #0370fa;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  z-index: 1000;
}

.play-button:hover {
  background-color: #0256c4;
} 