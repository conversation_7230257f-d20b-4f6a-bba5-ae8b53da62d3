<template>
  <div style="padding: 5px">
    <el-form :model="form">
      <el-form-item style="margin: 15px 0 15px 0">
        <!-- <span>
          {{ $t('common.Date') }}:
          <el-date-picker v-model="form.start" :clearable="false" type="datetime" :placeholder="$t('page.usermanagement.StartDate')"
            value-format="yyyy-MM-dd HH:mm:ss" style="width: 185px" />
          -
          <el-date-picker v-model="form.end" :clearable="false" type="datetime" :placeholder="$t('page.usermanagement.EndDate')"
            value-format="yyyy-MM-dd HH:mm:ss" style="width: 185px" />
        </span> -->

        <span>
          {{ $t("common.Date") }}:
          <el-date-picker
            v-model="form.start"
            type="datetime"
            :picker-options="minPickerOptions"
            @change="handleMinChange"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 185px"
            :placeholder="$t('page.usermanagement.StartDate')"
          ></el-date-picker>
          -
          <el-date-picker
            v-model="form.end"
            type="datetime"
            :picker-options="maxPickerOptions"
            @change="handleMaxChange"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 185px"
            :placeholder="$t('page.usermanagement.EndDate')"
          ></el-date-picker>
        </span>

        <!-- <span>
          {{ $t('common.Date') }}:
          <el-date-picker
          v-model="dateRange"
          style="width: 350px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          :start-placeholder="$t('page.usermanagement.StartDate')"
          :end-placeholder="$t('page.usermanagement.endDate')"
          :picker-options="{
                  disabledDate: (date) => {
                    if (date.getDay() === 0 || date.getDay() === 6) {
                      return true;
                    } else {
                      return false;
                    }
                  },
                }"
        ></el-date-picker>
        </span> -->
        <span style="margin: 0 0 0 20px">
          {{ $t("common.Chute") }}:
          <el-input
            v-model="form.chuteNo"
            type="number"
            :min="0"
            :max="999"
            style="width: 136px"
          />
        </span>

        <!-- <span style="margin: 0 0 0 20px">
          上车时间:
          <el-input v-model="form.carTime" style="width: 136px" />
        </span>  -->

        <span style="margin: 0 0 0 20px" v-if="isWcs">
          {{ $t("common.Supply") }}:
          <el-input
            v-model="form.supNo"
            style="width: 136px"
            type="number"
            :min="0"
            :max="99"
          />
        </span>

        <span style="margin: 0 0 0 20px" v-if="!isWcs">
          {{ $t("common.DwsNo") }}:
          <el-select v-model="form.dwsNo" style="width: 120px">
            <el-option
              v-for="dict in dict.type.dwsNumber"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </span>

        <span style="margin: 0 0 0 20px">
          {{ $t("common.ExceptionCode") }}:
          <el-select v-model="form.exceptionCode" style="width: 120px">
            <el-option
              v-for="dict in dict.type.sortingExceptionCode"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
          <!-- <el-input v-model="form.exceptionCode" style="width: 136px" type="number" /> -->
        </span>
        <span style="margin: 0 0 0 20px">
          {{ $t("common.Code") }}:
          <el-input
            v-model="barCode"
            style="width: 136px"
            @keydown.enter.native="onBarCodeEnter"
            @blur="onBarcodeFocus"
          />
        </span>
        <span style="margin: 0 0 0 20px" v-if="isAdmin">
          {{ $t("common.turns") }}:
          <el-input v-model="form.turns" style="width: 136px" type="number" />
        </span>
      </el-form-item>

      <el-form-item style="margin: 15px 0 15px 0">
        <span style="margin: 0 0 0 20px" v-if="isWcs">
          {{ $t("common.PacketNumber") }}:
          <el-input v-model="form.packageNumber" style="width: 136px" />
        </span>
        <span style="margin: 0 0 0 20px" v-if="isWcs">
          {{ $t("common.CarNumber") }}:
          <el-input
            v-model="form.carNumber"
            type="number"
            :min="0"
            :max="999"
            style="width: 136px"
          />
        </span>

        <span style="margin: 0 0 0 20px" v-if="isAdmin">
          {{ $t("common.TimeType") }}:
          <el-select v-model="businessTimeType" style="width: 120px">
            <el-option
              v-for="dict in dict.type.BusinessTimeTypeWeb"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </span>

        <span style="margin: 0 0 0 20px">
          {{ $t("common.SystemType") }}:
          <el-select
            v-model="systemTypeCode"
            style="width: 120px"
            @change="handleOptionChange"
          >
            <el-option
              v-for="item in systemOptions"
              :key="item.dbCode"
              :label="item.dbName"
              :value="item.dbCode"
            />
          </el-select>
        </span>

        <span style="margin: 0 0 0 20px">
          <el-button-group>
            <el-button
              style="margin-right: 15px"
              icon="el-icon-search"
              @click="setloadData"
              type="primary"
              >{{ $t("common.Select") }}</el-button
            >
            <el-button
              style="margin-right: 15px"
              icon="el-icon-refresh-left"
              @click="resetForm"
              type="warning"
              >{{ $t("common.Reset") }}</el-button
            >
            <el-button icon="el-icon--download" @click="exportDataEvent">{{
              $t("common.Export")
            }}</el-button>
          </el-button-group>
        </span>
      </el-form-item>

      <!-- <el-form-item style="margin: 0 0 5px 0"> -->
      <!-- <span>
          {{ $t('common.Code') }}:
          <el-input
            v-model="barCode"
            style="width: 136px"
            @keydown.enter.native="onBarCodeEnter"
            @blur="onBarcodeFocus"
          />
        </span> -->

      <!-- <span style="margin: 0 0 0 20px">
          {{ $t('common.PacketNumber') }}:
          <el-input v-model="form.packageNumber" style="width: 136px" />
        </span> -->

      <!-- <span style="margin: 0 0 0 20px">
          工号:
          <el-input v-model="form.userCode" style="width: 136px" />
        </span> -->
      <!--
        <span style="margin: 0 0 0 20px">
          消息类型:
          <el-select v-model="form.step" style="width: 100px">
            <el-option
              v-for="item in stepOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </span> -->

      <!-- <span style="margin: 0 0 0 20px">
          <el-button-group>
            <el-button
              style="margin-right: 15px"
              icon="el-icon-search"
              @click="loadData"
            >{{ $t('common.Select') }}</el-button>
            <el-button
              style="margin-right: 15px"
              icon="el-icon-refresh-left"
              @click="resetForm"
            >{{ $t('common.Reset') }}</el-button>
            <el-button
              icon="el-icon--download"
              @click="exportDataEvent"
            >导出数据</el-button>
          </el-button-group>
        </span> -->
      <!-- </el-form-item> -->
    </el-form>
    <ul class="barCodeList">
      <li v-for="(item, index) in form.barCode" :key="index">
        <span>{{ item }}</span>
        <ul>
          <li class="el-icon-close" @click="onDelBarCode(index)" />
        </ul>
      </li>
    </ul>
    <!-- <vxe-toolbar>
      <template v-slot:buttons>
        <vxe-button icon="vxe-icon--download" @click="exportDataEvent">导出数据</vxe-button>
      </template>
    </vxe-toolbar> -->

    <vxe-table
      v-if="isWcs"
      ref="xTable"
      resizable
      show-overflow
      :height="tableHeight"
      :row-config="{ height: 130, width: 50 }"
      :loading="loading"
      :data="tableData"
      :border="true"
    >
      <vxe-table-column
        :resizable="true"
        type="seq"
        :title="$t('common.SerialNumber')"
        width="10%"
      />
      <!-- <vxe-table-column :resizable="true" field="businessDate" :title="$t('common.Date')" /> -->
      <vxe-table-column
        :resizable="true"
        field="scanTime"
        :title="$t('common.ScanTime')"
        width="10%"
      />
      <vxe-table-column
        :resizable="true"
        field="carTime"
        :title="$t('common.BoardingTime')"
        width="10%"
      />
      <!-- <vxe-table-column :resizable="true" field="chuteTime" title="落格时间" width="10%" /> -->
      <vxe-table-column
        field="chuteTime"
        :title="$t('common.DropTime')"
        :resizable="true"
        width="14%"
        :edit-render="{}"
      >
        <!-- <template #default="{ row }">
          <span>{{ row.chuteTime }}</span>
        </template> -->
        <template #default="{ row }">
          <vxe-select v-if="row.chuteTimeList" v-model="row.chuteTime" transfer>
            <vxe-option
              v-for="item in row.chuteTimeList2"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </vxe-select>
          <!-- <vxe-input v-else v-model="row.chuteTime"/> -->
        </template>
      </vxe-table-column>

      <vxe-table-column
        :resizable="true"
        field="upLoadTime"
        :title="$t('common.NextPieceTime')"
        width="10%"
      />
      <vxe-table-column
        :resizable="true"
        field="barCode"
        :title="$t('common.Code')"
        width="10%"
      />
      <vxe-table-column
        :resizable="true"
        field="supNo"
        :title="$t('common.Supply')"
        width="4%"
      />
      <vxe-table-column
        :resizable="true"
        field="packageNumber"
        :title="$t('common.PacketNumber')"
        width="10%"
      />
      <vxe-column
        :title="$t('common.Image')"
        :resizable="true"
        align="center"
        width="8%"
      >
        <template v-slot="{ row }">
          <el-row>
            <el-button @click="showPictureHandler(row)">View</el-button>
          </el-row>
        </template>
      </vxe-column>

      <vxe-table-column
        :resizable="true"
        field="weight"
        :title="$t('common.Weight')"
        width="6%"
      />
      <vxe-table-column
        :resizable="true"
        field="startChuteNo"
        :title="$t('common.Chute')"
        width="4%"
      />
      <vxe-table-column
        v-if="isFfsSystem"
        :resizable="true"
        field="circleLevel"
        title="内外圈"
        width="6%"
      >
        <template v-slot="{ row }">
          <span>{{ row.circleLevel == "1" ? '内圈' : '外圈' }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column
        :resizable="true"
        field="carNo"
        :title="$t('common.CarNumber')"
        width="4%"
      />
      <vxe-table-column
        :resizable="true"
        field="chuteNos"
        :title="$t('common.RequestedGate')"
        width="8%"
      />
      <vxe-table-column
        :resizable="true"
        field="chuteNo"
        :title="$t('common.PhysicalGrid')"
        width="8%"
      />

      <vxe-table-column
        :resizable="true"
        field="planFlag"
        :title="$t('common.PlanFlag')"
        width="8%"
      />
      <vxe-table-column
        :resizable="true"
        field="terminalDispatchCode"
        :title="$t('common.TerminalDispatchCode')"
        width="10%"
      />
      <!-- <vxe-table-column :resizable="true" field="message" title="消息" /> -->
      <!-- <vxe-table-column :resizable="true" field="userCode" title="工号" /> -->
      <vxe-table-column
        :resizable="true"
        field="elapsedTime"
        :title="$t('common.FallingGridTime')"
        width="10%"
      />
      <vxe-table-column
        :resizable="true"
        field="exceptionCode"
        :title="$t('common.ExceptionCode')"
        width="10%"
      />
      <vxe-table-column
        :resizable="true"
        field="extraData"
        :title="$t('common.ExtraData')"
        width="10%"
      />
      <vxe-table-column
        v-if="isAdmin"
        :resizable="true"
        field="turns"
        :title="$t('common.turns')"
        width="10%"
      />
    </vxe-table>

    <vxe-table
      v-if="!isWcs"
      ref="xTable"
      :height="tableHeight"
      :row-config="{ height: 130, width: 50 }"
      :loading="loading"
      :data="tableDataDws"
      border
      resizable
      show-overflow
    >
      <vxe-table-column
        v-for="column in filteredColumns"
        :key="column.field"
        :resizable="column.resizable !== false"
        :field="column.field"
        :title="column.title.startsWith('common.') ? $t(column.title) : column.title"
        :width="column.width"
      >
        <template v-slot="{ row }" v-if="column.type === 'image'">
          <el-row>
            <el-button @click="showPictureHandler(row)">View</el-button>
          </el-row>
        </template>
        <template v-slot="{ row }" v-if="column.field === 'circleLevel'">
          <span>{{ row.circleLevel == "1" ? '内圈' : '外圈' }}</span>
        </template>
      </vxe-table-column>
      <!-- <vxe-column  
      :title="$t('common.Image')"  
      align="center"  
      width="8%"  
      resizable  
    >  
      <template v-slot="{ row }">  
        <el-row>  
          <el-button @click="showPictureHandler(row)">View</el-button>  
        </el-row>  
      </template>  
    </vxe-column>   -->
    </vxe-table>

    <el-dialog
      :title="$t('common.Image')"
      :visible.sync="showPicture"
      width="50%"
      center
    >
      <div>
        <el-image :preview-src-list="previewSrcList" :src="openImage" />
      </div>
      <!-- <span>需要注意的是内容是默认不居中的</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">{{ $t('common.Cancellation') }}</el-button>
        <el-button type="primary" @click="centerDialogVisible = false">{{ $t('common.Determine') }}</el-button>
      </span> -->
    </el-dialog>
    <vxe-pager
      background
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.totalResult"
      :layouts="[
        'PrevPage',
        'JumpNumber',
        'NextPage',
        'FullJump',
        'Sizes',
        'Total',
      ]"
      @page-change="handlePageChange"
    />
  </div>
</template>

<script>
import request from "@/utils/request";
import moment from "moment";
import Cookies from "js-cookie";

export default {
  name: "SortingLog",
  dicts: ["BusinessTimeTypeWeb", "sortingExceptionCode", "dwsNumber"],
  data() {
    return {
      showPicture: false,
      openImage: "",
      previewSrcList: [],
      // stepOptions: [
      //   { value: "", label: "全部" },
      //   { value: "1", label: "扫描条码" },
      //   { value: "2", label: "包裹上车" },
      //   { value: "3", label: "落格反馈" },
      // ],
      barCode: '',
      form: {
        start: this.getDate(0),
        end: this.getDate(1),
        // step: "",
        barCode: [],
      },
      // 日期范围
      dateRange: [],
      tableHeight: window.innerHeight - 245,
      loading: false,
      tableData: [],
      tableDataDws: [],
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0,
      },
      minPickerOptions: {},
      maxPickerOptions: {
        disabledDate: (time) => {
          // 最大时间不能选择比最小时间早五天以上的时间
          if (this.form.start) {
            const minTimestamp = new Date(this.form.start).getTime();
            const maxTimestamp = time.getTime();
            return (
              maxTimestamp < minTimestamp - 1000 ||
              maxTimestamp > minTimestamp + 5 * 24 * 60 * 60 * 1000 - 1000
            );
          }
          return false;
        },
      },
      systemOptions: this.getBoxValue(),
      systemTypeCode: "1",
      //businessTimeTypeOptions:this.getBusinessTimeTypeValue(),
      businessTimeType: "",
      isWcs: true,
      isAdmin: false,
      columns: [
        { 
          type: 'seq',  // 改为使用内置序号类型
          title: 'common.SerialNumber', 
          width: '10%',
          seqMethod: this.seqMethod  // 添加序号计算方法
        },
        { field: "scanTime", title: "common.ScanTime", width: "10%" },
        { field: "passBackTime", title: "common.passBackTime", width: "10%" },
        { field: "arrivalTime", title: "common.arrivalTime", width: "10%" },
        { field: "barCode", title: "common.Code", width: "10%" },
        { field: "dwsNo", title: "common.DwsNo", width: "7%" },
        { field: "weight", title: "common.Weight", width: "6%" },
        { field: 'image', title: 'common.Image', width: '8%', type: 'image' },
        { field: "length", title: "common.length", width: "6%" },
        { field: "width", title: "common.width", width: "6%" },
        { field: "heigth", title: "common.heigth", width: "6%" }, // Fixed spelling error
        { field: "startChuteNo", title: "common.Chute", width: "4%" },
        { field: "circleLevel", title: "内外圈", width: "6%", vIf: "isFfsSystem" },
        { field: "chuteNos", title: "common.RequestedGate", width: "8%" },
        { field: "chuteNo", title: "common.PhysicalGrid", width: "8%" },
        { field: "planFlag", title: "common.PlanFlag", width: "8%" },
        { field: "taskNo", title: "common.taskNo", width: "8%" },
        {
          field: "terminalDispatchCode",
          title: "common.TerminalDispatchCode",
          width: "10%",
        },
        { field: "elapsedTime", title: "common.FallingGridTime", width: "10%" },
        { field: "exceptionCode", title: "common.ExceptionCode", width: "10%" },
        { field: "extraData", title: "common.ExtraData", width: "10%" },
        // { field: 'turns', title: 'common.turns', width: '10%', resizable: true, v-if="isAdmin" } // Uncomment if needed
      ],
    };
  },
  computed: {
    isFfsSystem() {
      if (this.systemOptions && this.systemTypeCode) {
        const systemOption = this.systemOptions.find(option => option.dbCode === this.systemTypeCode);
        return systemOption && systemOption.dbType === 'ffs';
      }
      return false;
    },
    filteredColumns() {
      return this.columns.filter(column => {
        if (column.vIf === 'isFfsSystem') {
          return this.isFfsSystem;
        }
        return true;
      });
    }
  },
  methods: {
    handleMinChange() {
      // 最小时间改变后，需要更新最大时间的可选范围
      if (this.form.start && this.form.end) {
        const minTimestamp = new Date(this.form.start).getTime();
        const maxTimestamp = new Date(this.form.end).getTime();
        if (
          maxTimestamp < minTimestamp ||
          maxTimestamp > minTimestamp + 5 * 24 * 60 * 60 * 1000
        ) {
          // 最大时间不符合要求，修改为符合要求的时间
          this.form.end = moment(
            new Date(
              Math.min(
                minTimestamp + 5 * 24 * 60 * 60 * 1000 - 1000,
                Date.now()
              )
            )
          ).format("YYYY-MM-DD HH:mm:ss");
        }
      }
    },
    handleMaxChange() {
      // 最大时间改变后，需要更新最小时间的可选范围
      if (this.form.start && this.form.end) {
        const minTimestamp = new Date(this.form.start).getTime();
        const maxTimestamp = new Date(this.form.end).getTime();
        if (
          maxTimestamp < minTimestamp ||
          maxTimestamp > minTimestamp + 5 * 24 * 60 * 60 * 1000
        ) {
          // 最大时间不符合要求，修改为符合要求的时间
          //this.form.end = moment(new Date(Math.min(minTimestamp + 5 * 24 * 60 * 60 * 1000, Date.now()))).format("YYYY-MM-DD hh:mm:ss");
        }
      }
    },
    showPictureHandler(row) {
      this.showPicture = true;
      this.previewSrcList = [];
      this.openImage = row.imageUrl;
      this.previewSrcList.push(row.imageUrl);
    },
    handleOptionChange() {
      let typeCode = this.systemTypeCode;
      let type = this.systemOptions.find(
        (option) => option.dbCode === typeCode
      ).dbType;
      if (type == "dws") {
        this.isWcs = false;
      } else {
        this.isWcs = true;
      }
      this.tableData = [];
      this.tableDataDws = [];
      // this.loadData()
    },
    getDate(format) {
      var today = new Date();
      var year = today.getFullYear();
      var month = today.getMonth() + 1;
      var date = today.getDate();

      if (format === 0) {
        return year + "-" + month + "-" + date + " 00:00:00";
      } else if (format === 1) {
        return year + "-" + month + "-" + date + " 23:59:59";
      }
    },
    loadData() {
      this.loading = true;
      // if(this.dateRange){
      // this.form.start = this.dateRange[0]
      // this.form.end = this.dateRange[1]
      // }else{
      // this.form.start = ''
      // this.form.end = ''
      // }
      let typeCode = this.systemTypeCode;
      let type = this.systemOptions.find(
        (option) => option.dbCode === typeCode
      ).dbType;

      let url = "/log/assembly/sorting/list";
      if (type == "dws") {
        url = "/dws/log/assembly/sorting/list";
      }
      if (type == "ffs") {
        url = "/ffs/log/assembly/sorting/list";
      }
      console.log('测试数据',type)
      if (this.form.start && this.form.end) {
        return request({
          url: url,
          method: "post",
          data: {
            turns: this.form.turns,
            chuteNo: this.form.chuteNo,
            carTime: this.form.carTime,
            supNo: this.form.supNo,
            exceptionCode: this.form.exceptionCode,
            startTime: this.form.start,
            endTime: this.form.end,
            barcodeList: this.form.barCode,
            packageNumber: this.form.packageNumber,
            carNo: this.form.carNumber,
            pageSize: this.tablePage.pageSize,
            curPage: this.tablePage.currentPage,
            dbCode: typeCode,
            businessTimeType: this.businessTimeType,
            dwsNo: this.form.dwsNo,
          },
        })
          .then((resp) => {
            if (typeof resp.data.result.records !== "undefined") {
              resp.data.result.records.forEach((element) => {
                if (element.planFlag === "0") element.planFlag = "综合";
                else if (element.planFlag === "1") element.planFlag = "出港";
                else if (element.planFlag === "2") element.planFlag = "进港";
                if (
                  element.chuteTimeList !== undefined &&
                  element.chuteTimeList !== "" &&
                  element.chuteTimeList !== null
                ) {
                  element.chuteTimeList2 = element.chuteTimeList.map((e) => {
                    return { label: e, value: e };
                  });
                }
                element.dwsNo = this.selectDictLabel(
                  this.dict.type.dwsNumber,
                  element.dwsNo
                );
                element.exceptionCode = this.selectDictLabel(
                  this.dict.type.sortingExceptionCode,
                  element.exceptionCode
                );
              });
            }
            if (this.isWcs) {
              this.tableData = resp.data.result.records;
            } else {
              this.tableDataDws = resp.data.result.records;
            }
            //this.tablePage.currentPage = resp.data.result.current
            this.tablePage.pageSize = resp.data.result.size;
            this.tablePage.totalResult = resp.data.result.total;
            this.loading = false;
          })
          .catch((error) => {
            this.loading = false;
            this.$message({
              message: error || "加载失败！",
              type: "error",
              duration: 2 * 1000,
            });
          });
      } else {
        this.loading = false;
        this.$message({
          message: "时间不能为空！",
          type: "error",
          duration: 2 * 1000,
        });
      }
    },
    setloadData() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage;
      this.tablePage.pageSize = pageSize;
      this.loadData();
    },
    resetForm() {
      this.form = {
        start: this.getDate(0),
        end: this.getDate(1),
        // step: "",
        barCode: [],
      };
      this.tableData = [];
      this.tablePage = {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0,
      };
    },
    exportDataEvent() {
      this.loading = true;
      // if(this.dateRange){
      // this.form.start = this.dateRange[0]
      // this.form.end = this.dateRange[1]
      // }else{
      // this.form.start = ''
      // this.form.end = ''
      // }
      let typeCode = this.systemTypeCode;
      let type = this.systemOptions.find(
        (option) => option.dbCode === typeCode
      ).dbType;
      

      let url = "/log/assembly/sorting/export";
      if (type == "dws") {
        url = "/dws/log/assembly/sorting/export";
      }
      if (type == "ffs") {
        url = "/ffs/log/assembly/sorting/export";
      }
      if (this.form.start && this.form.end) {
        return request({
          url: url,
          method: "post",
          data: {
            turns: this.form.turns,
            chuteNo: this.form.chuteNo,
            carTime: this.form.carTime,
            supNo: this.form.supNo,
            exceptionCode: this.form.exceptionCode,
            startTime: this.form.start,
            endTime: this.form.end,
            barcodeList: this.form.barCode,
            packageNumber: this.form.packageNumber,
            carNo: this.form.carNumber,
            dbCode: typeCode,
            businessTimeType: this.businessTimeType,
          },
          responseType: "blob",
          timeout: 200000,
        })
          .then((resp) => {
            const blob = new Blob([resp], { type: "application/vnd.ms-excel" });
            const a = document.createElement("a");
            a.href = URL.createObjectURL(blob);
            a.download = "分拣日志.xlsx";
            a.style.display = "none";
            document.body.appendChild(a);
            a.click();
            URL.revokeObjectURL(a.href);
            document.body.removeChild(a);
            this.$message({
              message: this.$t("common.BeginExport"),
              type: "success",
              duration: 2 * 1000,
            });
            //   this.$refs.xTable.exportData({
            //     filename: '分拣日志',
            //     // sheetName: "Sheet1",
            //     type: 'csv',
            //     data: resp.data.result
            //   })
            this.loading = false;
          })
          .catch((error) => {
            this.loading = false;
            this.$message({
              message: error || "导出失败！",
              type: "error",
              duration: 2 * 1000,
            });
          });
      } else {
        this.loading = false;
        this.$message({
          message: "时间不能为空！",
          type: "error",
          duration: 2 * 1000,
        });
      }
    },
    // 输入条码时按下回车(国内)
    onBarCodeEnter() {
      if (!this.barCode) return
      let codeList = this.barCode.split(' ');
      codeList.forEach((element) => {
        let data = element.replace(/ /g, '')

        const regstr = /^\d{13}$/;
        if (regstr.test(data)) // 校验格式
        {
          data = 'JT' + data
        }

        const reg = /^JT\d{13}$/; // 正则表达式
        if (reg.test(data)) // 校验格式
        {
          this.form.barCode.push(data)
        }
      })
      this.barCode = ''
    },
    // 输入条码时按下回车(国外)
    // onBarCodeEnter() {
    //   if (!this.barCode) return;
    //   let codeList = this.barCode.split(" ");
    //   codeList.forEach((element) => {
    //     let data = element.replace(/ /g, "");

    //     const regstr = /^\d{12}$/;
    //     const regstr1 = /^[A-Z]\d{9}$/;
    //     if (regstr.test(data) || regstr1.test(data)) {
    //       // 校验格式
    //       this.form.barCode.push(data);
    //     }
    //   });
    //   this.barCode = "";
    // },
    // 删除条码值
    onDelBarCode(index) {
      this.form.barCode.splice(index, 1);
    },
    // 光标离开输入框
    onBarcodeFocus() {
      this.onBarCodeEnter();
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params:{paramName: 'ScadaConnInfo'}
      }).then((res) => {
        console.log(res,'测试')
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    },
    // getBusinessTimeTypeValue() {
    //   return request({
    //     url: '/obtain/db/getParamValueByName',
    //     method: 'get',
    //     params:{paramName: 'BusinessTimeTypeWeb'}
    //   }).then((response) => {
    //     this.businessTimeTypeOptions = JSON.parse(response.msg)
    //     if (Cookies.get('account') == 'admin'){
    //      this.isAdmin = true
    //     }else(
    //       this.isAdmin = false
    //     )
    //   });
    // },
    seqMethod({ row, rowIndex }) {
      return (this.tablePage.currentPage - 1) * this.tablePage.pageSize + rowIndex + 1
    },
  },
};
</script>
<style scoped lang="scss">
.barCodeList {
  display: flex;
  flex-wrap: wrap;
  list-style: none;

  li {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
    margin: 2px 6px;
    padding: 1px 8px;
    border-radius: 4px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: space-between;

    span {
      margin-right: 10px;
    }

    .el-icon-close {
      transform: scale(0.8);
      background-color: #c0c4cc;
      border-radius: 50%;
      padding: 2px;
      cursor: pointer;
    }
  }
}
</style>
