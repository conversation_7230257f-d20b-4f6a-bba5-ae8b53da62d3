// 马来语
const ms = {
  login: {
    changelanguage: "Pertukaran Bahasa",
    index: "I'm six",
    title: "Sistem Pengasingan COGY",
    Success: "Log Masuk Berjaya",
    plsuaername: "Sila Masukkan Nama Pengguna",
    plspassword: "Sila Masukkan Kata Laluan",
    plscode: "Sila Masukkan Kata Laluan Kod Pengesahan",
    login: "Log Masuk",
    code: "Kod",
       chinese: "中文",
    english: "english",
    thai: "ภาษาไทย",
    ms: "Bahasa Melayu",
    vi: "Tiếng Việt",
    es: "Español",
    pt: "Bahasa Portugis",
    id: "Bahasa Indonesia",
    ph: "Filipino",
    SwipeRight: "Leret Kanan Ke",
  },
  welcome: {
    title: "Selamat Datang Ke Sistem Pertanyaan Log Pengasingan",
  },
  headers: {
    title: "Sistem Pencarian Data Pengasingan",
    loginout: "<PERSON><PERSON><PERSON> Dar<PERSON> Sistem",
  },
  sidebar: {
    Home: "<PERSON><PERSON>",
    Syscfg: "Sistem Konfigurasi",
    Userinfosup: "Pengurusan Penggguna Mesin Feeding",
    User: "Pengurusan Pengguna",
    Role: "Pengurusan Peranan",
    Menu: "Pengurusan Menu",
    Sysparam: "Sistem Parameter",
    Errchute: "Port Tidak Normal",
    Sysurl: "Bilangan Hari Untuk Menyimpan Log",
    Sortingplan: "Pelan Pengasingan",
    Datamgr: "Pengurusan Data",
    Runninglog: "Log Operasi",
    Locklog: "Kunci Log",
    Sortinglog: "Log Pengasingan",
    Packagenumberlog: "Sejarah Nombor Pakej",
    Sortingrepush: "Data Re-push",
    Sortingreport: "Laporan Statistik",
    Chutereport: "Kuantiti Pengasingan Port",
    Sortingcountsup: "Statistik Kuantiti (Mesin Feeder)",
    Sortingcountuser: "Statistik Kuantiti (ID Pekerja)",
    Packagenumbercount: "Statistik Jumlah Pakej",
    Sortingefficiency: "Statistik Efisiensi",
    Errorreport: "Statistik Jenis",
    Monitor: "Pemantauan Sistem",
    Scada: "Pemantauan SCADA",
    Job: "Tugas Yang Dijadualkan",
    Config: "Tetapan Parameter",
    Dict: "Pengurusan Kamus",
  },
  page: {
    homepage: {
      boardingcount: "Bilangan Cart",
      scancount: "Kiraan Imbasan",
      cellcount: "Bilangan Jatuh Port",
      cancelleditems: "Pembatalan Item",
      rabbitinterceptions: "Jumlah Intercept J&T",
      unmatchedcodes: "Kod Tiga Segmen Tidak Sepadan dengan Port",
      overbounds: "Jumlah Pusingan Terlebih",
      anomalies: "Jumlah Tidak Normal",
      sortingvolume: "Bilangan Pengasingan",
      monday: "Isnin",
      tuesday: "Selasa",
      wednesday: "Rabu",
      thursday: "Khamis",
      friday: "Jumaat",
      saturday: "Sabtu",
      sunday: "Ahad",
    },
    Serviceusermanagement: {
      Refresh: "Penyegaran Semula Halaman",
      AddUser: "Tambah Pengguna Baharu",
      BulkDelete: "Padam Berkelompok",
      FullName: "Nama Penuh",
      EmployeeID: "ID Pekerja",
      Query: "Carian",
      Import: "Import",
      OnlyExcel: "(Hanya Boleh Import Dokumen Berformat excel.xlsx)",
      SerialNumber: "Nombor Siri",
      UserEmployeeID: "ID Pekerja Pengguna",
      UserName: "Nama Pengguna",
      UserID: "ID Pengguna",
      Creator: "Pencipta",
      CreationTime: "Waktu Penciptaan",
      Operation: "Operasi",
      UserPassword: "Kata Laluan Pengguna",
      ConfirmPassword: "Sila Sahkan Kata Laluan",
      NewPassword: "Kata Laluan Baru",
      Save: "Simpan",
      Cancel: "Batal",
      UpdateUser: "Pengubahsuaian Pengguna",
    },
    usermanagement: {
      UserID: "ID Pengguna",
      UserName: "Nama Pengguna",
      UserAccount: "Akaun Pengguna",
      PlaseUserAccount: "Sila Masukkan Akaun Pengguna",
      PhoneNumber: "Nombor Telefon",
      PleasePhoneNumber: "Sila Masukkan Nombor Telefon",
      Status: "Status",
      PleaseStatus: "Sila Masukkan Status",
      StartDate: "Tarikh Bermula",
      EndDate: "Tarikh Tamat",
      CreationTime: "Waktu Penciptaan",
      Operation: "Operasi",
      UserGender: "Jantina Pengguna",
      UserNickname: "Nama Samaran Pengguna",
      Email: "Emel",
      UserPassword: "Kata Laluan Pengguna",
      Active: "Normal",
      Inactive: "Nyahaktif",
      Role: "Peranan",
      Note: "Perhatian",
      Search: "Carian",
      Reset: "Set Semula",
      Add: "Tambah",
      Delete: "Padam",
      Update: "Kemaskini",
      More: "Lanjut",
      ResetPassword: "Set Semula Kata Laluan",
    },
  },
  common: {
    DerivedData: "Eksport Data",
    ViewDWSPeakEffect: "Semak Puncak Effisiensi DWS",
    peakEffect: "Puncak Kecekapan",
    SearchData: "Data Carian",
    NoData: "Tiada Data",
    Arrival: "Ketibaan",
    Departure: "Perlepasan",
    ArrivalAndDeparture: "Ketibaan Dan Perlepasan",
    CloseOther: "Tutup Yang Lain",
    CloseAll: "Tutup Semua",
    RoleName: "Nama Peranan",
    PlaseRoleName: "Sila Masukkan Nama Peranan",
    PermissionCharacters: "Aksara Kebenaran",
    PlasePermissionCharacters: "Sila Masukkan Aksara Kebenaran",
    Order: "Susunan",
    Status: "Status",
    Search: "Carian",
    Reset: "Set Semula",
    Delete: "Padam",
    Update: "Kemaskini",
    More: "Lanjut",
    CreationTime: "Waktu Penciptaan",
    Operation: "Operasi",
    Update: "Kemaskini",
    Add: "Tambah",
    UpdateCache: "Kemaskini Cache",
    Select: "Pilih",
    Import: "Import",
    Export: "Eksport",
    SerialNumber: "Nombor Siri",
    RoleNumber: "Nombor Peranan",
    Memo: "Memo",
    UpdateDate: "Kemaskini Waktu",
    ParameterName: "Nama Parameter",
    ParameterValue: "Nilai Parameter",
    PleaseSelectARole: "Sila Pilih Peranan",
    PleaseInputnickname: "Sila Masukkan Nama Samaran Pengguna",
    PleaseInputPhoneNumber: "Sila Masukkan Nombor Telefon",
    PleaseInputEmail: "Sila Masukkan Emel",
    PleaseInputUserName: "Sila Masukkan Nama Pengguna",
    PleaseInputPassWord: "Sila Masukkan Kata Laluan",
    RoleStatus: "Status Peranan",
    RoleCode: "Kod Peranan",
    PlaseInputRoleCode: "Sila Masukkan Nombor Pengguna",
    CreatorID: "ID Pencipta",
    Determine: "Setuju",
    Cancellation: "Batal",
    Task: "Nama Tugasan",
    PlaseTask: "Sila Masukkan Nama Tugasan",
    TaskCode: "Kod Tugasan",
    PlaseTaskCode: "Sila Masukkan Kod Tugasan",
    MenuName: "Nama Menu",
    PlaseMenuName: "Sila Masukkan Nama Menu",
    MenuStatus: "Status Menu",
    ExpandAndCollapse: "Pengembangan/Penyusutan",
    SelectAllDonteSelectAll: "Pilih Semua/ Tidak Pilih Semua",
    ParentChildLinkage: "Hubungan Bapa Dan Anak",
    MenuPermissions: "Keizinan Menu",
    PleaseEnterContent: "Sila Masukkan Isi Kandungan",
    Icon: "Ikon",
    ComponentPath: "Laluan Komponen",
    SchemeID: "Skim ID",
    ModeType: "Jenis Pengasingan",
    PlanName: "Nama Pelan",
    PlanCode: "Kod Pelan",
    PlanDesc: "Penerangan Pelan",
    PlanFlag: "Jenis Pelan",
    IsSelected: "Adakah Digunakan",
    UpdateDate: "Pengubahsuaian Waktu",
    Detail: "Butiran",
    Enable: "Boleh Digunakan",
    Date: "Waktu",
    Grade: "Gred",
    Source: "Sumber",
    Message: "Mesej",
    ExtraData: "Data Tambahan",
    plcType: "Lapisan",
    Number: "Nombor Siri",
    Component: "Komponen",
    Chute: "Port",
    Supply: "Mesin Feeder",
    dwsNo: "Nombor DWS",
    BigBarRate: "Kadar Pakej Besar",
    Quantity: "Kuantiti",
    SmallBarRate: "Kadar Pakej Kecil",
    ExceptionCode: "Kod Tidak Normal",
    Code: "Kod",
    ScanTime: "Waktu Imbasan",
    BoardingTime: "Waktu Naik Cart",
    DropTime: "Waktu Jatuh Port",
    NextPieceTime: "Tempoh Ikat Guni",
    passBackTime: "Waktu Pulangan",
    arrivalTime: "Waktu Ketibaan",
    PacketNumber: "Nombor Pakej",
    TimeType: "Jenis Waktu",
    turns: "Pusingan",
    Image: "Imej",
    UpperLevel: "Tingkat Atas",
    LowerLevel: "Tingkat Bawah",
    LayerNumber: "Jumlah Tingkat",
    PleaseEnterTheDictionaryLabel: "Sila masukkan label kamus",
    DictionaryName: "Nama Kamus",
    DictionaryId: "ID Kamus",
    DictionaryType: "Jenis Kamus",
    PleaseEnterTheDictionaryType: "Sila masukkan jenis kamus",
    PleaseEnterTheDictionaryName: "Sila masukkan nama kamus",
    BagBindingBfficer: "Petugas Pengikat Paket",
    DictionaryEncoding: "Pengkodan Kamus",
    DictionaryTag: "Tag Kamus",
    DictionaryValue: "Nilai Kamus",
    DictionarySort: "Penyesuaian Kamus",
    DictionaryRemark: "Keterangan",
    DictionaryCreateTime: "Waktu Pembuatan",
    DataTag: "Tag Data",
    PleaseEnterTheDataLabel: "Sila masukkan label data",
    DataKey: "Kunci Data",
    StyleAttribute: "Sifat Gaya",
    DisplayOrder: "Urutan Paparan",
    EchoStyle: "Gaya Paparan Balik",
    ListClass: "Gaya Senarai",
    PleaseEnterTheDataKey: "Sila masukkan kunci data",
    PleaseEnterTheStyleAttribute: "Sila masukkan sifat gaya",
    PleaseEnterTheDisplayOrder: "Sila masukkan urutan paparan",
    PleaseEnterTheEchoStyle: "Sila masukkan gaya paparan balik",
    PleaseEnterTheListClass: "Sila masukkan gaya senarai",
    PleaseEnterTheRemark: "Sila masukkan keterangan",
    PleaseEnterTheContent: "Sila masukkan kandungan",
    TheAddressNeedsToBehttp:
      "Pilih untuk tautan luar, maka alamat rute harus bermula dengan `http(s)://`",
    TheAddressNeedsToBehttpUser:
      "Alamat rute yang akan diakses, seperti: `user`, jika alamat luar rangkaian perlu diakses secara internal, bermula dengan `http(s)://`",
    TheAddressNeedsToBehttpCatalogue:
      "Laluan komponen yang akan diakses, seperti: `system/user/index`, secara lalai dalam direktori `views`",
    TheDefaultPassingParametersForTheRoute:
      "Parameter laluan laluan yang dihantar secara lalai, seperti: `{'id': 1, 'name': 'ry'}`",
    TheComponentWillBeCachedByKeepAlive:
      "Pilih untuk disimpan dalam cache oleh `keep-alive`, perlu sepadan dengan `name` komponen dan alamat yang konsisten",
    SelectHiddenThenTheRoute:
      "Pilih untuk tersembunyi maka laluan tidak akan muncul di sidebar, tetapi masih boleh diakses",
    SelectDisableThenTheRouteSidebar:
      "Pilih untuk dimatikan maka laluan tidak akan muncul di sidebar, dan tidak boleh diakses",
    PleaseEnterTheRouteParameters: "Sila masukkan parameter laluan",
    Yes: "Ya",
    No: "Tidak",
    PermissionCharactersString: "String kebenaran yang ditentukan dalam controller, contoh: @PreAuthorize(`@ss.hasRole('admin')`)",
    Cache1: "Cache",
    NoCache: "Tanpa Cache",
    AddUser: "Tambah Pengguna",
    BatchCancelAuthorization: "Batalkan Pengesahan Secara Beramai-ramai",
    Close: "Tutup",
    CancelAuthorization: "Batalkan Pengesahan",
    View: "Lihat",
    UserType: "Jenis Pengguna",
    PleaseSelectUserType: "Sila pilih jenis pengguna",
    Forward: "Teruskan",
    Reverse: "Terbalik",
    Lock: "Kunci",
    Unlock: "Buka Kunci",
    SendMessage: "Hantar Mesej",
    TaskGroup: "Nama Kumpulan Tugas",
    PleaseSelectTaskGroup: "Sila pilih nama kumpulan tugas",
    TaskStatus: "Status Tugas",
    PleaseSelectTaskStatus: "Sila pilih status tugas",
    Log: "Log",
    InvokeTarget: "Rentang sasaran pemanggilan",
    CronExpression: "Ungkapan cron",
    ExecuteOnce: "Laksanakan Sekali",
    TaskDetails: "Butiran Tugas",
    DispatchLog: "Log Penghantaran",
    InvokeTarget: "Metode Pemanggilan",
    BeanCallExample: "Contoh pemanggilan Bean: ryTask.ryParams('ry')",
    ClassCallExample:
      "Contoh pemanggilan Kelas: com.ruoyi.quartz.task.RyTask.ryParams('ry')",
    ParameterDescription:
      "Keterangan parameter: sokongan untuk rentetan, jenis boolean, jenis jangka panjang, jenis tumpuan, jenis jangka pendek",
    PleaseInputInvokeTarget: "Sila masukkan rentang sasaran pemanggilan",
    PleaseInputCronExpression: "Sila masukkan ungkapan cron",
    GenerateExpression: "Hasilkan Ungkapan",
    ExecuteStrategy: "Strategi Pelaksanaan",
    MisfirePolicy: "Strategi Pelaksanaan",
    ImmediateExecution: "Laksanakan Segera",
    DelayExecution: "Laksanakan dengan Kelewatan",
    AbandonExecution: "Tinggalkan Pelaksanaan",
    PleaseSelectExecuteStrategy: "Sila pilih strategi pelaksanaan",
    Concurrent: "Adakah Serentak",
    Allow: "Benarkan",
    Prohibit: "Larang",
    PleaseSelectConcurrent: "Sila pilih adakah serentak",
    CronExpressionGenerator: "Pembuat Ungkapan Cron",
    NextExecutionTime: "Waktu Pelaksanaan Seterusnya",
    TaskDetails: "Butiran Tugas",
    TaskGroup1: "Kumpulan Tugas",
    DefaultStrategy: "Strategi Lalai",
    ExecuteStatus: "Status Pelaksanaan",
    PleaseSelectExecuteStatus: "Sila pilih status pelaksanaan",
    ExecutionTime: "Waktu Pelaksanaan",
    PleaseSelectExecutionTime: "Sila pilih waktu pelaksanaan",
    Clear: "Kosongkan",
    JobLogId: "ID Log Tugas",
    JobMessage: "Mesej Log",
    Detail1: "Butiran",
    DispatchLogDetails: "Butiran Log Penghantaran",
    PleaseSelect: "Sila pilih",
    SelectStartTime: "Pilih Waktu Permulaan",
    SelectEndTime: "Pilih Waktu Akhir",
    DebugStart: "Debug (Mula)",
    DebugClose: "Debug (Tutup)",
    ClearPacketNumber: "Kosongkan Nombor Paket",
    Upload: "Muat Naik",
    TotalQuery: "Jumlah Pertanyaan",
    property: "Zhang gambar",
    all: "Semua",
    UnloadingToDeliveryScanning:
      "Pemindaian Pengeluaran dari Kereta Api hingga Penerimaan",
    BuildingPackageScanning: "Pemindaian Pembinaan Paket",

    SelectUser: "Pilih Pengguna",
    InvokeTargetMethod: "Metode Pemanggilan Objek Tujuan",
    PleaseSelectTaskGroup: "Sila pilih kumpulan tugas",
    CronExpression1: "cron expression",
    ExceptionInfo: "Informasi Pengecualian",

    Edit: "Sunting",
    ScopeOfAuthority: "Skop Kewenangan",
    DataPermission: "Kewenangan Data",
    Confirm: "Pengesahan",
    StartDate: "Tarikh Permulaan",
    EndDate: "Tarikh Akhir",
    Weight: "Berat",
    length: "Kepanjangan",
    width: "Lebar",
    heigth: "Ketinggian",
    CarNumber: "Nombor Cart",
    RequestedGate: "Port yang Diminta",
    PhysicalGrid: "Grid Fizikal",
    taskNo: "Nombor Tugasan",
    TerminalDispatchCode: "Kod Tiga Segmen",
    FallingGridTime: "Tempoh Masa Jatuh Port",
    PackageGrade: "Nombor Tag Bag",
    ChipNumber: "Nombor Chip",
    BindingTime: "Tempoh Ikat Guni",
    BindingPersonnel: "Pengikat Guni",
    ScanType: "Jenis Imbasan",
    NextStationNumber: "Nombor Stesen Seterusnya",
    Rfid: "RFID",
    TimeInterval: "Selang Waktu",
    SortingQuantity: "Kuantiti Pengasingan",
    TotalSortingWeight: "Jumlah Berat Pengasingan (KG)",
    NumberOfScannedBarcodeRecords: "Bilangan Rekod Imbas Kod Bar",
    RecordTheNumberOfPackagesLoadedOntoTheVehicle:
      "Rekod Bilangan Pakej Naik Cart",
    NumberOfDropFeedbackRecords: "Bilangan Rekod Maklum Balas Jatuh Port",
    scanQuantity: "Kuantiti Imbasan",
    arrivalQuantity: "Kuantiti Ketibaan",
    passBackQuantity: "Kuantiti Pulangan",
    TotalNumberOfPackages: "Jumlah Pakej",
    Packagenumb: "Nombor Tag Bag (Kuantiti Parcel Dalam Guni Untuk Tempoh Ini)",
    Type: "Jenis",
    Count: "Bilangan",
    SystemType: "Jenis Sistem",
    EmployeeID: "Sila Masukkan ID Pekerja",
    FullName: "Sila Masukkan Nama",
    Password: "Sila Masukkan Kata Laluan",
    ConfirmPassword: "Sila Masukkan Semula Kata Laluan",
    SelectGender: "Pilih Jantina",
    Active: "Normal",
    Inactive: "Nyahaktif",
    Male: "Lelaki",
    Female: "Perempuan",
    Unknown: "Tidak Diketahui",
    RoleOrder: "Susunan Peranan",
    Show: "Tunjuk",
    Hide: "Sembunyi",
    Default: "Asal",
    System: "Sistem",
    Success: "Berjaya",
    Failure: "Tidak Berjaya",
    AddMenu: "Tambah Menu",
    EditMenu: "Penyuntingan Menu",
    ParentMenu: "Menu Atasan",
    MenuType: "Jenis Menu",
    Directory: "Isi Kandungan",
    Menu: "Menu",
    Button: "Butang",
    MenuIcon: "Ikon Menu",
    SelectIcon: "Pilih Ikon",
    RouteAddress: "Alamat Laluan",
    DisplayOrder: "Tunjuk Susunan",
    ExternalLink: "Pautan Luaran",
    DisplayStatus: "Tunjuk Status",
    MenuStatus: "Status Menu",
    RouteParameters: "Parameter Laluan",
    Cache: "Sama Ada Cache",
    ComponentPath: "Laluan Komponen",
    AddRole: "Tambah Peranan",
    EditRole: "Penyuntingan Peranan",
    AddPlan: "Tambah Pelan Baharu",
    Cancel: "Batal",
    FirstSegmentCode: "Kod Segmen Pertama",
    SecondSegmentCode: "Kod Segmen Kedua",
    ThirdSegmentCode: "Kod Segmen Ketiga",
    NextStopCode: "Kod Stesen Seterusnya",
    NextStopName: "Nama Stesen Seterusnya",
    ModificationTime: "Waktu Pengubahsuaian",
    BulkDelete: "Padam Berkelompok",
    AddDetails: "Tambah Butiran",
    ByServicePackage: "Mengikut Mesin Feeder",
    ByDwsNo: "Mengikut Nombor Siri DWS",
    Granularity: "Kebutiran",
    LogicCode: "Kod Logik",
    PacketType: "Jenis Pakej",
    IsUpload: "Adakah Dimuatnaik",
    AddSuccess: "Tambahan Berjaya",
    EditSuccess: "Penyuntingan Berjaya",
    DelSuccess: "Berjaya Dipadam",
    ImportSuccessful: "Import Berjaya",
    BeginExport: "Mula Eksport",
    ModificationFailed: "Pengubahsuaian Tidak Berjaya",
    AddFailed: "Tambahan Tidak Berjaya",
    OperationSuccessful: "Operasi Berjaya",
    OperationFailed: "Operasi Tidak Berjaya",
    OperationCancellation: "Pembatalan Operasi",
    ExportFailed: "Eksport Tidak Berjaya",
    LoginOut:
      "Anda Sudah Dilog Keluar Dan Memilih Untuk Keluar Dari Halaman ini",
    ConfirmLogout: "Sah Untuk Log Keluar",
    LogAgain: "Log Masuk Semula",
    Remark: "Ulasan",
    DwsNo: "Nombor Siri DWS",
    Notempty: "Kata Laluan Atau Kod Pengesahan Tidak Boleh Dikosongkan",
    Notpassword: "Kata Laluan Tidak Boleh Dikosongkan",
    Id: "Kunci Utama Parameter",
    Parameter: "Parameter",
    PlParameter: "Sila Masukkan Nama Parameter",
    ParameterKey: "Kunci Parameter",
    PlParameterKey: "Sila Masukkan Nama Kunci Parameter",
    ParameterValue: "Nilai Kunci Parameter",
    PlParameterValue: "Sila Masukkan Nilai Kunci Parameter",
    Group: "Kumpulan",
    PlGroup: "Sila Pilih Kumpulan",
  },
  scada: {
    DeviceRunningStatus: "Status Operasi Peralatan",
    DeviceStopped: "Peralatan Berhenti Beroperasi",
    DeviceRunning: "Peralatan Sedang Beroperasi",
    StartTime: "Waktu Mula Beroperasi",
    RunningSpeed: "Kelajuan Beroperasi",
    CartOccupancyRate: "Kadar Cart",
    TotalDistanceTraveledByDevice: "Jumlah kilometer Operasi Peralatan",
    DistanceTraveledInCurrentRun: "Bilangan Kilometer Beroperasi Kali Ini",
    TotalDistanceTraveled: "Jumlah kilometer Operasi",
    Scans: "Imbasan",
    PendingStart: "Menunggu Untuk Mula Beroperasi",
    FullScreen: "Skrin Penuh",
    ExitFull: "Keluar Dari Skrin Penuh",
    UpperLevelRunningSpeed: "Kelajuan Operasi Lapisan Atas",
    UpperLevelDeviceRunningStatus: "Status Pengendalian Peralatan Lapisan Atas",
    UpperLevelStartTime: "Waktu Lapisan Atas Mula Beroperasi",
    UpperLevelCartOccupancyRate: "Kadar Naik Cart Lapisan Atas",
    UpperLevelByDevice: "Jumlah Kilometer Operasi Peralatan Lapisan Atas",
    LowerLevelDeviceRunningStatus:
      "Status Pengendalian Peralatan Lapisan Bawah",
    UpperLayerPLCDisconnect: "PLC Lapisan Atas Terputus Secara Tidak Normal",
    LowerLevelPLCDisconnect: "Status Putus Sambungan PLC Lapisan Bawah",
    UpperLayerPLCConnectionStatus: "Status Sambungan PLC Lapisan Atas",
    LowerLevelPLCConnectionStatus: "Status Sambungan PLC Lapisan Bawah",
    LowerLevelStartTime: "Waktu Lapisan Bawah Mula Beroperasi",
    LowerLevelRunningSpeed: "Kelajuan Pengendalian Lapisan Bawah",
    LowerLevelCartOccupancyRate: "Kadar Cart Lapisan Bawah",
    LowerLevelTotalDistanceTraveledByDevice:
      "Jumlah Kilometer Operasi Peralatan Lapisan Bawah",
    UpperLevelScans: "Bilangan Imbasan Lapisan Atas",
    LowerLevelScans: "Bilangan Imbasan Lapisan Bawah",
    AbnormalQuantity: "Kuantiti Tidak Normal",
    NumberOfSlotsOccupied: "Bilangan Jatuh Port",
    FailedRepushQuantity: "Kuantiti Tidak Berjaya",
    InterceptedQuantity: "Kuantiti Intercept",
    ExcessiveCirclesQuantity: "Bilangan Pusingan Terlebih",
    UnconfiguredThreeSegmentCodeSlots:
      "Port Kod Tiga Segmen Tidak Dikonfigurasi",
    ComprehensiveExceptionSlots: "Port Komprehensif",
    CancelledItems: "Pembatalan Item",
    UnobtainedThreeSegmentCodeInformation:
      "Maklumat Kod Tiga Segmen Tidak Diperolehi",
    WebSocketStatus: "Status WebSocket",
    WCSCommunicationStatus: "Status Perhubungan WCS",
    Connected: "Disambung",
    NotConnected: "Tiada Penyambungan",
    SortingStatus: "Status Pengasingan",
    Idle: "Lapang",
    Loaded: "Pengisian",
    CartStatus: "Status Cart",
    LittleThingsAreQuick:'Kecil tangan cepat',
    Headscratcher: 'Pembersihan atas',
    OnLine:'dalam talian',
    Offline:'Luar talian',
    
    Locked: "Dikunci",
    FullPackage: "Penuh",
    SlotStatus: "Status Port",
    InterceptedItem: "Item Intercept",
    ExceptionSlot: "Port Tidak Normal",
    PendingCommunication: "Menunggu Komunikasi",
    Max: "Kitaran Maksimum",
    Cancellation: "Pembatalan",
    UnProgramme: "Tiada Pelan",
    UnThree: "Kod Tiga Segmen Belum Disepadankan",
    CartOperation: "Operasi Cart",
    OneKeyUnlock: "Buka Kunci Semua",
    OneKeyLockSlot: "Kunci Semua",
    CartNumber: "Nombor Cart",
    FloorNumber: "Bilangan Lapisan",
    UpperLevel: "Lapisan Atas",
    LowerLevel: "Lapisan Bawah",
    Lock: "Kunci",
    Unlock: "Buka Kunci",
    ManualSwitchStart: "Penukaran Manual (Buka)",
    ManualSwitchClose: "Penukaran Manual (Tutup)",
    Run: "Beroperasi",
    Close: "Tutup",
    DisabledList: "Senarai Larangan",
    AbnormalAlarm: "Penggera Tidak Normal",
    DisableCart: "Cart Dilarang Guna",
    UpperLevelCart: "Cart Lapisan Atas",
    LowerLevelCart: "Cart Lapisan Bawah",
    VerificationPassword: "Sila Masukkan Kod Pengesahan",
    Confirm: "Pengesahan",
    ClearTheKilometers: "Padam Kilometer",
    PleaseSelectClearKilometers: "Sila Pilih Padamkam Kilometer",
    FaultLevel: "Peringkat Kegagalan",
    StartTime: "Waktu Bermula",
    EndTime: "Waktu Tamat",
    CriticalAlarm: "Penggera Serius",
    GeneralAlarm: "Penggera Am",
    MinorAlarm: "Penggera Minor",
    SearchCriteria: "Kriteria Carian",
    Time: "Waktu",
    Select: "Pilih",
    PleaseEnterContent: "Sila Masukkan Isi Kandungan",
    SerialNumber: "Nombor Siri",
    Operation: "Operasi",
    AlarmHelpLink: "Dokumen Bantuan",
    AlarmType: "Jenis Penggera",
    AlarmSource: "Sumber Penggera",
    Content: "Isi Kandungan",
  },
};
export default ms;
