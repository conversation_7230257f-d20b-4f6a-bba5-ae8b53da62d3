// 菲律宾
const ph = {
  login: {
    changelanguage: "Palitan ang Wika",
    index: "Index",
    title: " COGYsorting query system",
    AddSuccess: "Matagumpay na Idinagdag",
    plsuaername: " Mangyaring ipasok ang username",
    plspassword: " Mangyaring ipasok ang password",
    plscode: " Mangyaring ipasok ang verification code password",
    login: "Mag-login",
    Success: " Matagumpay na pag-login",
    Code: "Verification Code",
      chinese: "中文",
    english: "english",
    thai: "ภาษาไทย",
    ms: "Bahasa Melayu",
    vi: "Tiếng Việt",
    es: "Español",
    pt: "Bahasa Portugis",
    id: "Bahasa Indonesia",
    ph: "Filipino",
    SwipeRight: "Mag-swipe pakanan",
  },
  welcome: {
    title: "Maligayang pagdating sa paggamit ng sorting log query system",
  },
  headers: {
    title: "Sorting data query system",
    loginout: "Lumabas sa Sistema",
  },
  sidebar: {
    Home: "Pangunahing Pahina",
    Syscfg: "Pag-configure ng sistema",
    Userinfosup: "Pamamahala ng gumagamit ng package",
    User: "Pamamahala ng gumagamit",
    Role: "Pamamahala ng papel",
    Menu: "Pamamahala ng Menu",
    Sysparam: "Parameter ng sistema",
    Errchute: "Abnormal na Gripo",
    Sysurl: "Bilang ng araw ng pag-iimbak ng log",
    Sortingplan: "Plano ng pag-uuri",
    Datamgr: "Pamamahala ng Data",
    Runninglog: "Log ng pagpapatakbo",
    Locklog: "Log ng Pag-lock",
    Sortinglog: "Log ng pag-uuri",
    Packagenumberlog: "Kasaysayan ng Numero ng Pakete",
    Sortingrepush: "Dagdag na data ng pagtulak",
    Sortingreport: "Istatistika ng ulat",
    Chutereport: "Dami ng Pag-uuri ng Gripo",
    Sortingcountsup: "Istatistika ng dami (para sa package platform)",
    Sortingcountuser: "Istatistika ng dami (numero ng empleyado)",
    Packagenumbercount: "Kabuuang Istatistika ng Pakete",
    Sortingefficiency: "Istatistika ng kahusayan",
    Errorreport: "Istatistika ng Uri",
    Monitor: "Pagsubaybay ng Sistema",
    Scada: "SCADA monitoring",
    Job: "Nakatakdang Gawain",
    Config: "Pagtatakda ng Parameter",
    Dict: "Pamamahala ng Diksyunaryo",
  },
  page: {
    homepage: {
      boardingcount: "Dami ng Maliit na Sasakyan",
      scancount: "Bilang ng pag-scan",
      cellcount: "Dami ng Pagbagsak",
      cancelleditems: "Kanselahin ang Item",
      rabbitinterceptions: "Kabuuang bilang ng pagharang ng J&T",
      unmatchedcodes: "Hindi tumutugma ang slot ng tatlong segment code",
      overbounds: "Kabuuang Bilang ng Sobra sa Bilog",
      anomalies: "Kabuuang Abnormal",
      sortingvolume: "Dami ng pag-uuri",
      monday: "Lunes",
      tuesday: "Martes",
      wednesday: "Miyerkules",
      thursday: "Huwebes",
      friday: "Biyernes",
      saturday: "Sabado",
      sunday: "Linggo",
    },
    Serviceusermanagement: {
      Refresh: "I-refresh",
      AddUser: "Bagong User",
      BulkDelete: "Maramihang Tanggalin",
      FullName: "Pangalan",
      EmployeeID: "Numero ng Empleyado",
      Query: "Pagtatanong",
      Import: "I-import",
      OnlyExcel: "(Maaari lamang mag-import ng excel.xlsx na format ng file)",
      SerialNumber: "Numero ng pagkakasunod",
      UserEmployeeID: "Numero ng empleyado ng gumagamit",
      UserName: "Pangalan ng gumagamit",
      UserID: "ID ng gumagamit",
      Creator: "Tagalikha",
      CreationTime: "Oras ng Paglikha",
      Operation: "Operasyon",
      UserPassword: "Password ng gumagamit",
      ConfirmPassword: "Kumpirmahin ang Password",
      NewPassword: "Bagong Password",
      Save: "I-save",
      Cancel: "Kanselahin",
      UpdateUser: "Baguhin ang gumagamit",
    },
    usermanagement: {
      UserID: "ID ng gumagamit",
      UserName: "Pangalan ng gumagamit",
      UserAccount: "Account ng gumagamit",
      PlaseUserAccount: "Pakisulat ang Account ng User",
      PhoneNumber: "Numero ng Telepono",
      PleasePhoneNumber: "Mangyaring ipasok ang numero ng telepono",
      Status: "Katayuan",
      PleaseStatus: "Mangyaring ipasok ang estado",
      StartDate: "Petsa ng pagsisimula",
      EndDate: "Petsa ng Pagtatapos",
      CreationTime: "Oras ng Paglikha",
      Operation: "Operasyon",
      UserGender: "Kasarian ng gumagamit",
      UserNickname: "Palayaw ng gumagamit",
      Email: "Email",
      UserPassword: "Password ng gumagamit",
      Active: "Normal",
      Inactive: "I-disable",
      Role: "Papel",
      Note: "Tala",
      Search: "Maghanap",
      Reset: "I-reset",
      Add: "Bagong Idagdag",
      Delete: "Tanggalin",
      Update: "Baguhin",
      More: "Higit pa",
      ResetPassword: "I-reset ang password",
    },
  },
  common: {
    DerivedData: "I-export ang Data",
    ViewDWSPeakEffect: "Tingnan ang peak efficiency ng DWS",
    peakEffect: "Peak na Kahusayan",
    SearchData: "Data ng paghahanap",
    NoData: "Walang Data",
    Arrival: "Pagpasok ng Kargamento",
    Departure: "Paglabas ng Kargamento",
    ArrivalAndDeparture: "Pagpasok at Paglabas ng Kargamento",
    CloseOther: "Isara ang Iba",
    CloseAll: "Isara Lahat",
    RoleName: "Pangalan ng papel",
    PlaseRoleName: "Pakisulat ang Pangalan ng Papel",
    PermissionCharacters: "Karakter ng Pahintulot",
    PlasePermissionCharacters: "Pakisulat ang Karakter ng Pahintulot",
    Order: "Pagkakasunod-sunod",
    Status: "Katayuan",
    Search: "Maghanap",
    Reset: "I-reset",
    Delete: "Tanggalin",
    Update: "Baguhin",
    More: "Higit pa",
    CreationTime: "Oras ng Paglikha",
    Operation: "Operasyon",
    Update: "Baguhin",
    Add: "Bagong Idagdag",
    UpdateCache: "I-update ang cache",
    Select: "Pagtatanong",
    Import: "I-import",
    Export: "I-export",
    SerialNumber: "Numero ng pagkakasunod",
    RoleNumber: "Numero ng papel",
    Memo: "Tala",
    UpdateDate: "Oras ng pagbabago",
    ParameterName: "Pangalan ng Parameter",
    ParameterValue: "Halaga ng Parameter",
    PleaseSelectARole: "Mangyaring pumili ng papel",
    PleaseInputnickname: "Mangyaring ipasok ang palayaw ng gumagamit",
    PleaseInputPhoneNumber: "Mangyaring ipasok ang numero ng telepono",
    PleaseInputEmail: "Mangyaring ipasok ang email",
    PleaseInputUserName: "Mangyaring ipasok ang pangalan ng gumagamit",
    PleaseInputPassWord: "Mangyaring ipasok ang password",
    RoleStatus: "Katayuan ng papel",
    RoleCode: "Numero ng gumagamit",
    PlaseInputRoleCode: "Pakisulat ang Numero ng User",
    CreatorID: "Numero ng Tagalikha",
    Determine: "Kumpirmahin",
    Cancellation: "Kanselahin",
    Task: "Pangalan ng gawain",
    PlaseTask: "Pakisulat ang Pangalan ng Gawain",
    TaskCode: "Numero ng gawain",
    PlaseTaskCode: "Pakisulat ang Numero ng Gawain",
    MenuName: "Pangalan ng Menu",
    PlaseMenuName: "Pakisulat ang Pangalan ng Menu",
    MenuStatus: "Katayuan ng Menu",
    ExpandAndCollapse: "I-expand/I-collapse",
    SelectAllDonteSelectAll: "Piliin lahat/Huwag piliin lahat",
    ParentChildLinkage: "Pagkakaugnay ng Magulang at Anak",
    MenuPermissions: "Pahintulot ng Menu",
    PleaseEnterContent: "Mangyaring ipasok ang nilalaman",
    Icon: "Icon",
    ComponentPath: "Landas ng Komponent",
    SchemeID: "ID ng plano",
    ModeType: "Uri ng Pag-uuri",
    PlanName: "Pangalan ng Plano",
    PlanCode: "Numero ng Plano",
    PlanDesc: "Paglalarawan ng Plano",
    PlanFlag: "Uri ng Plano",
    IsSelected: "Naka-enable ba",
    UpdateDate: "Oras ng pagbabago",
    Detail: "Detalye",
    Enable: "Paganahin",
    Date: "Oras",
    Grade: "Antas",
    Source: "Pinagmulan",
    Message: "Mensahe",
    ExtraData: "Karagdagang Data",
    plcType: "Antas",
    Number: "Numero",
    Component: "Bahagi",
    Chute: "Gripo",
    Supply: "Package platform",
    dwsNo: "DWS Numero",
    BigBarRate: "Proporsyon ng Malaking Pakete",
    Quantity: "Dami",
    SmallBarRate: "Proporsyon ng maliliit na pakete",
    ExceptionCode: "Abnormal na Code",
    Code: "Numero ng Order",
    ScanTime: "Oras ng pag-scan",
    BoardingTime: "Oras ng Pagkarga",
    DropTime: "Oras ng Pagbagsak",
    NextPieceTime: "Naka-package na Oras",
    passBackTime: "Oras ng Feedback",
    arrivalTime: "Oras ng Pagdating",
    PacketNumber: "Numero ng Pakete",
    TimeType: "Uri ng oras",
    turns: "Bilang ng mga ikot",
    Image: "Larawan",
    UpperLevel: "Itaas na Antas",
    LowerLevel: "Ibabang Antas",
    LayerNumber: "Bilang ng mga Lapis",
    PleaseEnterTheDictionaryLabel: "Pakisumite ang Etiketang Diksiyonaryo",
    DictionaryName: "Pangalan ng Diksiyonaryo",
    DictionaryId: "Numero ng Diksiyonaryo",
    DictionaryType: "Uri ng Diksiyonaryo",
    PleaseEnterTheDictionaryType: "Pakisumite ang Uri ng Diksiyonaryo",
    PleaseEnterTheDictionaryName: "Pakisumite ang Pangalan ng Diksiyonaryo",
    BagBindingBfficer: "Taga-Pagkakabit ng Bag",
    DictionaryEncoding: "Pagkakodigo ng Diksiyonaryo",
    DictionaryTag: "Etiketang Diksiyonaryo",
    DictionaryValue: "Halaga ng Diksiyonaryo",
    DictionarySort: "Pag-uuri ng Diksiyonaryo",
    DictionaryRemark: "Puna",
    DictionaryCreateTime: "Oras ng Paglikha",
    DataTag: "Etiketang Data",
    PleaseEnterTheDataLabel: "Pakisumite ang Etiketang Data",
    DataKey: "Susi ng Data",
    StyleAttribute: "Katangian ng Istilo",
    DisplayOrder: "Pagkakasunud-sunod ng Pagpapakita",
    EchoStyle: "Istilo ng Pagpabalik",
    ListClass: "Klaseng Lista",
    PleaseEnterTheDataKey: "Pakisumite ang Susi ng Data",
    PleaseEnterTheStyleAttribute: "Pakisumite ang Katangian ng Istilo",
    PleaseEnterTheDisplayOrder:
      "Pakisumite ang Pagkakasunud-sunod ng Pagpapakita",
    PleaseEnterTheEchoStyle: "Pakisumite ang Istilo ng Pagpabalik",
    PleaseEnterTheListClass: "Pakisumite ang Klaseng Lista",
    PleaseEnterTheRemark: "Pakisumite ang Puna",
    PleaseEnterTheContent: "Pakisumite ang Nilalaman",
    TheAddressNeedsToBehttp:
      "Kung link labas ay kailangang magsimula sa `http(s)://`",
    TheAddressNeedsToBehttpUser:
      "Ang address ng pagbisita, tulad ng: `user`, kung labas ay kailangang magsimula sa `http(s)://` para makapuntang loob",
    TheAddressNeedsToBehttpCatalogue:
      "Ang landas ng komponente na pagbisita, tulad ng: `system/user/index`, default ay nasa direktoryo ng `views`",
    TheDefaultPassingParametersForTheRoute:
      "Ang default na ipinapasa na parametro para sa ruta, tulad ng: `{'id': 1, 'name': 'ry'}`",
    TheComponentWillBeCachedByKeepAlive:
      "Kung piliin ay tatago sa `keep-alive`, kailangang match ang `name` ng komponente at address na magkapareho",
    SelectHiddenThenTheRoute:
      "Kung piliin ang Hidden ay hindi makikita sa sidebar ngunit makakapagbisita",
    SelectDisableThenTheRouteSidebar:
      "Kung piliin ang Disable ay hindi makikita sa sidebar at hindi makakapagbisita",
    PleaseEnterTheRouteParameters: "Pakisumite ang mga parametro ng ruta",
    Yes: "Oo",
    No: "Hindi",
    PermissionCharactersString: "String ng pahintulot na tinukoy sa controller, halimbawa: @PreAuthorize(`@ss.hasRole('admin')`)",
    Cache1: "Kache",
    NoCache: "Hindi Kache",
    AddUser: "Magdagdag ng User",
    BatchCancelAuthorization: "Mag-batch ng Kanselahang Awtorisasyon",
    Close: "Isara",
    CancelAuthorization: "Kanselahin ang Awtorisasyon",
    View: "Tingnan",
    UserType: "Uri ng User",
    PleaseSelectUserType: "Pakipili ang Uri ng User",
    Forward: "Pahinang Itaas",
    Reverse: "Pahinang Ibaba",
    Lock: "I-lock",
    Unlock: "I-unlock",
    SendMessage: "Magpadala ng Mensahe",
    TaskGroup: "Pangalan ng Grupo ng Gawain",
    PleaseSelectTaskGroup: "Pakipili ang Pangalan ng Grupo ng Gawain",
    TaskStatus: "Kalagayan ng Gawain",
    PleaseSelectTaskStatus: "Pakipili ang Kalagayan ng Gawain",
    Log: "Tala",
    InvokeTarget: "String ng Tinawag na Layunin",
    CronExpression: "Ekspresyon ng Cron",
    ExecuteOnce: "Eksekutong isang Beses",
    TaskDetails: "Detalye ng Gawain",
    DispatchLog: "Tala ng Pagpapadala",
    InvokeTarget: "Tinawag na Pamamaraan",
    BeanCallExample: "Halimbawa ng Tawag sa Bean: ryTask.ryParams('ry')",
    ClassCallExample:
      "Halimbawa ng Tawag sa Klase: com.ruoyi.quartz.task.RyTask.ryParams('ry')",
    ParameterDescription:
      "Paglalarawan ng Parametro: Sumusuporta sa String, Boolean, Long, Float, Integer",
    PleaseInputInvokeTarget: "Pakisumite ang String ng Tinawag na Layunin",
    PleaseInputCronExpression: "Pakisumite ang Ekspresyon ng Cron",
    GenerateExpression: "Lumikha ng Ekspresyon",
    ExecuteStrategy: "Pamamaraan ng Eksekusyon",
    MisfirePolicy: "Pamamaraan ng Eksekusyon",
    ImmediateExecution: "Eksekutong Kagyat",
    DelayExecution: "Eksekutong May Pagkaantala",
    AbandonExecution: "Iwanan ang Eksekusyon",
    PleaseSelectExecuteStrategy: "Pakipili ang Pamamaraan ng Eksekusyon",
    Concurrent: "Kasabay ba?",
    Allow: "Payagan",
    Prohibit: "Ipagbawal",
    PleaseSelectConcurrent: "Pakipili kung Kasabay ba?",
    CronExpressionGenerator: "Lumikha ng Ekspresyon ng Cron",
    NextExecutionTime: "Susunod na Oras ng Eksekusyon",
    TaskDetails: "Detalye ng Gawain",
    TaskGroup1: "Grupo ng Gawain",
    DefaultStrategy: "Pangkaraniwang Pamamaraan",
    ExecuteStatus: "Kalagayan ng Eksekusyon",
    PleaseSelectExecuteStatus: "Pakipili ang Kalagayan ng Eksekusyon",
    ExecutionTime: "Oras ng Eksekusyon",
    PleaseSelectExecutionTime: "Pakipili ang Oras ng Eksekusyon",
    Clear: "Maglinis",
    JobLogId: "Numero ng Tala ng Gawain",
    JobMessage: "Mensahe ng Gawain",
    Detail1: "Detalye",
    DispatchLogDetails: "Detalye ng Tala ng Pagpapadala",
    PleaseSelect: "Pakipili",
    SelectStartTime: "Pumili ng Simulang Oras",
    SelectEndTime: "Pumili ng Katapusan ng Oras",
    DebugStart: "Pagsisimula ng Pag-debug",
    DebugClose: "Pagsara ng Pag-debug",
    ClearPacketNumber: "Maglinis ng Numero ng Pakete",
    Upload: "Mag-upload",
    TotalQuery: "Kabuuang Nakitang",
    property: "mga Larawan",
    all: "lahat",
    UnloadingToDeliveryScanning:
      "Pagsikan ng Pagkalabas hanggang Pag-scan ng Pagpapadala",
    BuildingPackageScanning: "Pagsikan ng Pagbuo ng Pakete",

    SelectUser: "Pumili ng User",
    InvokeTargetMethod: "Método de chamada de destino",
    PleaseSelectTaskGroup: "Pumili ng Grupo ng Gawain",
    CronExpression1: "cron expression",
    ExceptionInfo: "Informações de exceção",


    Edit: "I-edit",
    ScopeOfAuthority: "Saklaw ng Kapamahalaan",
    DataPermission: "Kapamahalaan ng Data",
    Confirm: "Kumpirmahin",
    StartDate: "Simulang Araw",
    EndDate: "Katapusan ng Araw",
    Weight: "Timbang",
    length: "Mahaba",
    width: "Lapad",
    heigth: "Mataas",
    CarNumber: "Numero ng Maliit na Sasakyan",
    RequestedGate: "Hiniling na slot",
    PhysicalGrid: "Pisikal na Gripo na Ibinabalik ng PLC",
    taskNo: "Numero ng gawain",
    TerminalDispatchCode: "Tatlong segment code",
    FallingGridTime: "Oras ng Pagbagsak",
    PackageGrade: "Numero ng Pakete",
    ChipNumber: "Numero ng Chip",
    BindingTime: "Oras ng Pag-ayos ng Pakete",
    BindingPersonnel: "Tagapag-ayos ng Pakete",
    ScanType: "Uri ng pag-scan",
    NextStationNumber: "Susunod na Istasyon Numero",
    Rfid: "Electronic package tag",
    TimeInterval: "Saklaw ng oras",
    SortingQuantity: "Dami ng pag-uuri",
    TotalSortingWeight: "Kabuuang timbang ng pag-uuri (KG)",
    NumberOfScannedBarcodeRecords: "Bilang ng Pag-scan ng Barcode",
    RecordTheNumberOfPackagesLoadedOntoTheVehicle:
      "Bilang ng mga tala ng pag-akyat ng package",
    NumberOfDropFeedbackRecords: "Bilang ng Feedback ng Pagbagsak",
    scanQuantity: "Bilang ng pag-scan",
    arrivalQuantity: "Dami ng Dumating",
    passBackQuantity: "Bilang ng Feedback",
    TotalNumberOfPackages: "Kabuuang bilang ng mga pakete",
    Packagenumb: "Numero ng Pakete (Dami ng Item sa Panahong Ito)",
    Type: "Uri",
    Count: "Dami",
    SystemType: "Uri ng sistema",
    EmployeeID: "Pakisulat ang Numero ng Empleyado",
    FullName: "Pakisulat ang Pangalan",
    Password: "Pakisulat ang Password",
    ConfirmPassword: "Pakisulat muli ang Password",
    SelectGender: "Mangyaring pumili ng kasarian",
    Active: "Normal",
    Inactive: "I-disable",
    Male: "Lalaki",
    Female: "Babae",
    Unknown: "Hindi alam",
    RoleOrder: "Pagkakasunod ng papel",
    Show: "Ipakita",
    Hide: "Itago",
    Default: "Default",
    System: "Sistema",
    Success: "Tagumpay",
    Failure: "Nabigo",
    AddMenu: "Magdagdag ng Menu",
    EditMenu: "Baguhin ang Menu",
    ParentMenu: "Mas Mataas na Menu",
    MenuType: "Uri ng Menu",
    Directory: "Direktoryo",
    Menu: "Menu",
    Button: "Buton",
    MenuIcon: "Icon ng Menu",
    SelectIcon: "Piliin ang icon",
    RouteAddress: "Routing address",
    DisplayOrder: "Ipakita ang Pagkakasunod-sunod",
    ExternalLink: "Naka-link ba sa Labas",
    DisplayStatus: "Ipakita ang Katayuan",
    MenuStatus: "Katayuan ng Menu",
    RouteParameters: "Routing parameter",
    Cache: "Naka-cache ba",
    ComponentPath: "Landas ng Komponent",
    AddRole: "Magdagdag ng Papel",
    EditRole: "Baguhin ang Papel",
    AddPlan: "Bagong Plano",
    Cancel: "Kanselahin",
    FirstSegmentCode: "Unang Bahagi ng Code",
    SecondSegmentCode: "Pangalawang segment code",
    ThirdSegmentCode: "Pangatlong segment code",
    NextStopCode: "Susunod na Istasyon Numero",
    NextStopName: "Pangalan ng Susunod na Istasyon",
    ModificationTime: "Oras ng Pagbabago",
    BulkDelete: "Maramihang Tanggalin",
    AddDetails: "Bagong Detalye",
    ByServicePackage: "Ayon sa Pagkarga ng Platform",
    ByDwsNo: "Ayon sa DWS Numero",
    Granularity: "Granularidad",
    LogicCode: "Logical Code",
    PacketType: "Uri ng Pakete",
    IsUpload: "Nai-upload ba",
    AddSuccess: "Matagumpay na Idinagdag",
    EditSuccess: "Matagumpay na Nabago",
    DelSuccess: "Matagumpay na Natanggal",
    ImportSuccessful: "Matagumpay na Na-import",
    BeginExport: "Simulan ang Pag-export",
    ModificationFailed: "Nabigong Baguhin",
    AddFailed: "Nabigong Idagdag",
    OperationSuccessful: "Matagumpay na Operasyon",
    OperationFailed: "Nabigong Operasyon",
    OperationCancellation: "Kanselahin ang Operasyon",
    ExportFailed: "Nabigong I-export",
    LoginOut:
      "Na-logout ka na maaari mong kanselahin upang manatili sa pahinang ito o mag-login muli",
    ConfirmLogout: "Kumpirmahin ang Pag-logout",
    LogAgain: "Mag-login muli",
    Remark: "Tala",
    DwsNo: "DWS Numero",
    Notempty:
      "Ang Username o Password o Verification Code ay hindi maaaring walang laman!",
    Notpassword: "Ang Username o Password ay hindi maaaring walang laman!",
    Id: "Pangunahing Susi ng Parameter",
    Parameter: "Pangalan ng Parameter",
    PlParameter: "Mangyaring ipasok ang pangalan ng parameter",
    ParameterKey: "Pangunahing Susi ng Parameter",
    PlParameterKey: "Mangyaring ipasok ang pangalan ng susi ng parameter",
    ParameterValue: "Halaga ng Pangunahing Susi ng Parameter",
    PlParameterValue: "Mangyaring ipasok ang halaga ng susi ng parameter",
    Group: "Pangalan ng Grupo",
    PlGroup: "Mangyaring pumili ng pangalan ng grupo",
  },
  scada: {
    DeviceRunningStatus: "Katayuan ng Pagpapatakbo ng Kagamitan",
    DeviceStopped: "Ang Kagamitan ay Huminto sa Pagpapatakbo",
    DeviceRunning: "Ang Kagamitan ay Tumatakbo",
    StartTime: "Oras ng pagsisimula ng pagpapatakbo",
    RunningSpeed: "Bilis ng pagpapatakbo",
    CartOccupancyRate: "Rate ng Paggamit ng Maliit na Sasakyan",
    TotalDistanceTraveledByDevice:
      "Kabuuang kilometrahe ng pagpapatakbo ng kagamitan",
    DistanceTraveledInCurrentRun: "Kilometro ng Kasalukuyang Pagpapatakbo",
    TotalDistanceTraveled: "Kabuuang kilometrahe ng pagpapatakbo",
    Scans: "Bilang ng pag-scan",
    PendingStart: "Naghihintay ng Pagsisimula ng Pagpapatakbo",
    FullScreen: "Ipakita sa Fullscreen",
    ExitFull: "Lumabas sa Fullscreen",
    UpperLevelRunningSpeed: "Bilis ng pagpapatakbo ng itaas na antas",
    UpperLevelDeviceRunningStatus:
      "Katayuan ng pagpapatakbo ng itaas na kagamitan",
    UpperLevelStartTime:
      "Oras ng pagsisimula ng pagpapatakbo ng itaas na antas",
    UpperLevelCartOccupancyRate:
      "Porsyento ng pagmamay-ari ng itaas na maliit na kotse",
    UpperLevelByDevice:
      "Kabuuang kilometrahe ng pagpapatakbo ng itaas na kagamitan",
    LowerLevelDeviceRunningStatus:
      "Katayuan ng Pagpapatakbo ng Mas Mababang Kagamitan",
    UpperLayerPLCDisconnect: "Hindi normal na pagdiskonekta ng itaas na PLC",
    LowerLevelPLCDisconnect: "Abnormal na Pagdiskonekta ng Mas Mababang PLC",
    UpperLayerPLCConnectionStatus: "Katayuan ng koneksyon ng itaas na PLC",
    LowerLevelPLCConnectionStatus: "Katayuan ng Koneksyon ng Mas Mababang PLC",
    LowerLevelStartTime:
      "Oras ng Pagsisimula ng Pagpapatakbo ng Mas Mababang Palapag",
    LowerLevelRunningSpeed: "Bilis ng Pagpapatakbo ng Mas Mababang Palapag",
    LowerLevelCartOccupancyRate:
      "Rate ng Paggamit ng Mas Mababang Maliit na Sasakyan",
    LowerLevelTotalDistanceTraveledByDevice:
      "Kabuuang Kilometro ng Pagpapatakbo ng Mas Mababang Kagamitan",
    UpperLevelScans: "Bilang ng pag-scan ng itaas na antas",
    LowerLevelScans: "Bilang ng Pag-scan ng Mas Mababang Palapag",
    AbnormalQuantity: "Dami ng Abnormal",
    NumberOfSlotsOccupied: "Bilang ng Pagbagsak",
    FailedRepushQuantity: "Bilang ng Nabigong Pagtulak",
    InterceptedQuantity: "Bilang ng Intercept",
    ExcessiveCirclesQuantity: "Bilang ng Sobra sa Bilog",
    UnconfiguredThreeSegmentCodeSlots:
      "Hindi naka-configure na slot ng tatlong segment code",
    ComprehensiveExceptionSlots: "Komprehensibong Abnormal na Gripo",
    CancelledItems: "Kanselahin ang Item",
    UnobtainedThreeSegmentCodeInformation:
      "Hindi nakuha ang impormasyon ng tatlong segment code",
    WebSocketStatus: "Katayuan ng WebSocket",
    WCSCommunicationStatus: "Katayuan ng komunikasyon ng WCS",
    Connected: "Nakakonekta",
    NotConnected: "Hindi Nakakonekta",
    SortingStatus: "Katayuan ng pag-uuri",
    Idle: "Libre",
    Loaded: "Kargamento",
    CartStatus: "Katayuan ng Maliit na Sasakyan",
    LittleThingsAreQuick:'Maliit na Kwai',
    Headscratcher: 'Top Scan',
    OnLine:'sa linya',
    Offline:'offline',

    Locked: "I-lock",
    FullPackage: "Buong Pakete",
    SlotStatus: "Katayuan ng Gripo",
    InterceptedItem: "Intercepted Item",
    ExceptionSlot: "Abnormal na Gripo",
    PendingCommunication: "Naghihintay ng Komunikasyon",
    Max: "Pinakamataas na Bilog",
    Cancellation: "Kanselahin ang Item",
    UnProgramme: "Walang naka-configure na plano",
    UnThree: "Walang naka-configure na tatlong segment code",
    CartOperation: "Operasyon ng Maliit na Sasakyan",
    OneKeyUnlock: "Isang-click na Pag-unlock",
    OneKeyLockSlot: "Isang-click na Pag-lock ng Gripo",
    CartNumber: "Numero ng Maliit na Sasakyan",
    FloorNumber: "Bilang ng Palapag",
    UpperLevel: "Itaas na antas",
    LowerLevel: "Mas Mababang Palapag",
    Lock: "I-lock",
    Unlock: "I-unlock",
    ManualSwitchStart: "Manu-manong Paglipat (Simulan)",
    ManualSwitchClose: "Manu-manong Paglipat (Isara)",
    Run: "Patakbuhin",
    Close: "Isara",
    DisabledList: "Listahan ng Pag-disable",
    AbnormalAlarm: "Abnormal na Alarma",
    DisableCart: "I-disable ang Maliit na Sasakyan",
    UpperLevelCart: "Itaas na maliit na kotse",
    LowerLevelCart: "Mas Mababang Maliit na Sasakyan",
    VerificationPassword: "Mangyaring ipasok ang verification password",
    Confirm: "Kumpirmahin",
    ClearTheKilometers: "I-clear ang Kilometro",
    PleaseSelectClearKilometers: "Piliin upang i-clear ang kilometrahe",
    FaultLevel: "Antas ng Pagkakamali",
    StartTime: "Oras ng pagsisimula",
    EndTime: "Oras ng Pagtatapos",
    CriticalAlarm: "Malubhang Alarma",
    GeneralAlarm: "Pangkalahatang Alarma",
    MinorAlarm: "Mild na Alarma",
    SearchCriteria: "Kondisyon ng paghahanap",
    Time: "Oras",
    Select: "Pagtatanong",
    PleaseEnterContent: "Mangyaring ipasok ang nilalaman",
    SerialNumber: "Numero ng pagkakasunod",
    Operation: "Operasyon",
    AlarmHelpLink: "Dokumentong Pantulong",
    AlarmType: "Uri ng Alarma",
    AlarmSource: "Pinagmulan ng Alarma",
    Content: "Nilalaman",
  },
};

export default ph;
