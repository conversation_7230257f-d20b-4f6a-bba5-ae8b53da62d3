import router, { constantRoutes } from './router'
import Layout from '@/layout'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
NProgress.configure({ showSpinner: false }) // NProgress Configuration
const whiteList = ['/login'] // no redirect whitelist
import i18n from "./lang";

router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start()
  // set page title
  document.title = getPageTitle(to.meta.title)
  // determine whether the user has logged in
  const hasToken = getToken()
  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done()
    } else {
      const hasGetUserInfo = store.getters.name
      if (hasGetUserInfo) {
        next()
      } else {
        try {
          // get user info
          await store.dispatch('user/getInfo')
          await store.dispatch('user/getRouters')
          const getAccessRoutes = store.state.user.userInfo
          //const accessRoutes = getAccessRoutes.map(item => item.menuName)
         
          // 定义好的全部分类
          const routeList = filterAsyncRouter(getAccessRoutes)
          const lastRouteList = []
          // 根据roles权限生成可访问的路由表
          const newRouters = constantRoutes.concat(routeList)

          router.options.routes = newRouters // 动态添加可访问路由表
          router.addRoutes(newRouters)
          next(to.path) // hack方法 确保addRoutes已完成
          // next()
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap) {
  return asyncRouterMap.filter(route => {
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component == 'Layout') {
        route.component = Layout
      } else {
        route.component = loadView(route.component)
      }
    }
    let str = 'sidebar.'+ route.name
    route.meta.title = i18n.t(str)
    
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children)
    } else {
      delete route['children']
      delete route['redirect']
    }
    return true
  })
}

export const loadView = (view) => {
    // 使用 import 实现生产环境的路由懒加载
   //return () => import(`@/views/${view}`)
    return resolve => require([`@/views/${view}`], resolve)
}

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
