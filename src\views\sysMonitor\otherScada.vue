<template>
    <div id="svgDiv">
       <!-- <div id="d0" style="position: absolute;margin: 0 0 0 0;">
          <img :width="width" :height="height" src="@/assets/user_images/bgimgMain.png">
       </div> -->
 
      
      <div id="d2" style="position: absolute;margin: 80px 0 0 0;">
          <span style="margin: 0 0 0 20px">
         {{$t('common.SystemType')}}:
           <el-select v-model="systemTypeCode" style="width:120px"  @change="handleOptionChange">
             <el-option
               v-for="item in systemOptions"
               :key="item.dbCode"
               :label="item.dbName"
               :value="item.dbCode"
             />
           </el-select>
         </span>
       </div>
 
       <div id="d2" style="position: absolute;margin: 290px 0 0 300px;">
          <img width="150" height="70" src="@/assets/user_images/textBK1.png">
          <img width="150" height="70" src="@/assets/user_images/textBK2.png">
          <img width="150" height="70" src="@/assets/user_images/textBK3.png">
       </div>
       <div id="d3" style="position: absolute;margin: 370px 0 0 300px;">
          <img width="150" height="70" src="@/assets/user_images/textBK1.png">
          <img width="150" height="70" src="@/assets/user_images/textBK2.png">
          <img width="150" height="70" src="@/assets/user_images/textBK3.png">
       </div>
       <div id="d4" style="position: absolute;margin: 450px 0 0 300px;">
          <img width="150" height="70" src="@/assets/user_images/textBK1.png">
          <img width="150" height="70" src="@/assets/user_images/textBK2.png">
          <img width="150" height="70" src="@/assets/user_images/textBK3.png">
       </div>
       <div id="d5" style="position: absolute;margin: 530px 0 0 300px;">
          <img width="150" height="70" src="@/assets/user_images/textBK1.png">
          <img width="150" height="70" src="@/assets/user_images/textBK2.png">
          <img width="150" height="70" src="@/assets/user_images/textBK3.png">
       </div>
 
       <div id="d6" style="position: absolute;margin: 260px 0 0 850px;">
          <img width="630" height="380" src="@/assets/user_images/bgimg1.png">
       </div>
 
 
       <div id="d1" style="position: absolute;margin: 285px 0 0 900px;">
          <Footer v-if="isOne"></Footer>
          <FooterTwo v-if="isTwo"></FooterTwo>
       </div>
 
       <svg id="svg1" :width="width" :height="height">
          <rect style="fill:#f0ffff;stroke-width:0.264583" :width="width" :height="height" x="0" y="0" />
          <rect style="fill:#f4f4f4;stroke-width:0.264583" :width="width" height="40" x="0" y="0" />
          <rect style="fill:#f4f4f4;stroke-width:0.264583" :width="width" height="40" x="0" y="40" />
          <rect style="fill:#f4f4f4;stroke-width:0.264583" :width="width" height="40" x="0" :y="heightDown" />
 
          <g v-if="isOne">
             <text x="20" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.DeviceRunningStatus')}}：</text>
             <circle cx="125" cy="20" r="5" fill="red" id="role2"></circle>
             <text x="140" y="24" style="font-size:14px;font-weight:bold" id="text1">{{ this.runningTextDown }}</text>
 
             <text x="300" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.StartTime')}}：</text>
             <text x="400" y="24" style="font-size:14px;font-weight:bold" id="text2">{{ this.nowStartDateDown }}</text>
 
             <text x="600" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.RunningSpeed')}}：</text>
             <text x="670" y="24" style="font-size:14px;font-weight:bold" id="text3">{{ this.speedDialDown }} m/s</text>
 
             <text x="800" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.CartOccupancyRate')}}：</text>
             <text x="910" y="24" style="font-size:14px;font-weight:bold" id="text4">{{ this.sortingMinutesCarShareDown
             }}%</text>
 
             <!-- <text x="1000" y="24" style="font-size:14px;" fill="#7e7e7e">下层小车占有率：</text>
          <text x="1110" y="24" style="font-size:14px;font-weight:bold" id="text4_2">{{ this.sortingMinutesCarShareDown
          }}%</text> -->
 
             <text x="1000" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.TotalDistanceTraveledByDevice')}}：</text>
             <text x="1130" y="24" style="font-size:14px;font-weight:bold" id="text91">{{ this.distanceDialDown }} km</text>
 
 
             <!-- //x+12 y+45 -->
             <text x="375" y="345" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
                alignment-baseline="middle"></text>
             <!-- //x y-23 -->
             <text x="375" y="322" style="font-size:18px;font-weight:bold;" fill="#008000" id="text5" text-anchor="middle"
                alignment-baseline="middle">{{ this.scansNumDown }}</text>
 
 
             <!-- //x+12 y+45 -->
             <text x="530" y="345" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.DistanceTraveledInCurrentRun')}}</text>
             <!-- //x+36 y-23 -->
             <text x="530" y="322" style="font-size:18px;font-weight:bold" fill="#008000" id="text6" text-anchor="middle"
                alignment-baseline="middle">{{ this.sortingMinutesDistance }}km</text>
 
             <!-- //x+12 y+45 -->
             <text x="685" y="345" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.TotalDistanceTraveled')}}</text>
                
          <!-- //x+36 y-23 -->
          <text x="685" y="322" style="font-size:18px;font-weight:bold" fill="#008000" id="text7" text-anchor="middle"
             alignment-baseline="middle">{{ this.distanceDialDown }}km</text>
          </g>
 
          <g v-if="isTwo">
             <text x="20" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.UpperLevelDeviceRunningStatus')}}：</text>
             <circle cx="145" cy="20" r="5" fill="red" id="role1"></circle>
             <text x="160" y="24" style="font-size:14px;font-weight:bold" id="text1">{{ this.runningText }}</text>
 
             <text x="300" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.UpperLevelStartTime')}}：</text>
             <text x="430" y="24" style="font-size:14px;font-weight:bold" id="text2">{{ this.nowStartDate }}</text>
 
             <text x="600" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.UpperLevelRunningSpeed')}}：</text>
             <text x="700" y="24" style="font-size:14px;font-weight:bold" id="text3">{{ this.speedDial }} m/s</text>
 
             <text x="860" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.UpperLevelCartOccupancyRate')}}：</text>
             <text x="970" y="24" style="font-size:14px;font-weight:bold" id="text4">{{ this.sortingMinutesCarShare}}%</text>
 
             <text x="1100" y="24" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.UpperLevelByDevice')}}：</text>
             <text x="1270" y="24" style="font-size:14px;font-weight:bold" id="text91">{{ this.distanceDial }} km</text>
 
 
             <text x="20" y="64" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.LowerLevelDeviceRunningStatus')}}：</text>
             <circle cx="145" cy="60" r="5" fill="red" id="role2"></circle>
             <text x="160" y="64" style="font-size:14px;font-weight:bold" id="text1">{{ this.runningTextDown }}</text>
 
             <text x="300" y="64" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.LowerLevelStartTime')}}：</text>
             <text x="430" y="64" style="font-size:14px;font-weight:bold" id="text2">{{ this.nowStartDateDown }}</text>
 
             <text x="600" y="64" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.LowerLevelRunningSpeed')}}：</text>
             <text x="700" y="64" style="font-size:14px;font-weight:bold" id="text3">{{ this.speedDialDown }} m/s</text>
 
 
             <text x="860" y="64" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.LowerLevelCartOccupancyRate')}}：</text>
             <text x="970" y="64" style="font-size:14px;font-weight:bold" id="text4_2">{{ this.sortingMinutesCarShareDown }}%</text>
 
             <text x="1100" y="64" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.LowerLevelTotalDistanceTraveledByDevice')}}：</text>
             <text x="1270" y="64" style="font-size:14px;font-weight:bold" id="text91">{{ this.distanceDialDown }} km</text>
 
             <!-- //x+12 y+45 -->
             <text x="375" y="345" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.UpperLevelScans')}}</text>
             <!-- //x y-23 -->
             <text x="375" y="322" style="font-size:18px;font-weight:bold;" fill="#008000" id="text5" text-anchor="middle"
                alignment-baseline="middle">{{ this.scansNum }}</text>
 
 
             <!-- //x+12 y+45 -->
             <text x="530" y="345" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.LowerLevelScans')}}</text>
             <!-- //x+36 y-23 -->
             <text x="530" y="322" style="font-size:18px;font-weight:bold" fill="#008000" id="text6" text-anchor="middle"
                alignment-baseline="middle">{{ this.scansNumDown }}</text>
 
             <!-- //x+12 y+45 -->
             <text x="685" y="345" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.DistanceTraveledInCurrentRun')}}</text>
 
          <!-- //x+36 y-23 -->
          <text x="685" y="322" style="font-size:18px;font-weight:bold" fill="#008000" id="text7" text-anchor="middle"
             alignment-baseline="middle">{{ this.sortingMinutesDistance }}km</text>
          </g>
 
 
          <!-- //x+12 y+45 -->
          <text x="375" y="425" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
             alignment-baseline="middle">{{$t('scada.AbnormalQuantity')}}</text>
          <!-- //x y-23 -->
          <text x="375" y="402" style="font-size:18px;font-weight:bold" fill="red" id="text8" text-anchor="middle"
             alignment-baseline="middle">{{ this.sortingErrorNum }}</text>
 
          <!-- //x+12 y+45 -->
          <text x="530" y="425" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
             alignment-baseline="middle">{{$t('scada.NumberOfSlotsOccupied')}}</text>
          <!-- //x+36 y-23 -->
          <text x="530" y="402" style="font-size:18px;font-weight:bold" fill="#008000" id="text9" text-anchor="middle"
             alignment-baseline="middle">{{ this.fallingNum }}</text>
 
          <!-- //x+12 y+45 -->
          <text x="685" y="425" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
             alignment-baseline="middle">{{$t('scada.FailedRepushQuantity')}}</text>
          <!-- //x+36 y-23 -->
          <text x="685" y="402" style="font-size:18px;font-weight:bold" fill="#008000" id="text10" text-anchor="middle"
             alignment-baseline="middle">{{ this.exceedsMaxNum }}</text>
 
 
          <!-- //x+12 y+45 -->
          <text x="375" y="505" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
             alignment-baseline="middle">{{$t('scada.InterceptedQuantity')}}</text>
          <!-- //x y-23 -->
          <text x="375" y="482" style="font-size:18px;font-weight:bold" fill="#008000" id="text11" text-anchor="middle"
             alignment-baseline="middle">{{ this.interceptNum }}</text>
 
          <!-- //x+12 y+45 -->
          <text x="530" y="505" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
             alignment-baseline="middle">{{$t('scada.ExcessiveCirclesQuantity')}}</text>
          <!-- //x+36 y-23 -->
          <text x="530" y="482" style="font-size:18px;font-weight:bold" fill="#008000" id="text12" text-anchor="middle"
             alignment-baseline="middle">{{ this.hypercycleNum }}</text>
 
          <!-- //x+12 y+45 -->
          <text x="685" y="505" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
             alignment-baseline="middle">{{$t('scada.UnconfiguredThreeSegmentCodeSlots')}}</text>
          <!-- //x+36 y-23 -->
          <text x="685" y="482" style="font-size:18px;font-weight:bold" fill="#008000" id="text13" text-anchor="middle"
             alignment-baseline="middle">{{ this.notConfiguredGrid }}</text>
 
          <!-- //x+12 y+45 -->
          <text x="375" y="585" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
             alignment-baseline="middle">{{$t('scada.ComprehensiveExceptionSlots')}}</text>
          <!-- //x y-23 -->
          <text x="375" y="562" style="font-size:18px;font-weight:bold" fill="#008000" id="text14" text-anchor="middle"
             alignment-baseline="middle">{{ this.abnormalNum }}</text>
 
          <!-- //x+12 y+45 -->
          <text x="530" y="585" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
             alignment-baseline="middle">{{$t('scada.CancelledItems')}}</text>
          <!-- //x+36 y-23 -->
          <text x="530" y="562" style="font-size:18px;font-weight:bold" fill="#008000" id="text15" text-anchor="middle"
             alignment-baseline="middle">{{ this.cancelNum }}</text>
 
          <!-- //x+12 y+45 -->
          <text x="685" y="585" style="font-size:14px;" fill="#4d6aac" text-anchor="middle"
             alignment-baseline="middle">{{$t('scada.UnobtainedThreeSegmentCodeInformation')}}</text>
          <!-- //x+36 y-23 -->
          <text x="685" y="562" style="font-size:18px;font-weight:bold" fill="#008000" id="text16" text-anchor="middle"
             alignment-baseline="middle">{{ this.notObtainedNum }}</text>
 
          <g v-if="isOne">
             <text x="1027" y="525" style="font-size:14px;font-weight:bold" fill="#63839e" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.RunningSpeed')}}</text>
             <text x="1273" y="525" style="font-size:14px;font-weight:bold" fill="#63839e" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.CartOccupancyRate')}}</text>
          </g>
 
          <g v-if="isTwo">
             <text x="1027" y="445" style="font-size:14px;font-weight:bold" fill="#63839e" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.UpperLevelRunningSpeed')}}</text>
             <text x="1027" y="590" style="font-size:14px;font-weight:bold" fill="#63839e" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.LowerLevelRunningSpeed')}}</text>
             <text x="1273" y="445" style="font-size:14px;font-weight:bold" fill="#63839e" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.UpperLevelCartOccupancyRate')}}</text>
             <text x="1273" y="590" style="font-size:14px;font-weight:bold" fill="#63839e" text-anchor="middle"
                alignment-baseline="middle">{{$t('scada.LowerLevelCartOccupancyRate')}}</text>
          </g>
 
          <text x="20" :y="(heightDown + 24)" style="font-size:12px;" fill="#7e7e7e">{{$t('scada.WebSocketStatus')}}：</text>
          <circle cx="140" :cy="(heightDown + 19)" r="5" fill="red" id="run2"></circle>
          <text x="155" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e" id="text99">{{ this.webSocketRunText
          }}</text>
 
          <text x="240" :y="(heightDown + 24)" style="font-size:12px;" fill="#7e7e7e">{{$t('scada.WCSCommunicationStatus')}}：</text>
          <circle cx="345" :cy="(heightDown + 19)" r="5" fill="red" id="run3"></circle>
          <text x="360" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e" id="text99">{{ this.wcsRunText
          }}</text>
 
 
          <text x="500" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.SortingStatus')}}：</text>
          <text x="600" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.Idle')}}：</text>
          <circle cx="645" :cy="(heightDown + 19)" r="5" fill="#008000" id="cir2"></circle>
          <text x="690" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.Loaded')}}：</text>
          <circle cx="735" :cy="(heightDown + 19)" r="5" fill="#ff8c00" id="cir2"></circle>
          <text x="775" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.Locked')}}：</text>
          <circle cx="820" :cy="(heightDown + 19)" r="5" fill="red" id="cir2"></circle>
          <text x="865" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.FullPackage')}}：</text>
          <circle cx="910" :cy="(heightDown + 19)" r="5" fill="#8b008b" id="cir2"></circle>
          <text x="955" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.InterceptedItem')}}：</text>
          <circle cx="1010" :cy="(heightDown + 19)" r="5" fill="#0000ff" id="cir2"></circle>
          <text x="1055" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.ExceptionSlot')}}：</text>
          <circle cx="1110" :cy="(heightDown + 19)" r="5" fill="#000000" id="cir2"></circle>
          <text x="1155" :y="(heightDown + 24)" style="font-size:14px;" fill="#7e7e7e">{{$t('scada.PendingCommunication')}}：</text>
          <circle cx="1215" :cy="(heightDown + 19)" r="5" fill="#7c9292" id="cir2"></circle>
 
          <g id="operate" style="display: none;">
 
             <g @click="abnormalAlarm()" style="cursor:pointer;">
                <rect width="120" height="40" x="640" y="80" ry="5" fill="url(#grad0)"></rect>
                <text x="662" y="107" style="font-size:18px;font-weight:bold;font-family:微软雅黑" fill="white">{{$t('scada.AbnormalAlarm')}}</text>
             </g>
 
             <g @click="forbiddenList()" style="cursor:pointer;">
                <rect width="120" height="40" x="780" y="80" ry="5" fill="url(#grad0)"></rect>
                <text x="802" y="107" style="font-size:18px;font-weight:bold;font-family:微软雅黑" fill="white">{{$t('scada.DisabledList')}}</text>
             </g>
             <linearGradient id="grad0" x1="0%" y1="0%" x2="120%" y2="0%">
                <stop offset="0%" style="stop-color:#070707;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#0914bc;stop-opacity:1" />
             </linearGradient>
 
             <rect width="120" height="40" x="920" y="80" ry="5" fill="url(#grad1)" style="cursor:pointer;"
                @click="carOperate()"></rect>
             <text x="942" y="107" style="font-size:18px;cursor:pointer;font-weight:bold;font-family:微软雅黑" fill="white"
                @click="carOperate()">{{$t('scada.CartOperation')}}</text>
 
             <rect width="120" height="40" x="1060" y="80" ry="5" fill="url(#grad1)" style="cursor:pointer;"
                @click="chuteUnLockAllBtn()"></rect>
             <linearGradient id="grad1" x1="0%" y1="0%" x2="120%" y2="0%">
                <stop offset="0%" style="stop-color:#024002;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#008000;stop-opacity:1" />
             </linearGradient>
             <text x="1082" y="107" style="font-size:18px;cursor:pointer;font-weight:bold;font-family:微软雅黑" fill="white"
                @click="chuteUnLockAllBtn()">{{$t('scada.OneKeyUnlock')}}</text>
 
             <rect width="120" height="40" x="1200" y="80" ry="5" fill="url(#grad2)" style="cursor:pointer;"
                @click="chuteLockAllBtn()"></rect>
             <linearGradient id="grad2" x1="0%" y1="0%" x2="120%" y2="0%">
                <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
                <stop offset="100%" style="stop-color:rgb(255,255,0);stop-opacity:1" />
             </linearGradient>
             <text x="1222" y="107" style="font-size:18px;cursor:pointer;font-weight:bold;font-family:微软雅黑" fill="white"
                @click="chuteLockAllBtn()">{{$t('scada.OneKeyLockSlot')}}</text>
          </g>
 
          <rect width="100" height="30" x="1540" y="5" ry="5" fill="url(#grad3)" style="cursor:pointer;"
             @click="requestFullScreen()"></rect>
          <linearGradient id="grad3" x1="0%" y1="0%" x2="120%" y2="0%">
             <stop offset="0%" style="stop-color:#63839e;stop-opacity:1" />
             <stop offset="100%" style="stop-color:rgb(255,255,0);stop-opacity:1" />
          </linearGradient>
          <text x="1553" y="26" style="font-size:18px;cursor:pointer;font-weight:bold;font-family:微软雅黑" fill="white"
             @click="requestFullScreen()">{{ this.fullText }}</text>
       </svg>
 
       <!-- <router-view /> -->
       <el-dialog :title="$t('scada.CartOperation')" :visible.sync="carVisible" width="30%" append-to-body>
          <el-form ref="form" :model="form" label-width="80px">
             <el-form-item :label="$t('scada.CartNumber')">
                <el-input v-model="form.name"></el-input>
             </el-form-item>
             <el-form-item :label="$t('scada.FloorNumber')" v-if="isTwo">
                <el-radio-group v-model="form.number" @change="layChange">
                   <el-radio label="1">{{$t('scada.UpperLevel')}}</el-radio>
                   <el-radio label="2">{{$t('scada.LowerLevel')}}</el-radio>
                </el-radio-group>
             </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
             <el-button @click="carLock()" type="danger">{{$t('scada.Lock')}}</el-button>
             <el-button @click="carUnLock()" type="success">{{$t('scada.Unlock')}}</el-button>
             <el-button @click="plcHand('1')" type="primary" v-if="isScadaStop">{{$t('scada.ManualSwitchStart')}}</el-button>
             <el-button @click="plcHand('0')" type="warning" v-if="!isScadaStop">{{$t('scada.ManualSwitchClose')}}</el-button>
             <el-button @click="plcCarNo()" type="primary">{{$t('scada.CartNumber')}}</el-button>
             <el-button @click="plcRun()" type="warning">{{$t('scada.Run')}}</el-button>
             <el-button @click="carVisible = false">{{$t('scada.Close')}}</el-button> 
          </span>
       </el-dialog>
 
       <el-dialog :title="$t('scada.AbnormalAlarm')" :visible.sync="abnormalAlarmVisible" width="50%" append-to-body>
          <!-- <el-form ref="form" :model="form" label-width="80px">
             <el-form-item label="报警日志:" >
                <el-input v-model="abnormalList" type="textarea" :disabled="true"></el-input>
             </el-form-item>
          </el-form> -->
          <vxe-table ref="xTable" show-footer resizable show-overflow :data="abnormalList">
       <vxe-table-column :resizable="true" type="seq" title="序号" width="50" />
       <vxe-table-column :resizable="true" field="createTime" title="时间"   width="150" />
       <vxe-table-column :resizable="true" field="message" title="报警信息" />
     </vxe-table>
          <span slot="footer" class="dialog-footer">
             <el-button @click="debugBtn()" type="primary" v-if="isLog">调试(启动)</el-button>
             <el-button @click="debugBtn()" type="warning" v-if="!isLog">调试(关闭)</el-button>
             <el-button @click="abnormalAlarmVisible = false">{{$t('scada.Close')}}</el-button>
          </span>
       </el-dialog>
 
 
       <el-dialog :title="$t('scada.DisabledList')" :visible.sync="forbiddenVisible" width="25%" append-to-body>
          <el-form ref="form" :model="form" label-width="80px">
             <el-form-item :label="$t('scada.DisableCart')"  v-if="isOne">
                <el-input v-model="forbidden.downCarStr" type="textarea" :disabled="true"></el-input>
             </el-form-item>
             <el-form-item :label="$t('scada.UpperLevelCart')"  v-if="isTwo">
                <el-input v-model="forbidden.upCarStr" type="textarea" :disabled="true"></el-input>
             </el-form-item>
             <el-form-item :label="$t('scada.LowerLevelCart')" v-if="isTwo">
                <el-input v-model="forbidden.downCarStr" type="textarea" :disabled="true"></el-input>
             </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
             <el-button @click="forbiddenVisible = false">{{$t('scada.Close')}}</el-button>
          </span>
       </el-dialog>
 
 
       <el-dialog :title="$t('scada.OneKeyUnlock')" :visible.sync="chuteUnLockVisible" width="25%" append-to-body>
          <el-form ref="form" :model="form" label-width="120px">
             <el-form-item :label="$t('scada.VerificationPassword')">
                <el-input v-model="form.chuteUnLockPwd" type="password"></el-input>
             </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
             <el-button @click="chuteUnLockAll()" type="danger">{{$t('scada.Confirm')}}</el-button>
             <el-button @click="chuteUnLockVisible = false">{{$t('scada.Close')}}</el-button>
          </span>
       </el-dialog>
 
       <el-dialog :title="$t('scada.OneKeyLockSlot')" :visible.sync="chuteLockVisible" width="25%" append-to-body>
          <el-form ref="form" :model="form" label-width="120px">
             <el-form-item :label="$t('scada.VerificationPassword')">
                <el-input v-model="form.chuteLockPwd" type="password"></el-input>
             </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
             <el-button @click="chuteLockAll()" type="danger">{{$t('scada.Confirm')}}</el-button>
             <el-button @click="chuteLockVisible = false">{{$t('scada.Close')}}</el-button>
          </span>
       </el-dialog>
 
    </div>
 </template>
 <style>
 text {
    font-family: '微软雅黑';
 }
 </style>
  
 <script>
 
 import Footer from '@/views/sysMonitor/Footer.vue'
 import FooterTwo from '@/views/sysMonitor/FooterTwo.vue'
 import request from '@/utils/request'
 //import getMAC, { isMAC } from 'getmac'
 import { v4 as uuidv4 } from 'uuid'
 //import path from 'path'
 import { Message, MessageBox } from 'element-ui'
 export default {
    components: { Footer, FooterTwo },
    data() {
       return {
          Distinguish: '0',//0为一个PLC、1为两个PLC
          //LayerNum: 1,//层数
          carCountState: 1,//1 无锡小车号特殊处理,
          isOne: false,
          isTwo: true,
          carVisible: false,
          form: {
             name: '',
             number: '2',
             chuteLockPwd: '',
             chuteUnLockPwd: '',
          },
          forbiddenVisible: false,
          chuteUnLockVisible: false,
          chuteLockVisible: false,
          abnormalAlarmVisible:false,
          abnormalList:[],
          forbidden: {
             upCarStr: '',
             downCarStr: ''
          },
          forbiddenDis: "false",
          scalesNum: 1,// 缩放比例
          WsUrl: 'ws://192.168.20.238:9001/ws',//"ws://82.157.123.54:9010/ajaxchattest",
          websock: null,
          socket: null,
          isFullFlag: false,//是否全屏
          CarNum: 622,
          ChuteNum: 320,
          SupplysNum: 24,
          width: '1700px',
          height: '845px',
          heightDown: 805,
          // 上层格口1 2，下层格口3 4
          // ChuteNum1: "2;4;6;8;10;12;14;16;18;20;22;24;26;28;30;32;34;36;38;40;42;44;46;48;50;52;54;56;58;60;62;64;66;68;70;72;74;76;78;80;82;84;86;88;90;92;94;96;98;100;102;104;106;108;110;",
          // ChuteNum2: "112;114;116;118;120;122;124;126;128;130;132;134;136;138;140;142;144;146;148;150;152;154;156;158;160",
          // ChuteNum3: "162;164;166;168;170;172;174;176;178;180;182;184;186;188;190;192;194;196;198;200;202;204;206;208;210;212;214;216;218;220;222;224;226;228;230;232;234;236;238;240;242;244;246;248;250;252;254;256;258;260;262;264;266;268;270;",
          // ChuteNum4: "272;274;276;278;280;282;284;286;288;290;292;294;296;298;300;302;304;306;308;310;312;314;316;318;320",
          // ChuteNum5: "1;3;5;7;9;11;13;15;17;19;21;23;25;27;29;31;33;35;37;39;41;43;45;47;49;51;53;55;57;59;61;63;65;67;69;71;73;75;77;79;81;83;85;87;89;91;93;95;97;99;101;103;105;107;109;",
          // ChuteNum6: "111;113;115;117;119;121;123;125;127;129;131;133;135;137;139;141;143;145;147;149;151;153;155;157;159",
          // ChuteNum7: "161;163;165;167;169;171;173;175;177;179;181;183;185;187;189;191;193;195;197;199;201;203;205;207;209;211;213;215;217;219;221;223;225;227;229;231;233;235;237;239;241;243;245;247;249;251;253;255;257;259;261;263;265;267;269;",
          // ChuteNum8: "271;273;275;277;279;281;283;285;287;289;291;293;295;297;299;301;303;305;307;309;311;313;315;317;319",
 
          ChuteNum1: "234;236;238;240;242;244;246;248;250;252;254;256;258;260;262;264;266;268;270;272;274;276;278;280;282;284;286;288;290;292;294;296;298;300;302;304;306;308;310;312;314;316;318;320;",
          ChuteNum2: "2;4;6;8;10;12;14;16;18;20;22;24;26;28;30;32;34;36;38;40;42;44;46;48;50;52;54;56;58;60;62;64;66;68;70;72;",
          ChuteNum3: "74;76;78;80;82;84;86;88;90;92;94;96;98;100;102;104;106;108;110;112;114;116;118;120;122;124;126;128;130;132;134;136;138;140;142;144;146;148;150;152;154;156;158;160;",
          ChuteNum4: "162;164;166;168;170;172;174;176;178;180;182;184;186;188;190;192;194;196;198;200;202;204;206;208;210;212;214;216;218;220;222;226;228;230;232;",
 
          ChuteNum5: "233;235;237;239;241;243;245;247;249;251;253;255;257;259;261;263;265;267;269;271;273;275;277;279;281;283;285;287;289;291;293;295;297;299;301;303;305;307;309;311;313;315;317;319;",
          ChuteNum6: "1;3;5;7;9;11;13;15;17;19;21;23;25;27;29;31;33;35;37;39;41;43;45;47;49;51;53;55;57;59;61;63;65;67;69;71;",
          ChuteNum7: "73;75;77;79;81;83;85;87;89;91;93;95;97;99;101;103;105;107;109;111;113;115;117;119;121;123;125;127;129;131;133;135;137;139;141;143;145;147;149;151;153;155;157;159;",
          ChuteNum8: "161;163;165;167;169;171;173;175;177;179;181;183;185;187;189;191;193;195;197;199;201;203;205;207;209;211;213;215;217;219;221;223;225;227;229;231;",
 
          //异常格口
          ChuteErro: "160",
          //上层供包台1 2，下层供包台3 4
          SupplysNum1: "13-18",
          SupplysNum2: "19-24",
          SupplysNum3: "1-6",
          SupplysNum4: "7-12",
          isCreateCar: false,
          isCreateChute: false,
          isCreateSupplys: false,
          oneTime: 62.2,//小车一圈时间/S
          time1: 0,
          time2: 0,
          runPath1: "",
          runPath2: "",
          scansNum: "0",//上层扫描数
          scansNumDown: "0",//下层扫描数
          sortingMinutesDistance: "0",//本次运行公里数
          sortingMinutesDistanceDown: "0",//下层本次运行公里数
          sortingErrorNum: "0", //异常量
          fallingNum: "0",//落格数
          exceedsMaxNum: "0",//失败补推数
          interceptNum: "0",//拦截数
          hypercycleNum: "0",//超圈数
          notConfiguredGrid: "0",//未配置三段码格口
          abnormalNum: "0",//综合异常口
          cancelNum: "0", //取消件
          notObtainedNum: "0",//未获取三段码信息
          runningText: this.$t('scada.DeviceStopped'),//设备运行状态
          runningTextDown: this.$t('scada.DeviceStopped'),//设备运行状态
          nowStartDate: this.$t('scada.PendingStart'),//开始运行时间
          nowStartDateDown: this.$t('scada.PendingStart'),//开始运行时间
          speedDial: "0.000",//运行速度
          speedDialDown: "0.000",//下层运行速度
          sortingMinutesCarShare: "0.000",//小车占有率
          sortingMinutesCarShareDown: "0.000",//下层小车占有率
          distanceDial: "0.000",//设备运行总公里数
          distanceDialDown: "0.000",//下层设备运行总公里数
          webSocketRunText: "未连接",
          wcsRunText: "未连接",
          isMainIp: false,
          fullText: "全屏显示",
          baseHeightNum: 1920,
          baseHeightNum: 937,
          upNo:"2",
          downNo:"1",
          isScadaStop:true,
          isLog:false,
          systemOptions:[],
          systemTypeCode:"1",
          isWcs : true
       }
    },
    created() {
       const typeCode = this.$route.params && this.$route.params.typeCode;
       this.getBoxValue(typeCode)
 
       // this.$nextTick(() => {
       //    //this.initWebSocket()
       //    this.getSvg()
       // })
    },
    mounted() {
 
       // 监听页面全屏
       // window.addEventListener("fullscreenchange", () => {
       //    if (screenfull.isFullscreen) {
       //       this.isFullFlag = true
       //       this.fullText = "退出全屏"
       //    } else {
       //       this.isFullFlag = false
       //       this.fullText = "全屏显示"
       //    }
       // })
       var browerWidth = window.innerWidth  //window.getComputedStyle(document.getElementsByClassName('app-main')[0]).width.replace("px", "")//浏览器可视宽度
       var baseWidth = 1920 //设计稿宽度
       var zoomValue = browerWidth / baseWidth //缩放比例计算
 
       var browerHeight = window.innerHeight  //window.getComputedStyle(document.getElementsByClassName('app-main')[0]).height.replace("px", "")//浏览器可视高度
       var baseHeight = 937 //设计稿宽度
       var zoomValue1 = browerHeight / baseHeight //缩放比例计算
       var dom = document.getElementsByClassName('app-main')[0]
       document.getElementById("svgDiv").style.transform = "scale(" + zoomValue + "," + zoomValue1 + ")" //mainContainer为主容器id
       document.getElementById("svgDiv").style.transformOrigin = "left top"
       window.onresize = () => { //窗口尺寸变化时，重新计算和缩放
          var browerWidth = window.innerWidth
          var browerHeight = window.innerHeight
          var flag = this.isFullFlag
          if (document.fullscreenElement) {
             zoomValue = browerWidth / 1640
             zoomValue1 = browerHeight / 880 //缩放比例计算
             this.fullText = "退出全屏"
          } else {
             zoomValue = browerWidth / baseWidth
             zoomValue1 = browerHeight / baseHeight //缩放比例计算
             this.fullText = "全屏显示"
          }
          document.getElementById("svgDiv").style.transform = "scale(" + zoomValue + "," + zoomValue1 + ")"
       }
       document.addEventListener('keydown', this.onKeyDown);
    },
    beforeDestroy() {
       document.removeEventListener('keydown', this.onKeyDown);
    },
    methods: {
       scaleAll() { //窗口尺寸变化时，重新计算和缩放
          var browerWidth = window.innerWidth
          var browerHeight = window.innerHeight
          let zoomValue = 0
          let zoomValue1 = 0
          var flag = this.isFullFlag
          if (document.fullscreenElement) {
             zoomValue = browerWidth / 1640
             zoomValue1 = browerHeight / 880 //缩放比例计算
             this.fullText = "退出全屏"
          } else {
             zoomValue = browerWidth / this.baseWidth
             zoomValue1 = browerHeight / this.baseHeight //缩放比例计算
             this.fullText = "全屏显示"
          }
          document.getElementById("svgDiv").style.transform = "scale(" + zoomValue + "," + zoomValue1 + ")"
       },
       getSvg() {//页面初始化
          var dom = document.getElementsByClassName('app-main')[0]
          //var w1 = dom.style.width	//此api只能获取到内联样式的属性值
          //var w2 = dom.currentStyle.width  //此api虽然支持全部三种设置样式的方式，但是只支持IE
          let mac = uuidv4()
          //this.WsUrl = this.WsUrl + mac.replace(/-/g, "")
 
          this.initWebSocket()
          //this.width = window.getComputedStyle(dom).width	//此api支持IE、Chrome、Firefox的全部三种样式
          //this.height = window.getComputedStyle(dom).height	//此api支持IE、Chrome、Firefox的全部三种样式
          //this.heightDown = dom.clientHeight - 40
          //var w4 = dom.getBoundingClientRect().width //同样能获取及时的尺寸，支持IE、Chrome、Firefox,只是获取到的是数值不带单位
 
          //document.getElementById("svg1").getAttributeNode("width").value = this.width
          //document.getElementById("svg1").getAttributeNode("height").value = this.height
 
 
          // //绘制全部小车
          // this.CreateCarsAll()
 
          // //绘制格口
          // this.CreateChuteAll()
 
 
          // //绘制供包台
          // this.CreateSupplysAll()
 
          // setInterval(function () {
          //    Footer.methods.SetGaugeEchart((Math.random() * 2).toFixed(2) - 0, (Math.random() * 7).toFixed(2) - 0)
          // }, 2000)
       },
       //轨道绘制
       CreateRun(svgMain, x, y) {
          if (this.isOne) {//一层
             let r = 50
             let w = 1330
             let h = 460
             x += 5
             y += 10
             let d1 = "M " + x + " " + y + "  L " + (x - w) + " " + y + "  A" + r + " " + r + " 0 0 0 " + (x - w - r) + " " + (y + r) + "   L " + (x - w - r) + " " + (y + h + r) + "  A" + r + " " + r + " 0 0 0 " + (x - w) + " " + (y + h + r + r) + "  L " + x + " " + (y + h + r + r) + "   A" + r + " " + r + " 0 0 0 " + (x + r) + " " + (y + h + r) + "  L " + (x + r) + " " + (y + r) + "   A" + r + " " + r + " 0 0 0 " + x + " " + y + "  "
 
             y += 15
             r = 35
             let d2 = "M " + x + " " + y + "  L " + (x - w) + " " + y + "  A" + r + " " + r + " 0 0 0 " + (x - w - r) + " " + (y + r) + "   L " + (x - w - r) + " " + (y + h + r) + "  A" + r + " " + r + " 0 0 0 " + (x - w) + " " + (y + h + r + r) + "  L " + x + " " + (y + h + r + r) + "   A" + r + " " + r + " 0 0 0 " + (x + r) + " " + (y + h + r) + "  L " + (x + r) + " " + (y + r) + "   A" + r + " " + r + " 0 0 0 " + x + " " + y + "  "
 
             this.runPath1 = d2
             this.addPath(svgMain, 'run1', d1, 'none', '#7c9292')
             this.addPath(svgMain, 'run2', d2, 'none', '#7c9292')
          } else {//二层
             let r = 65
             let w = 1320
             let h = 450
 
             let d1 = "M " + x + " " + y + "  L " + (x - w) + " " + y + "  A" + r + " " + r + " 0 0 0 " + (x - w - r) + " " + (y + r) + "   L " + (x - w - r) + " " + (y + h + r) + "  A" + r + " " + r + " 0 0 0 " + (x - w) + " " + (y + h + r + r) + "  L " + x + " " + (y + h + r + r) + "   A" + r + " " + r + " 0 0 0 " + (x + r) + " " + (y + h + r) + "  L " + (x + r) + " " + (y + r) + "   A" + r + " " + r + " 0 0 0 " + x + " " + y + "  "
 
             y += 15
             r = 50
             let d2 = "M " + x + " " + y + "  L " + (x - w) + " " + y + "  A" + r + " " + r + " 0 0 0 " + (x - w - r) + " " + (y + r) + "   L " + (x - w - r) + " " + (y + h + r) + "  A" + r + " " + r + " 0 0 0 " + (x - w) + " " + (y + h + r + r) + "  L " + x + " " + (y + h + r + r) + "   A" + r + " " + r + " 0 0 0 " + (x + r) + " " + (y + h + r) + "  L " + (x + r) + " " + (y + r) + "   A" + r + " " + r + " 0 0 0 " + x + " " + y + "  "
 
             y += 5
             r = 45
             let d3 = "M " + x + " " + y + "  L " + (x - w) + " " + y + "  A" + r + " " + r + " 0 0 0 " + (x - w - r) + " " + (y + r) + "   L " + (x - w - r) + " " + (y + h + r) + "  A" + r + " " + r + " 0 0 0 " + (x - w) + " " + (y + h + r + r) + "  L " + x + " " + (y + h + r + r) + "   A" + r + " " + r + " 0 0 0 " + (x + r) + " " + (y + h + r) + "  L " + (x + r) + " " + (y + r) + "   A" + r + " " + r + " 0 0 0 " + x + " " + y + "  "
 
             y += 15
             r = 30
             let d4 = "M " + x + " " + y + "  L " + (x - w) + " " + y + "  A" + r + " " + r + " 0 0 0 " + (x - w - r) + " " + (y + r) + "   L " + (x - w - r) + " " + (y + h + r) + "  A" + r + " " + r + " 0 0 0 " + (x - w) + " " + (y + h + r + r) + "  L " + x + " " + (y + h + r + r) + "   A" + r + " " + r + " 0 0 0 " + (x + r) + " " + (y + h + r) + "  L " + (x + r) + " " + (y + r) + "   A" + r + " " + r + " 0 0 0 " + x + " " + y + "  "
 
             this.runPath1 = d2
 
             this.runPath2 = d4
 
             this.addPath(svgMain, 'run1', d1, 'none', '#7c9292')
             this.addPath(svgMain, 'run2', d2, 'none', '#7c9292')
             this.addPath(svgMain, 'run3', d3, 'none', '#7c9292')
             this.addPath(svgMain, 'run4', d4, 'none', '#7c9292')
          }
       },
       //绘制所有小车
       CreateCarsAll() {
          let svgMain = document.getElementById("svg1")
          if (this.isTwo) {
             let wit = 3120 / (this.CarNum / 2)
             for (let index = 1; index <= this.CarNum; index++) {
                if (index <= this.CarNum / 2) {
                   this.addRectCar(svgMain, index, wit, 15, 0, 0, "#7c9292", '1')
                   this.time1 += this.oneTime / this.CarNum * 2
                } else {
                   this.addRectCar(svgMain, index, wit, 15, 0, 0, "#7c9292", '2')
                   this.time2 += this.oneTime / this.CarNum * 2
                }
             }
          }
          else {
             let wit = 3120 / this.CarNum
             for (let index = 1; index <= this.CarNum; index++) {
                if (index <= this.CarNum) {
                   this.addRectCar(svgMain, index, wit, 15, 0, 0, "#7c9292", '1')
                   this.time1 += this.oneTime / this.CarNum
                }
             }
          }
       },
       //绘制所有格口
       CreateChuteAll() {
          let svgMain = document.getElementById("svg1")
          this.CreateChute(svgMain, 1482, 132, this.ChuteNum1, 1150, 1)
          this.CreateChute(svgMain, 88, 205, this.ChuteNum2, 430, 2)
          this.CreateChute(svgMain, 160, 738, this.ChuteNum3, 1150, 3)
          this.CreateChute(svgMain, 1566, 663, this.ChuteNum4, 430, 4)
 
          this.CreateChute(svgMain, 1482, 192, this.ChuteNum5, 1150, 5)
          this.CreateChute(svgMain, 152, 205, this.ChuteNum6, 430, 6)
          this.CreateChute(svgMain, 160, 676, this.ChuteNum7, 1150, 7)
          this.CreateChute(svgMain, 1502, 663, this.ChuteNum8, 430, 8)
       },
       //绘制所有供包台
       CreateSupplysAll() {
          let svgMain = document.getElementById("svg1")
          this.CreateSupplys(svgMain, 275, 148, this.SupplysNum1, 90, 1)
          this.CreateSupplys(svgMain, 1370, 770, this.SupplysNum2, 90, 2)
          this.CreateSupplys(svgMain, 275, 224, this.SupplysNum3, 90, 3)
          this.CreateSupplys(svgMain, 1370, 690, this.SupplysNum4, 90, 4)
       },
       //svg圆形绘制
       addCircle(svg, x, y, r, fill) {
          var circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle')
          circle.setAttribute('cx', x)
          circle.setAttribute('cy', y)
          circle.setAttribute('r', r)
          circle.setAttribute('fill', fill)
          svg.appendChild(circle)
       },
       //svg矩形绘制
       addRect(svg, id, w, h, x, y, fill, t, r) {
          var rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
          rect.setAttribute('id', id)
          rect.setAttribute('width', w)
          rect.setAttribute('height', h)
          rect.setAttribute('x', x)
          rect.setAttribute('y', y)
          rect.setAttribute('ry', '2')
          rect.setAttribute('fill', fill)
          rect.setAttribute('style', 'cursor:pointer')
          if (t) {
             rect.setAttribute('transform', 'rotate(' + r + ',' + x + ' ' + y + ')')
          }
 
          rect.addEventListener('click', () => {
             this.chuteClick(id)
          })
          svg.appendChild(rect)
       },
       //svg路径绘制
       addPath(svg, id, d, fill, stroke) {
          var path = document.createElementNS('http://www.w3.org/2000/svg', 'path')
          path.setAttribute('id', id)
          path.setAttribute('d', d)
          path.setAttribute('fill', fill)
          if (stroke) {
             path.setAttribute('stroke', stroke)
             path.setAttribute('stroke-width', '1')
          }
          path.addEventListener('click', () => {
             this.carClick(id)
          })
          svg.appendChild(path)
       },
       //svg文本绘制
       addText(svg, id, x, y, val, fill) {
          var text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          text.setAttribute('id', id);
          text.setAttribute('x', x);
          text.setAttribute('y', y);
          text.setAttribute('style', 'font-size:10px;cursor:pointer;');
          if (fill) {
             text.setAttribute('fill', fill)
          } else {
             text.setAttribute('fill', '#ffffff')
          }
          text.setAttribute('text-anchor', 'middle')
          text.setAttribute('alignment-baseline', 'middle')
          //text.setAttribute('innerHTML', val)
          text.innerHTML = val
          //text.setAttribute('v-on:click', 'this.carClick()')
          text.addEventListener('click', () => {
             this.chuteClick("chu" + id)
          })
          svg.appendChild(text)
       },
       //小车矩形绘制
       addRectCar(svg, id, w, h, x, y, fill, type) {
          var rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
          let idStr = 'car' + id
          if (type == '1') {
             //console.log("计算小车1：" + this.checkCar(id, 1))
             idStr = 'car' + this.checkCar(id, '1')
          } else {
             //console.log("计算小车2：" + this.checkCar(id - this.CarNum/2, 2))
             idStr = 'car' + this.checkCar(id, '2')
          }
          rect.setAttribute('id', idStr)
          rect.setAttribute('width', w)
          rect.setAttribute('height', h)
          rect.setAttribute('x', x)
          rect.setAttribute('y', y)
          rect.setAttribute('ry', '2')
          rect.setAttribute('fill', fill)
          //rect.setAttribute('style', 'cursor:pointer')
          svg.appendChild(rect)
          this.addAnimateMotion(id, type, idStr)
       },
       //小车轨道运行动画
       addAnimateMotion(id, type, idStr) {
          let dom = document.getElementById(idStr)
          var rect = document.createElementNS('http://www.w3.org/2000/svg', 'animateMotion')
          if (type == '1') {
             //rect.setAttribute('begin', this.time1 + "s")
             rect.setAttribute('path', this.runPath1)
             rect.setAttribute('end', this.time1 + "s")
          }
          else {
             //rect.setAttribute('begin', this.time2 + "s")
             rect.setAttribute('path', this.runPath2)
             rect.setAttribute('end', this.time2 + "s")
          }
          rect.setAttribute('id', "anicar" + id)
          rect.setAttribute('dur', this.oneTime + "s")
          rect.setAttribute('rotate', "auto")
          //rect.setAttribute('repeatCount', "indefinite")
          rect.setAttribute('repeatCount', "1")
          rect.setAttribute('fill', "freeze")
 
          if (this.isTwo) {
             if (id == 1 || id == (this.CarNum / 2 + 1)) {
                rect.setAttribute('end', "0.001s")
             }
          } else {
             if (id == 1) {
                rect.setAttribute('end', "0.001s")
             }
          }
 
          dom.appendChild(rect)
       },
       //绘制格口
       CreateChute(svgMain, x, y, numStr, width, type) {
          let ctNum = 0;
          let chuteList = numStr.split(';');
          let a = []
          chuteList.forEach(element => {
             if (element) {
                let statNum = element
                let endNum = element
                if (element.indexOf("-") != -1) {
                   let str = element.split('-')
                   statNum = str[0] - 0
                   endNum = str[1] - 0
                }
                for (let i = statNum; i <= endNum; i++) {
                   ctNum++
                   a.push(i)
                }
             }
          })
          let w = width / ctNum
 
          a.forEach(element => {
             switch (type) {
                case 1:
                case 5:
                   this.addRect(svgMain, 'chute' + element, w, 16, x, y, '#7c9292')
                   this.addText(svgMain, 'te' + element, x + w / 2, y + 9, element)
                   x -= w + 1
                   break
                case 3:
                case 7:
                   this.addRect(svgMain, 'chute' + element, w, 16, x, y, '#7c9292')
                   this.addText(svgMain, 'te' + element, x + w / 2, y + 9, element)
                   x += w + 1
                   break
                case 2:
                case 6:
                   this.addRect(svgMain, 'chute' + element, 18, w, x, y, '#7c9292')
                   this.addText(svgMain, 'te' + element, x + 9, y + w / 2 + 1, element)
                   y += w + 1
                   break
                default:
                   this.addRect(svgMain, 'chute' + element, 18, w, x, y, '#7c9292')
                   this.addText(svgMain, 'te' + element, x + 9, y + w / 2 + 1, element)
                   y -= w + 1
                   break
             }
 
          })
       },
       //绘制供包台
       CreateSupplys(svgMain, x, y, numStr, width, type) {
          let ctNum = 0;
          let List = numStr.split(';');
          let a = []
          List.forEach(element => {
             if (element) {
                let statNum = element
                let endNum = element
                if (element.indexOf("-") != -1) {
                   let str = element.split('-')
                   statNum = str[0] - 0
                   endNum = str[1] - 0
                }
                for (let i = statNum; i <= endNum; i++) {
                   ctNum++
                   a.push(i)
                }
             }
          })
          let w = width / ctNum
 
 
          a.forEach(element => {
             switch (type) {
                case 1:
                case 3:
                   this.CreateSupplyOne(svgMain, x, y, w, element)
                   x -= w + 5
                   break
                default:
                   this.CreateSupplyOne(svgMain, x, y, w, element)
                   x += w + 5
                   break
             }
 
          })
       },
       //绘制单个供包台
       CreateSupplyOne(svgMain, x, y, w, num) {
          let h = 20
          let r = 6
          let d = 'M' + x + ' ' + y + ' L' + (x + r) + ' ' + (y - h) + ' L' + (x + r + w) + ' ' + (y - h) + ' L' + (x + w) + ' ' + (y) + " Z"
          this.addPath(svgMain, 'supply', d, 'none', '#808080')
 
          let x1 = x + r / 2
          let y1 = y - h / 2
 
          d = 'M' + x1 + ' ' + y1 + ' L' + (x1 + r) + ' ' + (y1 - h) + ' L' + (x1 + r + w) + ' ' + (y1 - h) + ' L' + (x1 + w) + ' ' + (y1) + " Z"
          this.addPath(svgMain, 'supply', d, 'none', '#808080')
 
          let x2 = x + r / 3
          let y2 = y - h / 3
 
          d = 'M' + x + ' ' + y + ' L' + x2 + ' ' + y2 + ' L' + (x2 + 4) + ' ' + (y2 + 3) + ' L' + (x2 + w) + ' ' + y2 + ' L' + (x + w) + ' ' + y + " Z"
          this.addPath(svgMain, 'supply' + num, d, '#7c9292')
          this.addText(svgMain, 'supply', x + w / 4 * 3, y - h / 100 * 70, num, "black")
       },
       carClick(ss) {
 
       },
       //格口点击
       chuteClick(id) {
          // this.changeColor(id, '2')
          // let str = "354#" + id + "#1\r\n"
          // this.websocketsend(str)
          if (this.isMainIp) {
             let chuteColor = document.getElementById(id).attributes.fill.value
             //this.carRun()
             if (chuteColor == "#ff0000") {
                let str = "351#" + id.replace("chute", "") + "#0\r\n"
                this.websocketsend(str)
             }
          }
          this.scaleAll()
       },
       //小车操作
       carOperate() {
          if (document.fullscreenElement) {
             this.requestFullScreen()
          }
          this.carVisible = true
       },
       //禁用列表
       forbiddenList() {
          if (document.fullscreenElement) {
             this.requestFullScreen()
          }
          this.forbiddenVisible = true
       },
       //异常报警
       abnormalAlarm() {
          if (document.fullscreenElement) {
             this.requestFullScreen()
          }
          this.abnormalAlarmVisible = true
          return request({
           url: '/log/scada/alarm/listDay',
           method: 'post'
         }).then(response => {
          this.abnormalList = response.data.result
         }).catch(error => {
           this.$message({
             message: error,
             type: 'error',
             duration: 2 * 1000
           })
         })
       },
       //一键锁格
       chuteLockAllBtn() {
          if (document.fullscreenElement) {
             this.requestFullScreen()
          }
          this.form.chuteLockPwd = ""
          this.chuteLockVisible = true
       },
       //一键解锁
       chuteUnLockAllBtn() {
          if (document.fullscreenElement) {
             this.requestFullScreen()
          }
          this.form.chuteUnLockPwd = ""
          this.chuteUnLockVisible = true
       },
       //调试显示日志
       debugBtn(){
             this.isLog = !this.isLog
       },
       //小车锁定
       carLock() {
          var carNo = this.form.name
          let str = ''
          var carStr = carNo
          if (carStr.includes('*')) {
             carNo = carStr.replace('*', '')
             carNo = carNo - 0
             if (carNo && carNo > 0 && carNo <= this.CarNum) {
                //carNo = this.checkCar(carNo)
                if (this.form.number == "1") {
                   str = "352#" + carStr + "#1\r\n"
                } else {
                   str = "353#" + carStr + "#1\r\n"
                }
                this.websocketsend(str)
             } else {
                Message({
                   message: "请输入正确小车号！",
                   type: 'warning',
                   duration: 2 * 1000
                })
             }
          } else {
             carNo = carNo - 0
             if (carNo && carNo > 0 && carNo <= this.CarNum / 2 && this.isTwo) {
                //carNo = this.checkCar(carNo)
                if (this.form.number == "1") {
                   str = "352#" + carStr + "#1\r\n"
                } else {
                   str = "353#" + carStr + "#1\r\n"
                }
                this.websocketsend(str)
             } else if (carNo && carNo > 0 && carNo <= this.CarNum  && this.isOne) {
                //carNo = this.checkCar(carNo)
                if (this.form.number == "1") {
                   str = "352#" + carStr + "#1\r\n"
                } else {
                   str = "353#" + carStr + "#1\r\n"
                }
                this.websocketsend(str)
             }  else {
                Message({
                   message: "请输入正确小车号！",
                   type: 'warning',
                   duration: 2 * 1000
                })
             }
          }
 
       },
       //小车解锁
       carUnLock() {
          var carNo = this.form.name
          var carStr = carNo
          if (carStr.includes('*')) {
             carNo = carStr.replace('*', '')
             carNo = carNo - 0
             if (carNo && carNo > 0 && carNo <= this.CarNum) {
                //carNo = this.checkCar(carNo)
                let str = ''
                if (this.form.number == "1") {
                   str = "352#" + carStr + "#0\r\n"
                } else {
                   str = "353#" + carStr + "#0\r\n"
                }
                this.websocketsend(str)
             } else {
                Message({
                   message: "请输入正确小车号！",
                   type: 'warning',
                   duration: 2 * 1000
                })
             }
          } else {
             carNo = carNo - 0
             if (carNo && carNo > 0 && carNo <= this.CarNum / 2 && this.isTwo) {
                //carNo = this.checkCar(carNo)
                let str = ''
                if (this.form.number == "1") {
                   str = "352#" + carStr + "#0\r\n"
                } else {
                   str = "353#" + carStr + "#0\r\n"
                }
                this.websocketsend(str)
             }else if(carNo && carNo > 0 && carNo <= this.CarNum  && this.isOne)
             { //carNo = this.checkCar(carNo)
                let str = ''
                if (this.form.number == "1") {
                   str = "352#" + carStr + "#0\r\n"
                } else {
                   str = "353#" + carStr + "#0\r\n"
                }
                this.websocketsend(str)
             } else {
                Message({
                   message: "请输入正确小车号！",
                   type: 'warning',
                   duration: 2 * 1000
                })
             }
          }
 
       },
       //小车正转
       carForeward() {
          var carNo = this.form.name
          var carStr = carNo
          if (carStr.includes('*')) {
             carNo = carStr.replace('*', '')
          }
          if (carNo && carNo > 0 && carNo <= this.CarNum) {
             //carNo = this.checkCar(carNo)
             let str = ''
             if (this.form.number == "1") {
                str = "355#" + carStr + "#2#1\r\n"
             } else {
                str = "355#" + carStr + "#1#1\r\n"
             }
             this.websocketsend(str)
          } else {
             Message({
                message: "请输入正确小车号！",
                type: 'warning',
                duration: 2 * 1000
             })
          }
       },
       //小车反转
       carReversal() {
          var carNo = this.form.name
          var carStr = carNo
          if (carStr.includes('*')) {
             carNo = carStr.replace('*', '')
          }
          if (carNo && carNo > 0 && carNo <= this.CarNum) {
             //carNo = this.checkCar(carNo)
             let str = ''
             if (this.form.number == "1") {
                str = "355#" + carStr + "#2#2\r\n"
             } else {
                str = "355#" + carStr + "#1#2\r\n"
             }
             this.websocketsend(str)
          } else {
             Message({
                message: "请输入正确小车号！",
                type: 'warning',
                duration: 2 * 1000
             })
          }
       },
       //手动切换
       plcHand(type){
          let str = "360#" + type + "\r\n"
          this.websocketsend(str)
       },
       //PLC小车号
       plcCarNo(){
          var carNo = this.form.name
          var carStr = carNo
          if (carStr.includes('*')) {
             carNo = carStr.replace('*', '')
          }
          carNo = carNo - 0
          if (carNo && carNo -0 > 0 && carNo -0 <= this.CarNum) {
             let str = ""
             if (this.form.number == "1") {
                str = "361#" + carStr + "#" + this.upNo + "\r\n"
             } else {
                str = "361#" + carStr + "#" + this.downNo + "\r\n"
             }
             this.websocketsend(str)
          } else {
             Message({
                message: "请输入正确小车号！",
                type: 'warning',
                duration: 2 * 1000
             })
          }
       },
       plcRun(){
          let str = "362#1\r\n"
          this.websocketsend(str)
       },
       checkCar(carID, layer) {
          let retCarNum
          switch (this.carCountState) {
             case 1://无锡
                carID = carID - 0
                let carNo
                if (layer == '1') {
                   //上层
                   if (carID > 300 && carID <= 311) {
                      carNo = carID - 300 + 11 + 600
                   }
                   else {
                      carNo = carID + (parseInt((carID - 1) / 30) + 1) * 30
                   }
                }
                else {
                   carID = carID - this.CarNum / 2
                   //下层
                   if (carID > 300 && carID <= 311) {
                      carNo = carID - 300 + 600
                   }
                   else {
                      carNo = carID + parseInt((carID - 1) / 30) * 30
                   }
                }
                retCarNum = carNo
                break
             default:
                retCarNum = carID
                break
          }
          return retCarNum
          // if (this.isWuxi) {
          //    carID = carID - 0
          //    let carNo
          //    if (layer == '1') {
          //       //上层
          //       if (carID > 300 && carID <= 311) {
          //          carNo = carID - 300 + 11 + 600
          //       }
          //       else {
          //          carNo = carID + (parseInt((carID - 1) / 30) + 1) * 30
          //       }
          //    }
          //    else {
          //       //下层
          //       if (carID > 300 && carID <= 311) {
          //          carNo = carID - 300 + 600
          //       }
          //       else {
          //          carNo = carID + parseInt((carID - 1) / 30) * 30
          //       }
          //    }
          //    return carNo
          // } else {
          //    return carID
          // }
       },
       reverseCheckCar(carNo) {
          carNo = carNo - 0
          let carID, layer
          if (carNo > 630 && carNo <= 660) {
             layer = '1'
             carID = carNo - (parseInt((carNo - 631) / 30) + 1) * 30
          }
          else {
             layer = '2'
             carID = carNo - parseInt((carNo - 601) / 30) * 30
          }
          if (carID >= 612 && carID <= 623) {
             carID = carID + 300 - 11 - 600
          }
          else if (carID >= 601 && carID <= 611) {
             carID = carID + 300 - 600
          }
          return { carID, layer }
       },
       layChange(val) {
          this.$nextTick(() => {
             this.form.number = val
          })
       },
       //一键解锁
       chuteUnLockAll() {
          if (this.form.chuteUnLockPwd == "000000") {
             let str = "354#0\r\n"
             this.websocketsend(str)
             this.chuteUnLockVisible = false
          } else {
             Message({
                message: "请输入正确密码！",
                type: 'error',
                duration: 2 * 1000
             })
          }
 
          // if (this.isFullFlag) {
          //    this.requestFullScreen()
          // }
          // MessageBox.prompt('', '请输入验证密码！', {
          //    inputType: 'password',
          //    //inputValidator: validator,
          //    inputValidator: (val) => {
          //       if (val != "000000") {
          //          return false
          //       }
          //    },
          //    inputErrorMessage: '请输入正确密码！',
          //    confirmButtonText: '确定',
          //    showClose: false,
          //    closeOnPressEscape: false,
          //    closeOnClickModal: false,
          //    center: true
          // }).then(({ value }) => {
          //    let str = "354#0\r\n"
          //    this.websocketsend(str)
          //    //this.carRun()
          // }).catch(() => {   // 点击取消按钮后的操作
          //    MessageBox.close()
          // })
       },
       //一键锁格
       chuteLockAll() {
          if (this.form.chuteLockPwd == "000000") {
             let str = "354#1\r\n"
             this.websocketsend(str)
             this.chuteLockVisible = false
          } else {
             Message({
                message: "请输入正确密码！",
                type: 'error',
                duration: 2 * 1000
             })
          }
 
          // if (this.isFullFlag) {
          //    this.requestFullScreen()
          // }
          // MessageBox.prompt('', '请输入验证密码！', {
          //    inputType: 'password',
          //    //inputValidator: validator,
          //    inputValidator: (val) => {
          //       if (val != "000000") {
          //          return false
          //       }
          //    },
          //    inputErrorMessage: '请输入正确密码！',
          //    confirmButtonText: '确定',
          //    showClose: false,
          //    closeOnPressEscape: false,
          //    closeOnClickModal: false,
          //    center: true
          // }).then(({ value }) => {
          //    let str = "354#1\r\n"
          //    this.websocketsend(str)
          //    //this.carStop()
          // }).catch(() => {   // 点击取消按钮后的操作
          //    MessageBox.close()
          // })
       },
       onKeyDown(event) {
          if (event.keyCode === 27 || event.keyCode === 122) {
             // // ESC键或F11键被按下
             // if (document.fullscreenElement) {
             //    // 如果当前处于全屏模式，退出全屏
             //    document.exitFullscreen();
             //    this.isFullFlag = false
             // } else {
             //    // 否则进入全屏模式
             //    const element = document.documentElement;
             //    if (element.requestFullscreen) {
             //       element.requestFullscreen();
             //    } else if (element.webkitRequestFullscreen) {
             //       element.webkitRequestFullscreen();
             //    } else if (element.mozRequestFullScreen) {
             //       element.mozRequestFullScreen();
             //    } else if (element.msRequestFullscreen) {
             //       element.msRequestFullscreen();
             //    }
 
             //    this.isFullFlag = true
             // }
          }
       },
       //全屏显示
       requestFullScreen() {
          this.isFullFlag = !this.isFullFlag
          let element = document.getElementsByClassName('app-main')[0]//document.getElementById('svgDiv')//指定全屏区域元素
 
          //let element = element || document.documentElement
          if (!document.fullscreenElement) {
             //element.requestFullscreen()
             if (element.requestFullscreen) {
                element.requestFullscreen()
             } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen()
             } else if (element.mozRequestFullScreen) {
                element.mozRequestFullScreen()
             } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen()
             }
             this.fullText = "退出全屏"
          } else {
             //document.exitFullscreen()
             if (document.exitFullscreen) {
                document.exitFullscreen()
             } else if (document.msExitFullscreen) {
                document.msExitFullscreen()
             } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen()
             } else if (document.webkitCancelFullScreen) {
                document.webkitCancelFullScreen()
             }
             this.fullText = "全屏显示"
          }
       },
       //小车运行
       carRun(LayerNum) {
          let runTime1 = 0
          let runTime2 = 0
          for (let index = 1; index <= this.CarNum; index++) {
             if (this.isTwo) {
                if (index <= this.CarNum / 2) {
                   if (LayerNum == this.upNo) {
 
                      let dom = document.getElementById("anicar" + index)
                      dom.setAttribute('end', "")
                      dom.setAttribute('repeatCount', "indefinite")
                      dom.setAttribute('begin', runTime1 + "s")
                      runTime1 += this.oneTime / this.CarNum * 2
 
                      if (index == 1) {
                         dom.setAttribute('begin', "0.001s")
                      }
                   }
                } else {
                   if (LayerNum == this.downNo) {
 
                      let dom = document.getElementById("anicar" + index)
                      dom.setAttribute('end', "")
                      dom.setAttribute('repeatCount', "indefinite")
                      dom.setAttribute('begin', runTime2 + "s")
                      runTime2 += this.oneTime / this.CarNum * 2
                      if (index == (this.CarNum / 2 + 1)) {
                         dom.setAttribute('begin', "0.001s")
                      }
                   }
 
                }
 
             } else {
                if (index <= this.CarNum) {
 
                   let dom = document.getElementById("anicar" + index)
                   dom.setAttribute('end', "")
                   dom.setAttribute('repeatCount', "indefinite")
                   dom.setAttribute('begin', runTime2 + "s")
                   runTime2 += this.oneTime / this.CarNum
                   if (index == 1) {
                      dom.setAttribute('begin', "0.001s")
                   }
                }
             }
 
             //this.changeColor("car" + index, '1')
          }
       },
       //小车停止
       carStop(LayerNum) {
          let runTime1 = 0
          let runTime2 = 0
          for (let index = 1; index <= this.CarNum; index++) {
             if (this.isTwo) {
                if (index <= this.CarNum / 2) {
                   if (LayerNum == this.upNo) {
                      let dom = document.getElementById("anicar" + index)
                      dom.setAttribute('begin', "")
                      dom.setAttribute('repeatCount', "1")
 
                      dom.setAttribute('end', runTime1 + "s")
                      runTime1 += this.oneTime / this.CarNum * 2
                      if (index == 1) {
                         dom.setAttribute('end', "0.001s")
                      }
                   }
                } else {
                   if (LayerNum == this.downNo) {
                      let dom = document.getElementById("anicar" + index)
                      dom.setAttribute('begin', "")
                      dom.setAttribute('repeatCount', "1")
 
                      dom.setAttribute('end', runTime2 + "s")
                      runTime2 += this.oneTime / this.CarNum * 2
                      if (index == (this.CarNum / 2 + 1)) {
                         dom.setAttribute('end', "0.001s")
                      }
                   }
                }
 
 
             } else {
                if (index <= this.CarNum) {
                   let dom = document.getElementById("anicar" + index)
                   dom.setAttribute('begin', "")
                   dom.setAttribute('repeatCount', "1")
 
                   dom.setAttribute('end', runTime1 + "s")
                   runTime1 += this.oneTime / this.CarNum
                   
                   if (index == 1) {
                      dom.setAttribute('end', "0.001s")
                   }
                }
             }
             //this.changeColor("car" + index, '0')
 
          }
       },
       //变色通用方法
       changeColor(id, type) {
          let dom = document.getElementById(id)
          if (dom) {
             switch (type) {
                case '0'://待通信
                   dom.setAttribute('fill', "#7c9292")
                   break
                case '1'://解锁
                   dom.setAttribute('fill', "#008000")
                   break
                case '2'://锁定
                   dom.setAttribute('fill', "#ff0000")
                   break
                case '3'://满包
                   dom.setAttribute('fill', "#8b008b")
                   break
                case '4'://异常口
                   dom.setAttribute('fill', "#000000")
                   break
                case '5'://拦截件
                   dom.setAttribute('fill', "#0000ff")
                   break
                case '6'://载货中
                   dom.setAttribute('fill', "#ff8c00")
                   break
                default:
                   break
             }
          }
       },
       //socket链接
       initWebSocket() {
          if (typeof WebSocket === "undefined")
             return console.log("您的浏览器不支持websocket")
          this.websock = new WebSocket(this.WsUrl)
          this.websock.onmessage = this.websocketonmessage
          this.websock.onopen = this.websocketonopen
          this.websock.onerror = this.websocketonerror
          this.websock.onclose = this.websocketclose
 
       },
       websocketonopen() {
          console.log("建立链接"+this.WsUrl)
          this.changeColor("run2", '1')
          this.webSocketRunText = "已连接"
          //alert("111")
          // console.log("链接建立之后执行send方法发送数据")
          // let action ="测试消息 /r/n"
          // this.websocketsend(action)
       },
       websocketonerror() {
          console.log("断线重连")
          this.changeColor("run2", '2')
          this.webSocketRunText = "未连接"
          //链接建立失败重连
          // setTimeout(() => {
          //    this.initWebSocket()
          // }, 5000)
       },
       websocketonmessage(e) {
          if(this.isLog){
             console.log("接收消息：" + e.data)
          }
          if (e.data) {
             // let dateList = ""
             // if (e.data.includes("326#{") || e.data.includes("327#{")) {
             //    dateList = e.data
             // }else{
             //    dateList = e.data.split('|')
             // }
             let dateList = e.data.split('|')
             dateList.forEach(element => {
                if (element) {
                   //数据接收
                   const strArr = element.split('#')
                   const _socketTag = strArr[0]
                   switch (_socketTag) {
                      case "300":
                         this.SocketMsg300(strArr)
                         break
                      case "301":
                         this.SocketMsg301(strArr)
                         break
                      case "303":
                         this.SocketMsg303(strArr)
                         break
                      case "304":
                         this.SocketMsg304(strArr)
                         break
                      case "305":
                         this.SocketMsg305(strArr)
                         break
                      case "306":
                         this.SocketMsg306(strArr)
                         break
                      case "307":
                         this.SocketMsg307(strArr)
                         break
                      case "308":
                         this.SocketMsg308(strArr)
                         break
                      case "309":
                         this.SocketMsg309(strArr)
                         break
                      case "310":
                         this.SocketMsg310(strArr)
                         break
                      case "311":
                         this.SocketMsg311(strArr)
                         break
                      case "312":
                         this.SocketMsg312(strArr)
                         break
                      case "313":
                         this.SocketMsg313(strArr)
                         break
                      case "314":
                         this.SocketMsg314(strArr)
                         break
                      case "315":
                         this.SocketMsg315(strArr)
                         break
                      case "316":
                         this.SocketMsg316(strArr)
                         break
                      case "317":
                         this.SocketMsg317(strArr)
                         break
                      case "318":
                         this.SocketMsg318(strArr)
                         break
                      case "319":
                         this.SocketMsg319(strArr)
                         break
                      case "320":
                         this.SocketMsg320(strArr)
                         break
                      case "321":
                         this.SocketMsg321(strArr)
                         break
                      case "322":
                         this.SocketMsg322(strArr)
                         break
                      case "323":
                         this.SocketMsg323(strArr)
                         break
                      case "324":
                         this.SocketMsg324(strArr)
                         break
                      case "325":
                         this.SocketMsg325(strArr)
                         break
                      case "326":
                         this.SocketMsg326(strArr)
                         break
                      case "327":
                         this.SocketMsg327(strArr)
                         break
                      case "328":
                         this.SocketMsg328(strArr)
                         break
                      case "329":
                         this.SocketMsg329(strArr)
                         break
                      case "330":
                         this.SocketMsg330(strArr)
                         break
                      case "331":
                         this.SocketMsg331(strArr)
                         break
                      case "332":
                         this.SocketMsg332(strArr)
                         break
                      case "333":
                         this.SocketMsg333(strArr)
                         break
                      case "334":
                         this.SocketMsg334(strArr)
                         break
                      default:
                         break
                   }
                }
 
             })
 
          }
       },
       websocketsend(Data) {
          //数据发送
          if(this.isLog){
              console.log("数据发送", Data)
          }
          if (this.websock.readyState == WebSocket.OPEN) {
             if (this.isMainIp) {
                if (this.wcsRunText == "已连接") {
                   this.websock.send(Data)
                   Message({
                      message: "操作成功！",
                      type: 'success',
                      duration: 2 * 1000
                   })
                } else {
                   Message({
                      message: "WCS 连接没有建立成功！",
                      type: 'error',
                      duration: 2 * 1000
                   })
                }
             }
          } else {
             //alert("WebSocket 连接没有建立成功！")
             Message({
                message: "WebSocket 连接没有建立成功！",
                type: 'error',
                duration: 2 * 1000
             })
          }
          //this.websock.send(Data)
       },
       websocketclose(e) {
          //关闭
          console.log("断开链接", e)
          Message({
             message: "WebSocket 连接没有建立成功！",
             type: 'error',
             duration: 2 * 1000
          })
          this.changeColor("run2", '2')
          this.webSocketRunText = "未连接"
          //链接建立失败重连
          setTimeout(() => {
             this.initWebSocket()
          }, 5000)
       },
       SocketMsg300(strArr) {
       },
       SocketMsg301(strArr) {
          let _socketNo = strArr[1]
          let _socketFlag = strArr[2]
          let _socketLayer = strArr[3]
          if(_socketLayer == this.downNo && this.isTwo)
          {
             if(this.carCountState != 1)
             {
                _socketNo = _socketNo - 0 + this.CarNum/2
             }
          }
          if (_socketFlag == '1') {
             this.changeColor("car" + _socketNo, '6')
          } else {
             this.changeColor("car" + _socketNo, '1')
          }
       },
       SocketMsg303(strArr) {
          let _socketNo = strArr[1]
          this.exceedsMaxNum = _socketNo
       },
       SocketMsg304(strArr) {
          let _socketLayer = strArr[1]
          let _socketNo = strArr[2]
          if (_socketLayer == this.downNo) {
             this.scansNumDown = _socketNo
          } else {
             this.scansNum = _socketNo
          }
       },
       SocketMsg305(strArr) {
          let _socketNo = strArr[1]
          this.fallingNum = _socketNo
       },
       SocketMsg306(strArr) {
          let _socketNo = strArr[1]
          this.interceptNum = _socketNo
       },
       SocketMsg307(strArr) {
          let _socketNo = strArr[1]
          this.hypercycleNum = _socketNo
       },
       SocketMsg308(strArr) {
          let _socketNo = strArr[1]
          this.notConfiguredGrid = _socketNo
       },
       SocketMsg309(strArr) {
          let _socketNo = strArr[1]
          this.abnormalNum = _socketNo
       },
       SocketMsg310(strArr) {
          let _socketNo = strArr[1]
          this.notObtainedNum = _socketNo
       },
       SocketMsg311(strArr) {
          let _socketNo = strArr[1]
          this.cancelNum = _socketNo
       },
       SocketMsg312(strArr) {
          let _socketNo = strArr[1]
          let _socketFlag = strArr[2]
          if (_socketFlag == '1') {
             this.changeColor("supply" + _socketNo, '1')
          } else {
             this.changeColor("supply" + _socketNo, '0')
          }
       },
       SocketMsg313(strArr) {
          let _socketNo = strArr[1]
          let _socketFlag = strArr[2]
          let _socketLayer = strArr[3]
          if(_socketLayer == this.downNo && this.isTwo)
          {
             if (this.carCountState != 1) {
                _socketNo = _socketNo - 0 + this.CarNum / 2
             }
          }
          if (_socketFlag == '1') {
             this.changeColor("car" + _socketNo, '2')
          } else {
             this.changeColor("car" + _socketNo, '1')
          }
       },
       SocketMsg314(strArr) {
          let _socketNo = strArr[1]
          let _socketFlag = strArr[2]
          this.changeColor("chute" + _socketNo, _socketFlag)
       },
       SocketMsg315(strArr) {
 
       },
       SocketMsg316(strArr) {
          // let _socketNo = strArr[1]
          // let _socketFlag = strArr[2]
          // if(_socketFlag == '0')
          // {
          //    this.changeColor("supply" + _socketNo, '1')
          // }else{
          //    this.changeColor("supply" + _socketNo, '2')
          // }
       },
       SocketMsg317(strArr) {
          let _socketNo = strArr[1]
          let _socketFlag = strArr[2]
          let _socketLayer = strArr[3]
       },
       SocketMsg318(strArr) {
          let _socketNo = strArr[2] - 0
          let _socketLayer = strArr[1]
          if (this.Distinguish == 0) {
             if (this.isOne) {
                if (_socketLayer == this.upNo) {
                   this.speedDial = _socketNo
                   if (_socketNo > 0 && this.runningText == "设备停止运行" && this.isCreateCar) {
                      this.changeColor("role1", '1')
                      this.runningText = "设备运行中"
                      this.carRun(_socketLayer)
                   }
                   if (_socketNo == 0 && this.runningText == "设备运行中") {
                      this.changeColor("role1", '2')
                      this.runningText = "设备停止运行"
                      this.carStop(_socketLayer)
                   }
                } else {
                   this.speedDialDown = _socketNo
                   if (_socketNo > 0 && this.runningTextDown == "设备停止运行" && this.isCreateCar) {
                      this.changeColor("role2", '1')
                      this.runningTextDown = "设备运行中"
                      this.carRun(_socketLayer)
                   }
                   if (_socketNo == 0 && this.runningTextDown == "设备运行中") {
                      this.changeColor("role2", '2')
                      this.runningTextDown = "设备停止运行"
                      this.carStop(_socketLayer)
                   }
                }
             } else {
                this.speedDial = _socketNo
                if (_socketNo > 0 && this.runningText == "设备停止运行" && this.isCreateCar) {
                   this.changeColor("role1", '1')
                   this.runningText = "设备运行中"
                   this.carRun('1')
                }
                if (_socketNo == 0 && this.runningText == "设备运行中") {
                   this.changeColor("role1", '2')
                   this.runningText = "设备停止运行"
                   this.carStop('1')
                }
                this.speedDialDown = _socketNo
                if (_socketNo > 0 && this.runningTextDown == "设备停止运行" && this.isCreateCar) {
                   this.changeColor("role2", '1')
                   this.runningTextDown = "设备运行中"
                   this.carRun('2')
                }
                if (_socketNo == 0 && this.runningTextDown == "设备运行中") {
                   this.changeColor("role2", '2')
                   this.runningTextDown = "设备停止运行"
                   this.carStop('2')
                }
             }
          }else{
             if (_socketLayer == this.upNo) {
                   this.speedDial = _socketNo
                   if (_socketNo > 0 && this.runningText == "设备停止运行" && this.isCreateCar) {
                      this.changeColor("role1", '1')
                      this.runningText = "设备运行中"
                      this.carRun(_socketLayer)
                   }
                   if (_socketNo == 0 && this.runningText == "设备运行中") {
                      this.changeColor("role1", '2')
                      this.runningText = "设备停止运行"
                      this.carStop(_socketLayer)
                   }
                } else {
                   this.speedDialDown = _socketNo
                   if (_socketNo > 0 && this.runningTextDown == "设备停止运行" && this.isCreateCar) {
                      this.changeColor("role2", '1')
                      this.runningTextDown = "设备运行中"
                      this.carRun(_socketLayer)
                   }
                   if (_socketNo == 0 && this.runningTextDown == "设备运行中") {
                      this.changeColor("role2", '2')
                      this.runningTextDown = "设备停止运行"
                      this.carStop(_socketLayer)
                   }
                }
          }
 
          
          if (this.isOne) {
             Footer.methods.SetGaugeEchart(this.speedDialDown, this.sortingMinutesCarShareDown)
          } else {
             FooterTwo.methods.SetGaugeEchart(this.speedDial, this.speedDialDown, this.sortingMinutesCarShare, this.sortingMinutesCarShareDown)
          }
          this.scaleAll()
       },
       SocketMsg319(strArr) {
          let _socketNo = strArr[1]
          if (_socketNo == "1") {
             this.changeColor("run3", '1')
             this.wcsRunText = "已连接"
          } else {
             this.changeColor("run3", '2')
             this.wcsRunText = "未连接"
          }
       },
       SocketMsg320(strArr) {
          let _socketNo = strArr[2]  
          let _socketLayer = strArr[1]
          if (_socketLayer == this.downNo) {
             this.sortingMinutesCarShareDown = _socketNo
          }
          else {
             this.sortingMinutesCarShare = _socketNo
          }
          if (this.isOne) {
             Footer.methods.SetGaugeEchart(this.speedDialDown, this.sortingMinutesCarShareDown)
          } else {
             FooterTwo.methods.SetGaugeEchart(this.speedDial, this.speedDialDown, this.sortingMinutesCarShare, this.sortingMinutesCarShareDown)
          }
       },
       SocketMsg321(strArr) {
          let _socketNo = strArr[2]
          let _socketLayer = strArr[1]
          if (_socketLayer == this.downNo) {
             this.distanceDialDown = _socketNo
             if(this.isTwo && this.Distinguish == 0){
                this.distanceDial = _socketNo
             }
          }
          else {
             this.distanceDial = _socketNo
          }
       },
       SocketMsg322(strArr) {
          let _socketNo = strArr[2]
          let _socketLayer = strArr[1]
          if (_socketLayer == this.downNo) {
             this.nowStartDateDown = _socketNo
             if(this.isTwo && this.Distinguish == 0){
                this.nowStartDate = _socketNo
             }
          }
          else {
             this.nowStartDate = _socketNo
          }
       },
       SocketMsg323(strArr) {
          let _socketNo = strArr[1]
          this.CarNum = _socketNo
          if (!this.isCreateCar) {
             //绘制全部小车
             this.CreateCarsAll()
             this.isCreateCar = true
             if (this.speedDial > 0 && this.runningText == "设备停止运行") {
                this.changeColor("role1", '1')
                this.runningText = "设备运行中"
                this.carRun(this.upNo)
             }
             if (this.speedDialDown > 0 && this.runningTextDown == "设备停止运行") {
                this.changeColor("role2", '1')
                this.runningTextDown = "设备运行中"
                this.carRun(this.downNo)
             }
          }
          this.scaleAll()
       },
       SocketMsg324(strArr) {
          let _socketNo = strArr[1]
          this.ChuteNum = _socketNo
       },
       SocketMsg325(strArr) {
          let _socketNo = strArr[1]
          this.SupplysNum = _socketNo
       },
       SocketMsg326(strArr) {
          let _socketNo = strArr[1]
          let chuteJson = JSON.parse(_socketNo)
          this.ChuteNum1 = chuteJson.ChuteNum1
          this.ChuteNum2 = chuteJson.ChuteNum2
          this.ChuteNum3 = chuteJson.ChuteNum3
          this.ChuteNum4 = chuteJson.ChuteNum4
          this.ChuteNum5 = chuteJson.ChuteNum5
          this.ChuteNum6 = chuteJson.ChuteNum6
          this.ChuteNum7 = chuteJson.ChuteNum7
          this.ChuteNum8 = chuteJson.ChuteNum8
          if (!this.isCreateChute) {
             //绘制格口
             this.CreateChuteAll()
             this.isCreateChute = true
          }
          this.scaleAll()
       },
       SocketMsg327(strArr) {
          let _socketNo = strArr[1]
          let supplyJson = JSON.parse(_socketNo)
          this.SupplysNum1 = supplyJson.SupplysNum1
          this.SupplysNum2 = supplyJson.SupplysNum2
          this.SupplysNum3 = supplyJson.SupplysNum3
          this.SupplysNum4 = supplyJson.SupplysNum4
          if (!this.isCreateSupplys) {
             //绘制供包台
             this.CreateSupplysAll()
             this.isCreateSupplys = true
          }
          this.scaleAll()
       },
       SocketMsg328(strArr) {
          let _socketNo = strArr[1]
          this.sortingErrorNum = _socketNo
       },
       SocketMsg329(strArr) {
          let _socketNo = strArr[2]
          let _socketLayer = strArr[1]
          
          this.sortingMinutesDistance = _socketNo
          // if (_socketLayer == this.downNo) {
          //    this.sortingMinutesDistanceDown = _socketNo
          // }
          // else {
          //    this.sortingMinutesDistance = _socketNo
          // }
       },
       SocketMsg330(strArr) {
          let _socketNo = strArr[1]
          if (_socketNo == "0") {
             this.isMainIp = true
             let elm = document.getElementById("operate")
             elm.setAttribute("style", "display:back")
          }
       },
       SocketMsg331(strArr) {
          let _socketNo = strArr[2]
          let _socketLayer = strArr[1]
          if (_socketLayer == this.downNo) {
             this.forbidden.downCarStr = _socketNo
          } else {
             this.forbidden.upCarStr = _socketNo
          }
       },
       SocketMsg332(strArr) {
          let _socketNo = strArr[1]
          if (_socketNo == "1") {
             this.carCountState = 1//1 无锡小车号特殊处理,
          } else {
             this.carCountState = 0
          }
       },
       SocketMsg333(strArr) {
          let _socketNo = strArr[1]
          if (_socketNo == "0") {
             this.Distinguish = 0//0为一个PLC、1为两个PLC
             this.isOne = true
             this.isTwo = false
          } else if (_socketNo == "1") {
             this.Distinguish = 0//0为一个PLC、1为两个PLC
             this.isOne = false
             this.isTwo = true
          } else if (_socketNo == "2") {
             this.Distinguish = 1//0为一个PLC、1为两个PLC
             this.isOne = false
             this.isTwo = true
          }
          let svgMain = document.getElementById("svg1")
 
          //绘制轨道
          this.CreateRun(svgMain, 1495, 153)
       },
       SocketMsg334(strArr) {
          let _socketNo = strArr[1]
          if (_socketNo == "0") {
             this.isScadaStop = true
          } else if (_socketNo == "1") {
             this.isScadaStop = false
          } 
       },
       getBoxValue(typeCode) {
          return request({
             url: '/cfg/param/getBoxValue',
             method: 'get'
          }).then((response) => {
             this.systemOptions = JSON.parse(response.msg)
             if (typeCode !== undefined && typeCode != 0) {
                this.systemTypeCode = typeCode
                let dbInfo = this.systemOptions.find(option => option.dbCode === typeCode);
                this.WsUrl = 'ws://192.168.20.238:'+ dbInfo.exposePort +'/ws'
             }else
             {
                this.systemTypeCode =  this.systemOptions[0].dbCode
                this.WsUrl = 'ws://192.168.20.238:'+ this.systemOptions[0].exposePort +'/ws'
             }
             this.getSvg()
          });
       },
       handleOptionChange() {
          let typeCode = this.systemTypeCode
          let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;
          if (type == 'dws') {
             this.isWcs = false
          } else {
             this.isWcs = true
          }
 
        this.$router.push('/monitor/otherScada/scada/' + typeCode)
          
       },
 
 
    },
 
    destroyed() {
       //离开路由之后断开websocket连接
       this.websock.close()
    }
 }
 </script>