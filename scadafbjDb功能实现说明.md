# scadafbjDb.vue 功能实现说明

## 已完成的功能

### 1. 新增方案圈层选择框

**位置**: 在设备运行状态区域的内/外圈小车占有率下方添加了一个新的设备项

**实现内容**:
- 添加了 `circleLevel` 数据字段
- 创建了下拉选择框，包含两个选项：
  - "1内" (值为 "1")
  - "2外" (值为 "2")
- 设置为必填字段，带有红色星号标识
- 添加了表单验证规则，确保用户必须选择一个选项

**代码位置**:
```vue
<!-- 模板部分 -->
<div class="equipment_item">
    <div style="margin-bottom: 12px;">方案圈层：<span style="color: red;">*</span></div>
    <el-form ref="circleLevelForm" :model="{ circleLevel }" :rules="{ circleLevel: circleLevelRules }">
        <el-form-item prop="circleLevel">
            <el-select v-model="circleLevel" placeholder="请选择圈层" style="width: 100%;">
                <el-option label="1内" value="1"></el-option>
                <el-option label="2外" value="2"></el-option>
            </el-select>
        </el-form-item>
    </el-form>
</div>

<!-- 数据部分 -->
data() {
    return {
        circleLevel: '', // 新增方案圈层字段
        circleLevelRules: [
            { required: true, message: '请选择方案圈层', trigger: 'change' }
        ],
    }
}
```

### 2. URL参数截取方法

**功能**: 从当前页面URL中截取 `dbCode` 和 `port` 参数

**实现内容**:
- 创建了 `parseUrlParams()` 方法
- 使用 `URL` 和 `URLSearchParams` API 解析URL参数
- 设置了默认值：dbCode=5, port=9002（注意：此文件默认端口为9002）
- 在组件挂载时自动调用解析方法

**代码实现**:
```javascript
// 解析URL参数方法
parseUrlParams() {
    const url = window.location.href;
    const urlObj = new URL(url);
    const searchParams = urlObj.searchParams;
    
    // 截取dbCode和port参数
    this.urlParams.dbCode = searchParams.get('dbCode') || '5'; // 默认值为5
    this.urlParams.port = searchParams.get('port') || '9002'; // 默认值为9002
    
    console.log('解析到的URL参数:', this.urlParams);
},

// 数据存储
urlParams: { // 存储URL参数
    dbCode: '',
    port: ''
},
```

### 3. 右下角告警接口使用动态dbCode

**修改内容**:
- 将原来硬编码的 `dbCode=5` 改为使用从URL解析的动态值
- 同时修改了异常报警列表接口

**修改的接口**:
```javascript
// 右下角告警接口
async getAlarmList() {
    let res = await request({
        url: `/ffs/log/alarm/real/list?dbCode=${this.urlParams.dbCode}`,
        method: "get",
    })
    console.log(res, 'getAlarmList')
    this.errList = res.data.result
},

// 异常报警列表接口
abnormalAlarm() {
    this.abnormalAlarmVisible = true;
    return request({
        url: "/ffs/log/alarm/list",
        method: "post",
        data: {
            dbCode: this.urlParams.dbCode, // 使用动态dbCode
            // ... 其他参数
        }
    });
}
```

### 4. WebSocket连接使用动态端口号

**修改内容**:
- 将 `clientSocket()` 方法中的硬编码端口 `9002` 改为使用从URL解析的动态端口号

**代码修改**:
```javascript
//创立链接
clientSocket() {
    socket = new WebSocket(`ws://***************:${this.urlParams.port}/ws`);
    // ... 其他代码
}
```

### 5. 密码验证接口使用动态dbCode

**修改内容**:
- 在密码验证方法中使用动态的dbCode参数

**代码修改**:
```javascript
async requirePassword(flag = 'deviceControlPassword') {
    try {
        const res = await request({
            url: '/ffs/hardwareOperation/verify/password',
            method: 'get',
            params: {
                password: this.form.chuteUnLockPwd,
                dbCode: this.urlParams.dbCode, // 使用动态dbCode
                flag: flag
            }
        });
        return res.data;
    } catch (error) {
        console.error('密码验证失败:', error);
        return null;
    }
},
```

### 6. 圈层选择验证方法

**新增功能**:
- 添加了 `validateCircleLevel()` 方法用于验证圈层选择

**代码实现**:
```javascript
// 验证圈层选择
validateCircleLevel() {
    return new Promise((resolve, reject) => {
        this.$refs.circleLevelForm.validate((valid) => {
            if (valid) {
                resolve(true);
            } else {
                reject(false);
            }
        });
    });
},
```

## 使用方法

### URL参数格式
访问页面时可以通过URL参数指定数据库代码和端口号：
```
http://your-domain/path?dbCode=5&port=9002
```

### 参数说明
- `dbCode`: 数据库代码，用于告警接口和密码验证接口
- `port`: WebSocket连接端口号

### 默认值
如果URL中没有提供参数，将使用以下默认值：
- `dbCode`: 5
- `port`: 9002

## 与 scadafbj.vue 的区别

1. **默认端口号不同**:
   - `scadafbj.vue`: 默认端口 9006
   - `scadafbjDb.vue`: 默认端口 9002

2. **其他功能完全一致**:
   - 都支持URL参数解析
   - 都有方案圈层选择框
   - 都使用动态dbCode和port参数

## 注意事项

1. 圈层选择框为必填字段，用户必须选择一个选项
2. URL参数解析在组件挂载时执行，确保所有接口调用都能获取到正确的参数
3. 所有相关的接口都已更新为使用动态参数，保持了代码的一致性
4. 保留了默认值设置，确保在没有URL参数时系统仍能正常工作
5. 此文件的默认WebSocket端口为9002，与scadafbj.vue的9006不同
