<template>
  <div style="position: relative">
    <svg width="130%" height="100%">
      <g v-if="isOne">
        <text
          x="20"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.DeviceRunningStatus") }}：
        </text>
        <circle
          cx="125"
          cy="20"
          r="5"
          fill="red"
          id="run1"
        ></circle>
        <text
          x="140"
          y="24"
          style="font-size: 14px; font-weight: bold"
          id="text1"
        >
          {{ runningText }}
        </text>

        <text
          x="260"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.StartTime") }}：
        </text>
        <text
          x="350"
          y="24"
          style="font-size: 14px; font-weight: bold"
          id="text2"
        >
          {{ nowStartDate }}
        </text>

        <text
          x="540"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.TotalDistanceTraveledByDevice") }}：
        </text>
        <text
          x="670"
          y="24"
          style="font-size: 14px; font-weight: bold"
          id="text91"
        >
          {{ distanceDial }} km
        </text>

        <text
          x="810"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.RunningSpeed") }}：
        </text>
        <text
          x="880"
          y="24"
          style="font-size: 14px; font-weight: bold"
          id="text3"
        >
          {{ speedDial }} m/s
        </text>

        <text
          x="980"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.CartOccupancyRate") }}：
        </text>
        <text
          x="1070"
          y="24"
          style="font-size: 14px; font-weight: bold"
          id="text4"
        >
          {{ sortingMinutesCarShare }}%
        </text>
      </g>
      <g v-if="isTwo">
        <text
          x="20"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
          }}{{ $t("scada.DeviceRunningStatus") }}：
        </text>
        <text
          x="20"
          y="50"
          style="font-size: 14px; font-weight: bold"
          id="text1"
        >
          {{ runningText }}
        </text>
        <text
          x="20"
          y="74"
          style="font-size: 14px; font-weight: bold"
          id="text1"
        >
          {{ runningTextDown }}
        </text>

        <text
          x="220"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
          }}{{ $t("scada.StartTime") }}：
        </text>
        <text
          x="220"
          y="50"
          style="font-size: 14px; font-weight: bold"
          id="text2"
        >
          {{ nowStartDate }}
        </text>

        <text
          x="220"
          y="74"
          style="font-size: 14px; font-weight: bold"
          id="text1"
        >
          {{ nowStartDateDown }}
        </text>

        <text
          x="380"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
          }}{{ $t("scada.UpperLevelRunningSpeed") }}：
        </text>
        <text
          x="380"
          y="50"
          style="font-size: 14px; font-weight: bold"
          id="text3"
        >
          {{ speedDial }} m/s
        </text>

        <text
          x="380"
          y="74"
          style="font-size: 14px; font-weight: bold"
          id="text3"
        >
          {{ speedDialDown }} m/s
        </text>

        <text
          x="620"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
          }}{{ $t("scada.TotalDistanceTraveledByDevice") }}：
        </text>
        <text
          x="620"
          y="50"
          style="font-size: 14px; font-weight: bold"
          id="text4"
        >
          {{ distanceDial }}%
        </text>

        <text
          x="620"
          y="74"
          style="font-size: 14px; font-weight: bold"
          id="text4"
        >
          {{ distanceDialDown }}%
        </text>

        <text
          x="850"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
          }}{{ $t("scada.CartOccupancyRate") }}：
        </text>
        <text
          x="850"
          y="50"
          style="font-size: 14px; font-weight: bold"
          id="text4_2"
        >
          {{ sortingMinutesCarShare }}%
        </text>
        <text
          x="850"
          y="74"
          style="font-size: 14px; font-weight: bold"
          id="text4_2"
        >
          {{ sortingMinutesCarShareDown }}%
        </text>
        <text
          x="1030"
          y="24"
          style="font-size: 14px"
          fill="#7e7e7e"
        >
          {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
          }}{{ $t("scada.PlCConnectionStatus") }}：
        </text>
        <text
          x="1030"
          y="50"
          style="font-size: 14px; font-weight: bold"
          id="text91"
        >
          {{ UpperLevelConnectionStatus }}
        </text>
        <text
          x="1030"
          y="74"
          style="font-size: 14px; font-weight: bold"
          id="text91"
        >
          {{ LowerLevelConnectionStatus }}
        </text>
      </g>
    </svg>
  </div>
</template>

<script>
export default {
  name: 'ScadaHeader',
  props: {
    isOne: {
      type: Boolean,
      default: false
    },
    isTwo: {
      type: Boolean,
      default: false
    },
    runningText: {
      type: String,
      default: ''
    },
    nowStartDate: {
      type: String,
      default: ''
    },
    distanceDial: {
      type: [String, Number],
      default: 0
    },
    speedDial: {
      type: [String, Number],
      default: 0
    },
    sortingMinutesCarShare: {
      type: [String, Number],
      default: 0
    },
    runningTextDown: {
      type: String,
      default: ''
    },
    nowStartDateDown: {
      type: String,
      default: ''
    },
    speedDialDown: {
      type: [String, Number],
      default: 0
    },
    distanceDialDown: {
      type: [String, Number],
      default: 0
    },
    sortingMinutesCarShareDown: {
      type: [String, Number],
      default: 0
    },
    UpperLevelConnectionStatus: {
      type: String,
      default: ''
    },
    LowerLevelConnectionStatus: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped>
/* 可以根据需要添加样式 */
</style> 