<template>
  <div class="login-container">
    <div class="ossLogo"></div>
    <div class="inputBg">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on"
      label-position="left">

      <div class="title-container">
        <h3 class="title">{{$t('login.title')}}</h3>
      </div>

      <el-dropdown szie="mini" class="changeLang" @command="handleCommand">
          <span>
            <font color="#191919" size="3" face="verdana" style="border: 1px solid rgba(25, 25, 25, 1);">{{$t('login.changelanguage')}}</font>
          </span>
          <el-dropdown-menu slot="dropdown" >
            <el-dropdown-item command="zh">{{ $t('login.chinese') }}</el-dropdown-item>
            <el-dropdown-item command="th">{{ $t('login.thai') }}</el-dropdown-item>
            <el-dropdown-item command="en">{{ $t('login.english') }}</el-dropdown-item>
            <el-dropdown-item command="vi">{{ $t('login.vi') }}</el-dropdown-item>
            <el-dropdown-item command="es">{{ $t('login.es') }}</el-dropdown-item>
            <el-dropdown-item command="pt">{{ $t('login.pt') }}</el-dropdown-item>
            <el-dropdown-item command="ph">{{ $t('login.ph') }}</el-dropdown-item>
            <el-dropdown-item command="id">{{ $t('login.id') }}</el-dropdown-item>
            <el-dropdown-item command="ms">{{ $t('login.ms') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

      <el-form-item prop="account">
        <span class="svg-container">
          <svg-icon icon-class="loginuse" />
        </span>
        <el-input :key="accountType" ref="account" v-model="loginForm.account" :type="accountType" :placeholder="$t('login.plsuaername')"
          name="account" tabindex="1" auto-complete="on" />
      </el-form-item>

      <el-form-item prop="password">
        <span class="svg-container">
          <svg-icon icon-class="loginlock" />
        </span>
        <el-input :key="passwordType" ref="password" v-model="loginForm.password" :type="passwordType" :placeholder="$t('login.plspassword')"
          name="password" tabindex="2" auto-complete="on" @keyup.enter.native="handleLogin" />
        <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="passwordType === 'password' ? 'password2' : 'password1'" />
        </span>
      </el-form-item>

      <el-card class="cover" v-if="isCard">
      <slide-verify
              :l="42"
              :r="10"
              :w="310"
              :h="155"
              :imgs="puzzleImglist"
              :slider-text="$t('login.SwipeRight')"
              @success="onSuccess"
              @fail="onFail"
              @refresh="onRefresh">
      </slide-verify>
    </el-card>


      <!-- <el-form-item prop="code">
        <el-input :key="codeType" ref="code" v-model="loginForm.code" :type="codeType" :placeholder="$t('login.code')" name="code"
          tabindex="1" auto-complete="on" style="width: 63%"/>
          <div class="login-code">
          <img :src="verifyImg" @click="getVerify" class="login-code-img"/>
        </div>
      </el-form-item> -->

      <el-button :loading="loading" size="medium" type="primary" style="width:100%;margin-bottom:30px;font-size:15px"
        @click.native.prevent="handleLogin">{{$t('login.login')}}</el-button>

    </el-form>
  </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import { resetRouter } from '@/router'
export default {
  name: 'Login',
  data() {
    const validateAccount = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('login.plsuaername')))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('login.plspassword')))
      } else {
        callback()
      }
    }
    const validateCode = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('login.plscode')))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        account: '',
        password: '',
        code: '',
        uuid: '',
        language: ''
      },
      loginRules: {
        account: [{ required: true, trigger: 'blur', validator: validateAccount }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        code: [{ required: true, trigger: 'blur', validator: validateCode }]
      },
      loading: false,
      accountType: 'account',
      passwordType: 'password',
      codeType: 'code',
      redirect: undefined,
      verifyImg: '',
      loginAdmin:{},
      isCard: true,
      puzzleImglist:[
        require('../../assets/images/1.jpg'),
        require('../../assets/images/2.jpg'),
        require('../../assets/images/3.jpg'),
        require('../../assets/images/4.jpg'),
        require('../../assets/images/5.jpg')
      ]
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  mounted() {
    //this.getVerify();
    this.isCard = false
  },
  methods: {
     // 切换语言
     handleCommand(command) {
      Cookies.set('locale', command);
      // 切换语言
      this.$i18n.locale = Cookies.get('locale')//command
      resetRouter()
      console.log(this.$t('language.zh'))
      window.location.reload()
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    onSuccess() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.loginForm.language = Cookies.get('locale')
          console.log('this.loginForm', this.loginForm)
          this.$store.dispatch('user/login', this.loginForm).then(() => {
            this.loading = false
            Cookies.set('account', this.loginForm.account);
            this.$router.push({ path: this.redirect || '/' })
          }).catch((err) => {
            this.$message({
              message: 'Login failed：' + err,
              type: 'warning',
              duration: 2 * 1000
            })
            this.getVerify();
            this.loading = false
            this.isCard = false
          })
        } else {
          this.$message({
            message: this.$t('common.Notempty'),
            type: 'warning',
            duration: 2 * 1000
          })
          this.getVerify();
          return false
        }
      })
    },
    getVerify() {
      this.$store.dispatch('user/captchaImage').then(() => {
        let baseImg = this.$store.state.user.img;
        //this.verifyImg = baseImg;
        const src = "data:image/png;base64," + window.atob(baseImg);
        this.verifyImg = src;
        this.loginForm.uuid = this.$store.state.user.uuid;
        //alert(src);
      }).catch((err) => {
        this.$message({
          message: 'Acquisition failed：' + err,
          type: 'warning',
          duration: 2 * 1000
        })
      })
    },
    handleLogin() {   //滑块验证通过之后触发的
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          console.log("loginForm", this.loginForm)
          this.isCard = true
        } else {
          this.$message({
            message: this.$t('common.Notpassword'),
            type: 'warning',
            duration: 2 * 1000
          })
          this.getVerify();
          return false
        }
      })
      // this.$notify.success("登录成功")
      // this.$router.push('/')
    },
    onFail() {

    },
    onRefresh() {
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #f2f3f5;
$light_gray: #191919;
$cursor: #191919;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    // height: 47px;
    width: 85%;

    input {
      background: #f2f3f5;
      border: 0px;
      //-webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      // height: 47px;
      font-weight: 600;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    // border: 1px solid rgba(0, 0, 0,0.1);
    background: #f2f3f5;
    border-radius: 5px;
    color: $light_gray;
    border: 1px solid transparent;

    &:focus-within {
      border: 1px solid #2d89ff;
    }
  }
}

.cover {
  width: fit-content;
  background-color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}
</style>

<style lang="scss" scoped>
$bg: #f2f3f5;
$dark_gray: #191919;
$light_gray: #eee;

.login-container {
  width: 100%;
  height: 100%;
  background-image: url("../../assets/login_images/aosuosi.png");
  background-size: 100% 100%;
  // background-repeat: no-repeat;
  background-position: center;
  position: relative;
  .ossLogo {
    background-image: url("../../assets/login_images/logo.png");
    background-size: 100% 100%;
    width: 272px;
    height: 138px;
    position: relative;
    top: 40px;
    left: 50px;
  }
  .inputBg {
    background-image: url("../../assets/login_images/inputBg.png");
    background-size: 100% 100%;
    width: 502px;
    height: 501px;
    background-position: center;
    position: absolute;
    right: 28vh;
    top: calc(50vh - 248px);
    display: flex;
    justify-content: center;
  }
  .login-form {
    position: absolute;
    width: 400px;
    max-width: 100%;
    top: 9vh;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #283443;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $light_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 30px;
      color: #2d89ff;
      margin: 0px auto 30px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: #191919;
    cursor: pointer;
    user-select: none;
  }

  .login-code {
    width: 33%;
    height: 38px;
    float: right;
    img {
      cursor: pointer;
      vertical-align: middle;
    }
  }

  .login-code-img {
    height: 47px;
    width: 135px;
  }
}
</style>
