import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

import Layout from "@/layout";
import i18n from "../lang";
export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true
  },
  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true
  },
  {
    path: "/scada",
    component: () => import("@/views/sysMonitor/scadaBox"),
    hidden: true
  },
  {
    path: "/scadajcd/:dbCode?/:port?",
    component: () => import("@/views/sysMonitor/scadajcd"),
    hidden: true
  },
  {
    path: "/scadafbj/:dbCode?/:port?",
    component: () => import("@/views/sysMonitor/scadafbj"),
    hidden: true
  },
  {
    path: "/scadafbjDb/:dbCode?/:port?",
    component: () => import("@/views/sysMonitor/scadafbjDb"),
    hidden: true
  },
  {
    path: "/scadancjcd/:dbCode?/:port?",
    component: () => import("@/views/sysMonitor/scadancjcd"),
    hidden: true
  },
  {
    path: "/",
    component: Layout,
    redirect: "/home",
    children: [
      {
        path: "home",
        name: "Home",
        component: () => import("@/views/home/<USER>"),
        meta: { title: i18n.t("sidebar.Home"), icon: "sy", affix: true }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  //{ path: '*', redirect: '/home', hidden: true },
  {
    path: "/syscfg/dict-data",
    component: Layout,
    hidden: true,
    permissions: ["syscfg:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/syscfg/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/syscfg/dict" },
        key: to => to.params.dictId
      }
    ]
  },
  // {
  //   path: '/monitor/job-log',
  //   component: Layout,
  //   hidden: true,
  //   children: [
  //     {
  //       path: 'joblog/:jobId(\\d+)',
  //       component: () => import('@/views/sysMonitor/joblog'),
  //       name: 'joblog',
  //       meta: { title: '调度日志', activeMenu: '/sysMonitor/job' }
  //     }
  //   ]
  // },
  {
    path: "/monitor/otherScada",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "scada/:typeCode(\\d+)",
        component: () => import("@/views/sysMonitor/scadaBox"),
        name: "scada",
        meta: { title: "SCADA监控", activeMenu: "/sysMonitor/scadaBox" }
      }
    ]
  }
];

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
