<template>
  <!-- 所有从scada.vue迁移过来的dialog框 22-->
  <div>
    <!-- 这里放置所有的dialog组件 -->
    <el-dialog
      v-for="dialog in dialogs"
      :key="dialog.id"
      :visible.sync="dialog.visible"
      :title="dialog.title"
      :width="dialog.width || '30%'"
      :before-close="() => closeDialog(dialog.id)"
    >
      <component
        :is="dialog.component"
        v-bind="dialog.props"
        @close="closeDialog(dialog.id)"
        @success="(data) => handleDialogSuccess(dialog.id, data)"
      ></component>
    </el-dialog>

    <!-- 禁用列表对话框 -->
    <el-dialog
      :title="$t('scada.DisabledList')"
      :visible.sync="dialogVisible.forbiddenVisible"
      width="25%"
      append-to-body
      @closed="$emit('update:forbiddenVisible', false)"
    >
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('scada.DisableCart')" v-if="isOne">
          <el-input
            v-model="forbidden.downCarStr"
            type="textarea"
            :disabled="true"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('scada.UpperLevelCart') + ':'" v-if="isTwo">
          <el-input
            v-model="forbidden.upCarStr"
            type="textarea"
            :disabled="true"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$t('scada.LowerLevelCart') + ':'" v-if="isTwo">
          <el-input
            v-model="forbidden.downCarStr"
            type="textarea"
            :disabled="true"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog('forbiddenVisible')">{{
          $t("scada.Close")
        }}</el-button>
      </span>
    </el-dialog>

    <!-- 一键解锁对话框 -->
    <el-dialog
      :title="$t('scada.OneKeyUnlock')"
      :visible.sync="dialogVisible.chuteUnLockVisible"
      width="25%"
      append-to-body
      @closed="$emit('update:chuteUnLockVisible', false)"
    >
      <div
        v-if="cellNum"
        style="
          text-align: center;
          font-size: 18px;
          font-weight: bold;
          margin: -10px 0 10px 0;
        "
      >
        {{ $t("scada.Unlock")
        }}<span style="color: rgb(121, 190, 124)">{{ cellNum }}</span
        >{{ $t("common.Chute") }}
      </div>
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item :label="$t('scada.VerificationPassword') + ':'">
          <el-input v-model="form.chuteUnLockPwd" type="password"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('chute-unlock-all')" type="danger">{{
          $t("common.Confirm")
        }}</el-button>
        <el-button @click="closeDialog('chuteUnLockVisible')">{{
          $t("common.Close")
        }}</el-button>
      </span>
    </el-dialog>

    <!-- 一键锁格口对话框 -->
    <el-dialog
      :title="$t('scada.OneKeyLockSlot')"
      :visible.sync="dialogVisible.chuteLockVisible"
      width="25%"
      append-to-body
      @closed="$emit('update:chuteLockVisible', false)"
    >
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item :label="$t('scada.VerificationPassword') + ':'">
          <el-input v-model="form.chuteLockPwd" type="password"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('chute-lock-all')" type="danger">{{
          $t("common.Confirm")
        }}</el-button>
        <el-button @click="closeDialog('chuteLockVisible')">{{
          $t("common.Close")
        }}</el-button>
      </span>
    </el-dialog>

    <!-- 小车操作对话框 -->
    <el-dialog
      :title="$t('scada.CartOperation')"
      :visible.sync="dialogVisible.carVisible"
      width="30%"
      append-to-body
      @closed="$emit('update:carVisible', false)"
    >
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('scada.CartNumber')">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item :label="$t('scada.FloorNumber')" v-if="isTwo">
          <el-radio-group v-model="form.number" @change="handleLayChange">
            <el-radio label="1">{{ $t("scada.UpperLevel") }}</el-radio>
            <el-radio label="2">{{ $t("scada.LowerLevel") }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('car-lock')" type="danger">{{
          $t("scada.Lock")
        }}</el-button>
        <el-button @click="$emit('car-unlock')" type="success">{{
          $t("scada.Unlock")
        }}</el-button>
        <el-button
          @click="$emit('plc-hand', '1')"
          type="primary"
          v-if="isScadaStop"
          >{{ $t("scada.ManualSwitchStart") }}</el-button
        >
        <el-button
          @click="$emit('plc-hand', '0')"
          type="warning"
          v-if="!isScadaStop"
          >{{ $t("scada.ManualSwitchClose") }}</el-button
        >
        <el-button @click="$emit('plc-car-no')" type="primary">{{
          $t("scada.CartNumber")
        }}</el-button>
        <el-button @click="$emit('car-run')" type="warning">{{
          $t("scada.Run")
        }}</el-button>
        <el-button @click="closeDialog('carVisible')">{{
          $t("common.Close")
        }}</el-button>
      </span>
    </el-dialog>

    <!-- 异常报警对话框 -->
    <el-dialog
      :title="$t('scada.AbnormalAlarm')"
      :visible.sync="dialogVisible.abnormalAlarmVisible"
      width="60%"
      append-to-body
      @closed="$emit('abnormal-close')"
    >
      <div class="AbnormalStyle fontWidth">
        {{ $t("scada.StartTime") }}:<el-date-picker
          v-model="AbnormalAlarmTime.startTime"
          type="datetime"
          :placeholder="$t('common.SelectStartTime')"
          :clearable="false"
          @change="(e) => handleChangeAbnormal(e, 'startTime')"
        >
        </el-date-picker>
        <span style="margin-left: 20px">{{ $t("scada.EndTime") }}</span
        >:<el-date-picker
          v-model="AbnormalAlarmTime.endTime"
          type="datetime"
          :clearable="false"
          :placeholder="$t('common.SelectEndTime')"
          @change="(e) => handleChangeAbnormal(e, 'endTime')"
        >
        </el-date-picker>
      </div>
      <div class="FaultLevelSty AbnormalStyle">
        <div
          style="display: flex; align-items: center"
          class="FaultLevelSty fontWidth"
        >
          <span class="fontWidth">
            {{ $t("common.Source") }}:
            <el-select
              v-model="AbnormalAlarmInfo.logSource"
              style="width: 95px"
            >
              <el-option
                v-for="dict in dicts"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </span>
          <div style="margin-left: 20px">
            {{ $t("scada.SearchCriteria") }}:
            <el-input
              :placeholder="$t('common.PleaseEnterContent')"
              v-model="AbnormalAlarmInfo.SearchCriteria"
              style="width: 120px"
              @input="(e) => handleChangeAbnormal(e, 'SearchCriteria')"
            />
          </div>
        </div>
        <div>
          <el-button @click="$emit('abnormal-alarm')" type="primary">{{
            $t("scada.Select")
          }}</el-button>
        </div>
      </div>
      <slot name="abnormal-list"></slot>
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('debug-btn')" type="primary" v-if="isLog">{{
          $t("scada.DebugStart")
        }}</el-button>
        <el-button @click="$emit('debug-btn')" type="warning" v-if="!isLog">{{
          $t("common.DebugClose")
        }}</el-button>
        <el-button @click="$emit('abnormal-close')">{{
          $t("common.Close")
        }}</el-button>
      </span>
    </el-dialog>

    <!-- 清除公里数对话框 -->
    <el-dialog
      :title="$t('scada.ClearTheKilometers')"
      :visible.sync="dialogVisible.clearTheKilometersVisible"
      width="25%"
      append-to-body
      @closed="$emit('update:clearTheKilometersVisible', false)"
    >
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item :label="$t('scada.VerificationPassword')">
          <el-input v-model="form.chuteLockPwd" type="password"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('clear-kilometers')" type="danger">{{
          $t("common.Confirm")
        }}</el-button>
        <el-button @click="closeDialog('clearTheKilometersVisible')">{{
          $t("common.Close")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// 导入所有dialog子组件
import DetailDialog from "./DetailDialog.vue";

export default {
  name: "ScadaDialogs",
  components: {
    DetailDialog,
    // 添加其他dialog组件
  },
  props: {
    dialogs: {
      type: Array,
      default: () => [],
    },
    // 对话框可见性
    forbiddenVisible: {
      type: Boolean,
      default: false,
    },
    chuteUnLockVisible: {
      type: Boolean,
      default: false,
    },
    chuteLockVisible: {
      type: Boolean,
      default: false,
    },
    carVisible: {
      type: Boolean,
      default: false,
    },
    abnormalAlarmVisible: {
      type: Boolean,
      default: false,
    },
    clearTheKilometersVisible: {
      type: Boolean,
      default: false,
    },
    // 表单数据
    form: {
      type: Object,
      default: () => ({
        name: "",
        number: "2",
        chuteLockPwd: "",
        chuteUnLockPwd: "",
      }),
    },
    // 其他数据
    cellNum: {
      type: String,
      default: "",
    },
    forbidden: {
      type: Object,
      default: () => ({
        upCarStr: "",
        downCarStr: "",
      }),
    },
    isOne: {
      type: Boolean,
      default: false,
    },
    isTwo: {
      type: Boolean,
      default: true,
    },
    isScadaStop: {
      type: Boolean,
      default: true,
    },
    isLog: {
      type: Boolean,
      default: false,
    },
    AbnormalAlarmTime: {
      type: Object,
      default: () => ({
        startTime: "",
        endTime: "",
      }),
    },
    AbnormalAlarmInfo: {
      type: Object,
      default: () => ({
        logSource: " ",
        loading: false,
        SearchCriteria: "",
        AbnormalAlarmList: [
          { name: "设备报警", id: 1 },
          { name: "业务报警", id: 2 },
        ],
        clickGroup: "设备报警",
        FaultLevelValueList: [false, false, true],
        FaultLevelList: [],
        abnormalListColumn: [],
        totalResult: 0,
        currentPage: 1,
        pageSize: 10,
        messageNum: 0,
      }),
    },
    dicts: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 内部管理对话框可见性
      dialogVisible: {
        forbiddenVisible: this.forbiddenVisible,
        chuteUnLockVisible: this.chuteUnLockVisible,
        chuteLockVisible: this.chuteLockVisible,
        carVisible: this.carVisible,
        abnormalAlarmVisible: this.abnormalAlarmVisible,
        clearTheKilometersVisible: this.clearTheKilometersVisible,
      },
    };
  },
  watch: {
    // 监听外部传入的可见性变化
    forbiddenVisible(val) {
      this.dialogVisible.forbiddenVisible = val;
    },
    chuteUnLockVisible(val) {
      console.log("qeqi");
      this.dialogVisible.chuteUnLockVisible = val;
    },
    chuteLockVisible(val) {
      this.dialogVisible.chuteLockVisible = val;
    },
    carVisible(val) {
      this.dialogVisible.carVisible = val;
    },
    abnormalAlarmVisible(val) {
      this.dialogVisible.abnormalAlarmVisible = val;
    },
    clearTheKilometersVisible(val) {
      this.dialogVisible.clearTheKilometersVisible = val;
    },
  },
  methods: {
    closeDialog(dialogId) {
      // 如果是动态生成的对话框
      if (typeof dialogId === "string") {
        // 更新内部对话框状态
        if (this.dialogVisible[dialogId] !== undefined) {
          this.dialogVisible[dialogId] = false;
        }

        // 对于特定的对话框，还需要通知父组件更新
        if (dialogId === "forbiddenVisible") {
          this.$emit("update:forbiddenVisible", false);
        } else if (dialogId === "chuteUnLockVisible") {
          this.$emit("update:chuteUnLockVisible", false);
          this.$emit("update:cellNum", "");
        } else if (dialogId === "chuteLockVisible") {
          this.$emit("update:chuteLockVisible", false);
        } else if (dialogId === "carVisible") {
          this.$emit("update:carVisible", false);
        } else if (dialogId === "abnormalAlarmVisible") {
          this.$emit("abnormal-close");
        } else if (dialogId === "clearTheKilometersVisible") {
          this.$emit("update:clearTheKilometersVisible", false);
        }
      }

      // 通知父组件对话框已关闭
      this.$emit("close-dialog", dialogId);
    },
    handleDialogSuccess(dialogId, data) {
      this.$emit("dialog-success", { dialogId, data });
    },
    // 处理层级变化
    handleLayChange(value) {
      this.$emit("lay-change", value);
    },
    // 处理异常报警变化
    handleChangeAbnormal(value, type) {
      this.$emit("change-abnormal", value, type);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../styles/scada-dialogs.scss";
</style> 