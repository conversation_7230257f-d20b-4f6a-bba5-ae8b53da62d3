<template>
    <screen-adaptive ref="screenAdaptive">
        <div class="scada">
            <!-- 效率统计弹窗 - 移出ScreenAdaptive组件避免层叠上下文问题 -->
            <el-dialog title="效率统计" width="1200px" :visible.sync="efficiencyDialogVisible"
                :before-close="closeEfficiencyDialog" center :append-to-body="true">
                <div class="efficiency-dialog-content">

                    <div class="chart_container_left">
                        <div class="chart_container_left_top">
                            <div class="chart_title">间隔10分钟分拣统计数据</div>
                            <div id="timelineChart" style="width: 100%; height: 200px;"></div>
                        </div>
                        <div class="chart_container_left_bottom">
                            <div class="chart_title">24小时内每小时的分拣数据统计（件/h）</div>
                            <div id="barChart" style="width: 100%; height: 200px;"></div>
                        </div>
                    </div>
                    <div class="chart_container_right">
                        <div class="progressChart">
                            <div class="chart_txt">
                                <span style="font-weight: 500;
                                font-size: 28px;
                                color: #2D89FF;">{{ this.averageEfficiency }}</span>
                                <span style="font-weight: 400;
                                            font-size: 12px;
                                            color: #666666;">件/时</span>
                                <div style="font-weight: 400;
                                            font-size: 12px;
                                            color: #666666;">平均效率</div>
                            </div>
                        </div>
                    </div>
                </div>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="closeEfficiencyDialog">取消</el-button>
                        <el-button type="primary" @click="closeEfficiencyDialog">确定</el-button>
                    </span>
                </template>
            </el-dialog>

            <div class="left">
                <div class="efficiencyStatistics" @click="openEfficiencyDialog">
                    <div class="efficiencyStatistics_icon">

                    </div>
                    <div class="efficiencyStatistics_txt">
                        效率统计
                    </div>
                </div>
                <div class="equipment_list">
                    <div class="equipment_item">
                        <div style="margin-bottom: 12px;">设备运行状态：</div>
                        <!-- <div style="margin-bottom: 8px;"> <span
                                :class="equipment['318_1'].prop2 > 0 ? 'equipment_icon_green' : 'equipment_icon_red'"></span>
                            {{ equipment['318_1'].prop2 > 0 ? '设备正在运行' : '设备停止运行' }}</div> -->
                        <div style="margin-bottom: 8px;"> <span
                                :class="equipment['318_2'].prop2 > 0 ? 'equipment_icon_green' : 'equipment_icon_red'"></span>
                            {{ equipment['318_2'].prop2 > 0 ? '设备正在运行' : '设备停止运行' }}</div>
                    </div>
                    <div class="equipment_item">
                        <div style="margin-bottom: 12px;">开始运行时间：</div>
                        <!-- <div style="margin-bottom: 8px;">{{ equipment['322_1'].prop2 }}</div> -->
                        <div>{{ equipment['322_2'].prop2 }}</div>
                    </div>
                    <div class="equipment_item">
                        <div style="margin-bottom: 12px;">运行速度：</div>
                        <!-- <div style="margin-bottom: 8px;">{{ equipment['318_1'].prop2 }}</div> -->
                        <div>{{ equipment['318_2'].prop2 || 0 }}</div>
                    </div>
                    <div class="equipment_item">
                        <div style="margin-bottom: 12px;">内/外圈小车占有率：</div>
                        <div style="margin-bottom: 8px;">{{ equipment['320_1'].prop2 || 0 }}% / {{
                            equipment['320_2'].prop2 || 0 }}%</div>
                        <!-- <div>{{ equipment['320_2'].prop2 || 0 }}%</div> -->
                    </div>
                    <div class="equipment_item">
                        <div style="margin-bottom: 12px;">设备运行总公里数：</div>
                        <!-- <div style="margin-bottom: 8px;">{{ equipment['321_1'].prop2 }}</div> -->
                        <div>{{ equipment['321_1'].prop2 }}</div>
                    </div>
                    <div class="equipment_item">
                        <div style="margin-bottom: 12px;">PLC连接状态：</div>
                        <!-- <div style="margin-bottom: 8px;"> <span
                                :class="equipment['337_1'].prop2 == 1 ? 'equipment_icon_green' : 'equipment_icon_red'"></span>
                            {{ equipment['337_1'].prop2 == 1 ? '已连接' : '断开连接' }}</div> -->
                        <div style="margin-bottom: 8px;"> <span
                                :class="equipment['337_2'].prop2 == 1 ? 'equipment_icon_green' : 'equipment_icon_red'"></span>
                            {{ equipment['337_2'].prop2 == 1 ? '已连接' : '断开连接' }}</div>

                    </div>

                </div>

                <div class="equipment_btn">

                    <div class="scadaStatus">
                        <div class="icon"></div>
                        <div class="txt_box">
                            <div class="tittle_txt">环线模式</div>
                            <div class="status_txt">{{ currentMode }}</div>
                        </div>
                    </div>

                    <div class="btn" @click="equipmentSet">
                        <div class="icon">
                            <img src="../../assets/scada/sbkz.png" alt="">
                        </div>
                        <div class="txt">设备控制</div>
                    </div>
                    <div class="btn" @click="abnormalAlarm">
                        <div class="icon">
                            <img src="../../assets/scada/ycbj.png" alt="">
                        </div>
                        <div class="txt">异常报警</div>
                    </div>
                    <div class="btn" @click="allOpen">
                        <div class="icon">
                            <img src="../../assets/scada/yjjs.png" alt="">
                        </div>
                        <div class="txt">一键解锁</div>
                    </div>
                    <div class="btn" @click="allLock">
                        <div class="icon">
                            <img src="../../assets/scada/yjsg.png" alt="">
                        </div>
                        <div class="txt">一键锁格</div>
                    </div>
                    <div class="btn" @click="clearKm">
                        <div class="icon">
                            <img src="../../assets/scada/qcgls.png" alt="">
                        </div>
                        <div class="txt">清除公里数</div>
                    </div>
                    <div class="btn">
                        <div class="icon">
                            <img src="../../assets/scada/qpxs.png" alt="">
                        </div>
                        <div class="txt" @click="fullScreen">全屏显示</div>
                    </div>
                </div>
                <!-- <div class="btn" @click="clientSocket">长链接测试</div> -->

                <div class="canvas-container">
                    <canvas ref="canvas" :width="width" :height="height" @wheel.prevent="handleWheel"
                        @mousedown="handleMouseDown" @mousemove="handleMouseMove" @mouseup="handleMouseUp"
                        @mouseleave="handleMouseUp" @click="handleCanvasClick" :style="canvasStyle"></canvas>


                </div>
                <!-- 速度控制 -->
                <!-- <div class="speedBox">
                    <el-input-number v-model="baseSpeedNum" :min="0" :precision="2" :step="0.1"
                        :max="1.4"></el-input-number>
                    <div class="btn" @click="setSpeed">确定</div>
                </div> -->
                <div class="startBox">
                    <div class="startBtn" @click="handleButtonClick(1)">
                        <img :src="equipment['362'].prop1 === 1 && equipment['362'].prop2 === 1 ? require('@/assets/scada/start.png') : require('@/assets/scada/start_no.png')"
                            alt="">
                    </div>
                    <div class="stopBtn" @click="handleButtonClick(2)">
                        <img :src="equipment['362'].prop1 === 2 && equipment['362'].prop2 === 1 ? require('@/assets/scada/stop.png') : require('@/assets/scada/stop_no.png')"
                            alt="">
                    </div>
                    <div class="resetBtn" @click="handleButtonClick(3)">
                        <img :src="equipment['362'].prop1 === 3 && equipment['362'].prop2 === 1 ? require('@/assets/scada/reset.png') : require('@/assets/scada/reset_no.png')"
                            alt="">
                    </div>
                </div>
                <div class="status_box">
                    <div class="line_box">
                        <div class="line_item">
                            <div class="txt">WebSocket状态：</div>
                            <div class="icon_green"></div>
                            <div class="status">已连接</div>
                        </div>
                        <div class="line_item">
                            <div class="txt">WCS通讯状态：</div>
                            <div class="icon_red"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">小车状态：</div>
                            <div class="icon_green"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">载货：</div>
                            <div class="icon_orange"></div>
                        </div>
                    </div>
                    <div class="line_box">
                        <div class="line_item">
                            <div class="txt">格口状态：</div>
                            <div class="icon_red"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">满包：</div>
                            <div class="icon_purple"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">拦截件：</div>
                            <div class="icon_blue"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">异常口：</div>
                            <div class="icon_black"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">待通信：</div>
                            <div class="icon_grey"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">最大循环：</div>
                            <div class="icon_orange"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">取消：</div>
                            <div class="icon_blue_d"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">未配方案：</div>
                            <div class="icon_blue_s"></div>
                        </div>
                        <div class="line_item">
                            <div class="txt">未配三段码：</div>
                            <div class="icon_brown"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="right">
                <div class="software">
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/sms.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">扫描数</div>
                            <div class="num">{{ equipment['304'].prop1 }}</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/yxgls.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">本次运行公里数</div>
                            <div class="num">{{ (equipment['329_1'].prop2 * 1000 + equipment['329_2'].prop2 * 1000) /
                                1000
                                || 0 }}
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/yxzgls.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">运行总公里数</div>
                            <div class="num">{{ (equipment['321_1'].prop2 * 1000 + equipment['321_2'].prop2 * 1000) /
                                1000
                                || 0 }}
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/ycl.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">异常量</div>
                            <div class="num">{{ equipment['328'].prop1 }}</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/lgs.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">落格数</div>
                            <div class="num">{{ equipment['305'].prop1 }}</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/sbbts.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">失败补推数</div>
                            <div class="num">{{ equipment['303'].prop1 }}</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/ljs.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">拦截数</div>
                            <div class="num">{{ equipment['306'].prop1 }}</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/cqs.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">超圈数</div>
                            <div class="num">{{ equipment['307'].prop1 }}</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/sdmgk.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">未配置三段码格口</div>
                            <div class="num">{{ equipment['308'].prop1 }}</div>
                        </div>
                    </div>

                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/zhycgk.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">综合异常格口</div>
                            <div class="num">{{ equipment['309'].prop1 }}</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/qxj.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">取消件</div>
                            <div class="num">{{ equipment['311'].prop1 }}</div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="icon">
                            <img src="../../assets/scada/sdmxx.png" alt="">
                        </div>
                        <div class="txtBox">
                            <div class="txt">未获取三段码信息</div>
                            <div class="num">{{ equipment['310'].prop1 }}</div>
                        </div>
                    </div>
                </div>
                <div class="echarts_box">
                    <div class="echarts_item">
                        <div class="unit">m/s</div>
                        <div class="num">{{ equipment['318_2'].prop2 }}</div>
                        <div class="txt">运行速度</div>
                    </div>
                    <div class="echarts_item">
                        <div class="unit">%</div>
                        <div class="num">{{ equipment['320_1'].prop2 }}%</div>
                        <div class="txt">小车占有率</div>
                    </div>
                    <div class="echarts_item">
                        <div class="unit">m/s</div>
                        <div class="num">{{ equipment['318_2'].prop2 || 0 }}</div>
                        <div class="txt">运行速度</div>
                    </div>
                    <div class="echarts_item">
                        <div class="unit">%</div>
                        <div class="num">{{ equipment['320_2'].prop2 || 0 }}%</div>
                        <div class="txt">小车占有率</div>
                    </div>
                </div>
                <div class="warning">
                    <div class="tittle">报警信息</div>
                    <div class="list">
                        <div class="list_item" v-for="(item, index) in errList" :key="index">
                            <div class="icon"></div>
                            <div class="txt">{{ item.message }}</div>
                            <div class="time">{{ item.createTime }}</div>
                        </div>

                    </div>
                </div>
            </div>

            <!-- 二次确认model -->
            <el-dialog :title="tittleName" :visible.sync="chuteUnLockVisible" width="25%" append-to-body>
                <el-form ref="form" :model="form" label-width="120px">
                    <el-form-item :label="$t('scada.VerificationPassword') + ':'">
                        <el-input v-model="form.chuteUnLockPwd" type="password"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="chuteUnLockAll()" type="danger">{{
                        $t("common.Confirm")
                    }}</el-button>
                    <el-button @click="closeChuteUnLock()">{{
                        $t("common.Close")
                    }}</el-button>
                </span>
            </el-dialog>

            <!-- 报警表格弹窗 -->
            <el-dialog :title="$t('scada.AbnormalAlarm')" :visible.sync="abnormalAlarmVisible" width="60%"
                append-to-body @closed="abnormalClose()">
                <el-row>
                    <el-col :span="8">
                        {{ $t("scada.StartTime") }}:<el-date-picker v-model="AbnormalAlarmTime.startTime"
                            type="datetime" :placeholder="$t('common.SelectStartTime')" :clearable="false"
                            @change="(e) => changeAbnormal(e, 'startTime')">
                        </el-date-picker>
                    </el-col>
                    <el-col :span="8">
                        <span>{{ $t("scada.EndTime") }}</span>:<el-date-picker v-model="AbnormalAlarmTime.endTime"
                            type="datetime" :clearable="false" :placeholder="$t('common.SelectEndTime')"
                            @change="(e) => changeAbnormal(e, 'endTime')">
                        </el-date-picker>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <span style="margin-left: 23px">
                            {{ $t("common.Source") }}:
                            <el-select v-model="AbnormalAlarmInfo.logSource" style="width: 220px">
                                <el-option v-for="dict in runLogSource" :key="dict.value" :label="dict.label"
                                    :value="dict.value"></el-option>
                            </el-select>
                        </span></el-col>
                    <el-col :span="8"> {{ $t("scada.SearchCriteria") }}:
                        <el-input :placeholder="$t('common.PleaseEnterContent')"
                            v-model="AbnormalAlarmInfo.SearchCriteria" style="width: 220px"
                            @input="(e) => changeAbnormal(e, 'SearchCriteria')" /></el-col>
                    <el-col :span="8">
                        <el-button @click="abnormalAlarm()" type="primary">{{
                            $t("scada.Select")
                        }}</el-button>
                    </el-col>
                </el-row>



                <scadaAbnormalList :abnormalList="abnormalList" :AbnormalAlarmInfo="AbnormalAlarmInfo"
                    @getParentFn="childParent" @postSuccess="postSuccess" @abnormalAlarm="abnormalAlarm" />
                <vxe-pager background :current-page="AbnormalAlarmInfo.currentPage"
                    :page-size="AbnormalAlarmInfo.pageSize" :total="AbnormalAlarmInfo.totalResult" :layouts="[
                        'PrevPage',
                        'JumpNumber',
                        'NextPage',
                        'FullJump',
                        'Sizes',
                        'Total',
                    ]" @page-change="handlePageChange" />
                <span slot="footer" class="dialog-footer">

                    <el-button @click="abnormalClose()">{{
                        $t("common.Close")
                    }}</el-button>
                </span>
            </el-dialog>

            <!-- 格口操作确认弹窗 -->
            <el-dialog :visible.sync="gridConfirmVisible" width="30%" center append-to-body>
                <div style="text-align: center; margin-bottom: 20px;">
                    <p> <strong style="font-size: 20px;">解锁 <span style="color: red;">{{ currentGridSide === '1' ? '内圈'
                        :
                                '外圈' }}{{ currentGridId }}</span> 格口</strong> </p>
                </div>
                <el-form ref="form" :model="form" label-width="120px">
                    <el-form-item :label="$t('scada.VerificationPassword') + ':'">
                        <el-input v-model="form.chuteUnLockPwd" type="password"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="confirmGridOperation" type="danger">{{
                        $t("common.Confirm")
                    }}</el-button>
                    <el-button @click="cancelGridOperation">{{
                        $t("common.Close")
                    }}</el-button>
                </span>
            </el-dialog>

            <!-- 设备控制确认弹窗 -->
            <el-dialog :visible.sync="deviceControlVisible" width="30%" center append-to-body>
                <div style="text-align: center; margin-bottom: 20px;">
                    <p> <strong style="font-size: 20px;">{{ deviceControlTitle }}</strong> </p>
                </div>
                <el-form ref="form" :model="form" label-width="120px">
                    <el-form-item :label="$t('scada.VerificationPassword') + ':'">
                        <el-input v-model="form.chuteUnLockPwd" type="password"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="confirmDeviceOperation" type="danger">{{
                        $t("common.Confirm")
                    }}</el-button>
                    <el-button @click="cancelDeviceOperation">{{
                        $t("common.Close")
                    }}</el-button>
                </span>
            </el-dialog>

            <!-- 设备控制弹窗 -->
            <el-dialog :visible.sync="equipmentVisible" class="setEqBg" width="30%" center append-to-body title="设备控制">
                <div>
                    <el-form label-width="90px">
                        <el-form-item label="学习模式">
                            <el-button type="primary" @click="handleLearnMode">启用</el-button>
                        </el-form-item>
                        <el-form-item label="定点停车">
                            <el-switch v-model="maintainMode" @change="handleMaintainModeChange"></el-switch>
                        </el-form-item>
                        <el-form-item label="小车号">
                            <el-input v-model="carStopId" placeholder="请输入小车号" style="width: 120px; margin-right: 10px;"
                                :disabled="!maintainMode"></el-input>
                            <el-button type="primary" @click="handleCarStop(true)"
                                :disabled="!maintainMode">确认</el-button>
                            <!-- <el-button @click="handleCarStop(false)" :disabled="!maintainMode">关闭</el-button> -->
                        </el-form-item>
                        <el-form-item label="速度切换">
                            <el-input-number v-model="speedValue" :min="0.4" :max="1.5" :step="0.1" :precision="1"
                                style="width: 120px; margin-right: 10px;" :disabled="!maintainMode">
                            </el-input-number>
                            <!-- <el-select v-model="speedValue" style="width: 100px; margin-right: 10px;" :disabled="!maintainMode">
                            <el-option v-for="item in speedOptions" :key="item" :label="item" :value="item"></el-option>
                        </el-select> -->
                            <el-button type="primary" @click="handleSpeedConfirm"
                                :disabled="!maintainMode">确认</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="equipmentVisible = false">取 消</el-button>
                </span>
            </el-dialog>

        </div>
    </screen-adaptive>


</template>

<script>
import scadaAbnormalList from "@/views/sysMonitor/scadaAbnormalList.vue";
import ScreenAdaptive from '@/components/ScreenAdaptive.vue'
let socket = null
import moment from "moment";
import request from "@/utils/request";
import { Message } from 'element-ui'
import * as echarts from 'echarts';

// 添加消息缓冲区类
class MessageBuffer {
    constructor() {
        this.buffer = new Map();
        this.timer = null;
        this.WINDOW_TIME = 100; // 100ms的时间窗口
        this.processing = false;
    }

    // 添加新报文
    addMessage(message, callback) {
        // 解析报文，提取关键信息
        const entries = this.parseMessage(message);

        // 更新或添加报文到缓冲区
        entries.forEach(entry => {
            if (entry.id) {
                this.buffer.set(entry.id, entry);
            }
        });

        // 如果没有活动的定时器，启动一个
        if (!this.timer) {
            this.timer = setTimeout(() => {
                this.processMessages(callback);
            }, this.WINDOW_TIME);
        }
    }

    // 解析报文
    parseMessage(message) {
        const entries = message.split('|').filter(entry => entry.trim() !== '');
        return entries.map(entry => {
            const parts = entry.split('#');
            const id = Number(parts[0]);

            // 特殊处理322报文（带有日期时间的报文）
            if (id === 322) {
                const circleValue = Number(parts[1]) || 0;
                const timeValue = parts[2] || '';
                return {
                    id: id + '_' + circleValue,
                    prop1: circleValue,
                    prop2: timeValue,
                    prop3: 0
                };
            }

            // 其他报文的处理
            const values = parts.slice(1).map(val => isNaN(Number(val)) ? 0 : Number(val));
            while (values.length < 3) {
                values.push(0);
            }

            // 报文有内外圈之分 
            if ([320, 318, 321, 329, 337].includes(id)) {
                return {
                    id: id + '_' + values[0],
                    prop1: values[0],
                    prop2: values[1],
                    prop3: values[2]
                }
            }

            // 301 313 314
            if ([301, 314].includes(id)) {
                const circleValue = values[2] !== undefined ? values[2] : 1;
                return {
                    id: id + '_' + circleValue + '_' + values[0],
                    prop1: values[0],
                    prop2: values[1],
                    prop3: circleValue,
                    prop4: values[3]
                }
            }

            return {
                id: id + '',
                prop1: values[0],
                prop2: values[1],
                prop3: values[2],
                prop4: values[3]
            };
        });
    }

    // 处理合并后的报文
    processMessages(callback) {
        if (this.processing) return;
        this.processing = true;

        try {
            // 获取所有待处理的报文
            const messages = Array.from(this.buffer.values());

            // 清空缓冲区
            this.buffer.clear();
            this.timer = null;

            // 按优先级排序处理报文
            this.sortAndProcess(messages, callback);
        } finally {
            this.processing = false;
        }
    }

    // 按优先级排序并处理报文
    sortAndProcess(messages, callback) {
        // 定义报文优先级
        const priorityOrder = {
            '314': 1,  // 格口状态 - 提高优先级
            // '364': 2,  // 急停
            '365': 3,  // 变频器
            '301': 4,  // 小车状态
            '312': 5,  // 小件快手
            '359': 6,  // 扫描框
            'default': 7
        };

        // 按优先级排序
        messages.sort((a, b) => {
            const priorityA = priorityOrder[a.id.split('_')[0]] || priorityOrder.default;
            const priorityB = priorityOrder[b.id.split('_')[0]] || priorityOrder.default;
            return priorityA - priorityB;
        });

        // 处理排序后的报文
        if (callback && typeof callback === 'function') {
            // 立即处理格口状态消息
            const gridMessages = messages.filter(msg => msg.id.startsWith('314_'));
            if (gridMessages.length > 0) {
                console.log('立即处理格口状态消息:', gridMessages);
                callback(gridMessages);
            }

            // 处理其他消息
            const otherMessages = messages.filter(msg => !msg.id.startsWith('314_'));
            if (otherMessages.length > 0) {
                console.log('处理其他消息:', otherMessages);
                callback(otherMessages);
            }
        }
    }
}

export default {
    components: { scadaAbnormalList, ScreenAdaptive },
    data() {
        return {
            hourlyData: [],
            averageEfficiency: '',
            minuteData: [],
            messageBuffer: new MessageBuffer(),
            equipmentVisible: false,
            maintainMode: false,
            carStopId: '',
            speedValue: 1.0,
            speedOptions: ['1.0', '0.8', '0.6', '0.4', '0.2'],
            urlParams: { // 存储URL参数
                dbCode: '',
                port: ''
            },
            errList: [],
            runLogSource: [],
            isLog: false,
            AbnormalAlarmTime: {
                startTime: "",
                endTime: "",
            },
            abnormalAlarmVisible: false,
            alarmTableData: [],
            modelType: '一键锁格', // 一键锁格 一键解锁  清除公里数
            tittleName: this.$t('scada.OneKeyUnlock'),
            chuteUnLockVisible: false,
            cellNum: '',
            form: {
                chuteUnLockPwd: ''
            },
            abnormalList: [],
            AbnormalAlarmInfo: {
                logSource: " ",
                loading: false,
                SearchCriteria: "",
                AbnormalAlarmList: [
                    { name: "设备报警", id: 1 },
                    { name: "业务报警", id: 2 },
                ],
                clickGroup: "设备报警",
                FaultLevelValueList: [false, false, true],
                FaultLevelList: [
                    this.$t("scada.CriticalAlarm"),
                    this.$t("scada.GeneralAlarm"),
                    this.$t("scada.MinorAlarm"),
                ],
                abnormalListColumn: [
                    {
                        prop: "SerialNumber",
                        label: this.$t("scada.SerialNumber"),
                        align: "center",
                        width: 80,
                    },
                    {
                        prop: "createTime",
                        label: this.$t("scada.Time"),
                        align: "center",
                        width: 135,
                    },
                    {
                        prop: "alarmSource",
                        label: this.$t("scada.AlarmSource"),
                        align: "center",
                        width: 135,
                    },
                    {
                        prop: "alarmType",
                        label: this.$t("scada.AlarmType"),
                        align: "center",
                        width: 135,
                    },
                    {
                        prop: "message",
                        label: this.$t("scada.Content"),
                        align: "center",
                        width: 420,
                    },
                    {
                        prop: "processState",
                        label: '处理情况',
                        align: "center",
                        width: 420,
                    },

                    {
                        prop: "alarmHelpUrl",
                        label: this.$t("scada.AlarmHelpLink"),
                        align: "center",
                        width: 135,
                    },
                    {
                        prop: "Operation",
                        label: this.$t("scada.Operation"),
                        align: "center",
                        width: 200,
                        fixed: "right",
                    },
                ],
                totalResult: 0,
                currentPage: 1,
                pageSize: 10,
                messageNum: 0,
            },
            //展示数据
            equipment: {
                '329_1': { prop1: 0, prop2: 0, prop3: 0 },
                '329_2': {},
                '321_1': { prop1: 0, prop2: 0, prop3: 0 },
                '321_2': {},
                '318_1': {
                    id: '318_1',
                    prop1: 1,
                    prop2: 0
                },
                '318_2': {
                    id: '318_2',
                    prop1: 2,
                    prop2: 0
                },
                '337_1': { prop1: 0, prop2: 0, prop3: 0 },
                '337_2': {},
                '320_1': { prop1: 0, prop2: 0, prop3: 0 },
                '320_2': {},
                '304': {},
                '328': {},
                '305': {},
                '303': {},
                '306': {},
                '307': {},
                '308': {},
                '309': {},
                '311': {},
                '310': {},
                '322_1': { prop1: 0, prop2: 0, prop3: 0 },
                '322_2': {},
                '313_1': { prop1: 0, prop2: 0, prop3: 0 },
                '313_2': { prop1: 0, prop2: 0, prop3: 0 },
                '314_1': { prop1: 0, prop2: 0, prop3: 0 },
                '314_2': {},
                "312": {},
                "359": {},
                "362": {
                    prop1: 1, prop2: 1,
                }//按钮状态的报文
            },
            // 格口状态
            fixedRect: [
                {
                    key: '未锁定',
                    value: '#5ECB80',
                    id: '1'
                },
                {
                    key: '锁定',
                    value: 'red',
                    id: '2'
                },
                {
                    key: '满包',
                    value: '#8C008B',
                    id: '3'

                },
                {
                    key: '异常口',
                    value: '#262626',
                    id: '4'
                },
                {
                    key: '拦截件',
                    value: '#0101B5',
                    id: '5'
                },

                {
                    key: '待通信',
                    value: '#B2B2B2',
                    id: '6'
                },
                {
                    key: '最大循环',
                    value: '#FF9500',
                    id: '7'
                },
                {
                    key: '取消',
                    value: '#4A428F',
                    id: '8'
                },

                {
                    key: '未分配三段码',
                    value: '#977965',
                    id: '9'
                },
                {
                    key: '未分配方案',
                    value: '#82CFFF',
                    id: '10'
                },
            ],
            // Canvas 配置
            width: 1196,
            height: 658,
            carIds: [],
            replicaMachine: "单层",
            baseSpeed: 0,
            baseSpeedNum: 0,
            laneStr: '外',
            currentLane: "outer",
            animationFrame: null,
            lastTime: 0,

            // 扫描框配置
            scanFrame: {
                id: 1,
                x: 210,  // 位置
                y: 320,
                width: 30,  // 大小
                height: 30,
                color: '#B2B2B2',  // 默认颜色
                borderWidth: 2,
                borderStyle: 'dashed',
                borderColor: 'black',
                isActive: false,  // 控制是否激活
                border: {
                    width: 2,        // 边框宽度
                    color: '#B2B2B2', // 边框颜色
                    style: 'solid'    // 样式（solid/dashed）
                },
                direction: 'down' // 新增direction属性 down up
            },
            //小件快手配置
            smallScanFrame: [
                {
                    id: 1,
                    x: 930,  // 位置
                    y: 335,
                    width: 30,  // 大小
                    height: 30,
                    color: '#B2B2B2',  // 默认颜色
                    borderWidth: 2,
                    borderStyle: 'dashed',
                    borderColor: '#B2B2B2',
                    isActive: false,  // 控制是否激活
                    border: {
                        width: 2,        // 边框宽度
                        color: '#B2B2B2', // 边框颜色
                        style: 'dashed'    // 样式（solid/dashed）
                    }
                },
                {
                    id: 2,
                    x: 930,  // 位置
                    y: 280,
                    width: 30,  // 大小
                    height: 30,
                    color: '#B2B2B2',  // 默认颜色
                    borderWidth: 2,
                    borderStyle: 'dashed',
                    borderColor: '#B2B2B2',
                    isActive: false,  // 控制是否激活
                    border: {
                        width: 2,        // 边框宽度
                        color: '#B2B2B2', // 边框颜色
                        style: 'dashed'    // 样式（solid/dashed）
                    }
                },
            ],
            // 急停按钮配置
            emergencyStop: {
                id: 1,
                x: 346,
                y: 313,
                size: 30,  // 正方形大小
                color: '#B2B2B2',  // 默认颜色
                activeColor: '#5ECB80', // 激活颜色（绿色）
                isActive: false,  // 控制是否激活
                border: {
                    width: 2,        // 边框宽度
                    color: '#333333', // 边框颜色
                }
            },
            // 变频器配置
              frequencyConverter: [{
                id: 1,
                x: 420,
                y: 100,
                width: 32,  // 易拉罐长度
                height: 28,  // 易拉罐高度增加
                color: '#B2B2B2',  // 默认颜色
                activeColor: '#5ECB80', // 激活颜色（绿色）
                isActive: false,  // 控制是否激活
            }, 
            // {
            //     id: 2,
            //     x: 460,
            //     y: 100,
            //     width: 32,  // 易拉罐长度
            //     height: 28,  // 易拉罐高度增加
            //     color: '#B2B2B2',  // 默认颜色
            //     activeColor: '#5ECB80', // 激活颜色（绿色）
            //     isActive: false,  // 控制是否激活
            // }
        ],
            // 轨道配置
            trackConfig: {
                segments: [
                    {
                        type: 'line',
                        start: { x: 200, y: 75 },
                        end: { x: 600, y: 75 },

                    },
                    {
                        type: 'arc',
                        center: { x: 600, y: 225 },
                        radius: 150,
                        startAngle: -Math.PI / 2,
                        endAngle: Math.PI / 2,
                        clockwise: true
                    },
                    {
                        type: 'line',
                        start: { x: 600, y: 375 },
                        end: { x: 200, y: 375 }
                    },
                    {
                        type: 'arc',
                        center: { x: 200, y: 225 },
                        radius: 150,
                        startAngle: Math.PI / 2,
                        endAngle: -Math.PI / 2,
                        clockwise: false,//圆心指向
                        clockwise: true
                    },
                    // {
                    //     type: 'arc',
                    //     center: { x: 200 - 85, y: 500 },
                    //     radius: 75,
                    //     startAngle: Math.PI / 2,
                    //     endAngle: -Math.PI / 2,
                    //     clockwise: true
                    // },
                    // {
                    //     type: 'line',
                    //     start: { x: 200 - 85, y: 425 },
                    //     end: { x: 1000 - 85, y: 425 }
                    // },
                    // {
                    //     type: 'arc',
                    //     center: { x: 1000 - 85, y: 325 },
                    //     radius: 100,
                    //     startAngle: Math.PI / 2,
                    //     endAngle: -Math.PI / 2,
                    //     clockwise: false,//圆心指向
                    //     isReversed: true // U型弯道需要反转方向
                    // },
                    // {
                    //     type: 'line',
                    //     start: { x: 1000 - 85, y: 225 },
                    //     end: { x: 200 - 85, y: 225 }
                    // },
                    // {
                    //     type: 'arc',
                    //     center: { x: 200 - 85, y: 150 },
                    //     radius: 75,
                    //     startAngle: Math.PI / 2,
                    //     endAngle: -Math.PI / 2,
                    //     clockwise: true
                    // },
                ],
                totalLength: 0
            },
            segmentMeta: [],

            // 小车配置
            cars: {
                pairs: [],
                count: 44,
                baseSpeed: 0.15,
                colorConfig: {
                    baseHue: 0,
                    saturation: 80,
                    lightness: 50,
                    hueSpeed: 0.1,
                    baseColor: '#B2B2B2'
                },// 自定义小车颜色
                offset: 15
            },

            // 格口配置
            fixedRectConfigs: [
                {
                    id: 1,
                    segments: [0], // 绑定轨道段索引
                    count: 9,
                    startId: 1,
                    spacing: 40,
                    side: 'outer', // 'inner' 或 'outer'
                    offset: 45,
                    start: 40, // 起始位置（单位：像素）
                    size: { width: 24, height: 30 },
                    color: '#B2B2B2'//初始颜色
                },
                // {
                //     id: 5,
                //     segments: [0], // 绑定轨道段索引
                //     count: 20,
                //     startId: 1,
                //     spacing: 40,
                //     side: 'outer', // 'inner' 或 'outer'
                //     offset: 45,
                //     start: 40, // 起始位置（单位：像素）
                //     size: { width: 24, height: 30 },
                //     color: '#B2B2B2'//初始颜色
                // },
                // {
                //     id: 2,
                //     segments: [1], // 同时绑定多个轨道段
                //     count: 5,
                //     startId: 21,
                //     spacing: 30,
                //     side: 'outer',
                //     offset: 45,
                //     start: 50, // 起始位置（单位：像素）
                //     size: { width: 24, height: 30 },
                //     color: '#B2B2B2'//初始颜色
                // },
                // {
                //     id: 3,
                //     segments: [2], // 同时绑定多个轨道段
                //     count: 15,
                //     startId: 26,
                //     spacing: 40,
                //     side: 'inner',
                //     offset: 45,
                //     start: 50, // 起始位置（单位：像素）
                //     size: { width: 24, height: 30 },
                //     color: '#B2B2B2'//初始颜色
                // },
                // {
                //     id: 4,
                //     segments: [4], // 同时绑定多个轨道段
                //     count: 10,
                //     startId: 26,
                //     spacing: 40,
                //     side: 'outer',
                //     offset: 45,
                //     start: 50, // 起始位置（单位：像素）
                //     size: { width: 24, height: 30 },
                //     color: '#B2B2B2'//初始颜色
                // },

            ],
            fixedRectangles: [],
            textConfig: {
                rotateWithRect: false, // 是否跟随方块旋转
                customAngle: 0         // 自定义旋转角度（弧度制）
            },

            // 缩放相关
            // 缩放相关状态
            zoom: 1.2,
            offset: { x: 120, y: 150 },
            minZoom: 0.5,
            maxZoom: 2,
            // 拖拽相关状态
            isDragging: false,
            lastMousePos: { x: 0, y: 0 },
            // 颜色控制
            carColorAPI: {
                setColors: this.setCarColors.bind(this),
            },

            // 格口确认弹窗
            gridConfirmVisible: false,
            currentGridId: null,
            currentGridColor: null,
            currentGridSide: null,
            currentGridIndex: null,
            alarmListTimer: null, // 新增定时器句柄
            gaugeSpeed1: null,
            gaugeRate1: null,
            gaugeSpeed2: null,
            gaugeRate2: null,
            currentMode: '模式1', // 添加当前环线模式状态
            // 添加格口颜色控制API
            gridColorAPI: {
                setColors: this.setGridColors.bind(this),
            },
            deviceControlVisible: false,
            deviceControlTitle: '',
            currentDeviceOperationType: null, // 存储当前设备操作类型 1-启动, 2-停止, 3-复位
            // 效率统计弹窗相关数据
            efficiencyDialogVisible: false,
        }
    },
    computed: {
        canvasStyle() {
            return {
                transform: `translate(${this.offset.x}px, ${this.offset.y}px) scale(${this.zoom})`,
                cursor: this.isDragging ? 'grabbing' : 'grab'
            }
        }
    },
    mounted() {
        this.parseUrlParams() // 解析URL参数
        this.getDict()
        this.getAlarmList()
        // 开启定时器每10秒请求一次
        this.alarmListTimer = setInterval(() => {
            this.getAlarmList()
        }, 10000)
        this.clientSocket()
        // 验证密码
        // debugger
        // 获取效率统计数据
        this.getQuantityList()

    },
    beforeDestroy() {
        cancelAnimationFrame(this.animationFrame)
        // 不要在这里清除carColorAPI
        // window.carColorAPI = null
        // 新增：清除定时器
        if (this.alarmListTimer) {
            clearInterval(this.alarmListTimer)
            this.alarmListTimer = null
        }
    },
    methods: {
        // 打开效率统计弹窗
        openEfficiencyDialog() {
            this.efficiencyDialogVisible = true;
            this.$nextTick(() => {
                this.initCharts()
            })

        },
        // 关闭效率统计弹窗
        closeEfficiencyDialog() {
            this.efficiencyDialogVisible = false;
        },
        extractTimeSlice(dateTimeStr) {
            // 提取时间部分，格式为 HH:mm:ss
            if (!dateTimeStr) return '';

            // 如果是完整的日期时间字符串，提取时间部分
            if (dateTimeStr.includes(' ')) {
                return dateTimeStr.split(' ')[1] || '';
            }

            // 如果已经是时间格式，直接返回
            return dateTimeStr;
        },
        extractTimeSliceHour(str) {
            return str.split(' ')[1] || str.slice(11, 19) || '格式错误';
        },
        // 初始化图表
        initCharts() {
            this.initTimelineChart();
            this.initBarChart();
            // this.initProgressChart();
        },
        // 初始化时间线图表
        initTimelineChart() {
            const chartDom = document.getElementById('timelineChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);

            const option = {
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '5%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: this.minuteData.map(item => this.extractTimeSlice(item.time)),
                    axisLine: {
                        lineStyle: {
                            color: '#ccc'
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#ccc'
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#f0f0f0'
                        }
                    }
                },
                series: [{
                    data: this.minuteData.map(item => item.num),
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        color: '#409EFF',
                        width: 2
                    },
                    itemStyle: {
                        color: '#409EFF'
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                                { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                            ]
                        }
                    }
                }],
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        return params[0].name + '<br/>' + params[0].seriesName + ': ' + params[0].value;
                    }
                }
            };

            myChart.setOption(option);
        },
        // 初始化柱状图
        initBarChart() {
            const chartDom = document.getElementById('barChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);

            // 生成24小时数据
            const hours = [];
            const data = [];

            for (let i = 0; i < 24; i++) {
                hours.push(i + ':00');
                data.push(Math.floor(Math.random() * 300) + 100);
            }

            const option = {
                grid: {
                    top: "5%",
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: this.hourlyData && this.hourlyData.length > 0
                        ? this.hourlyData.map(item => this.extractTimeSlice(item.time))
                        : hours,
                    axisLine: {
                        lineStyle: {
                            color: '#ccc'
                        }
                    },
                    axisLabel: {
                        color: '#666',
                        interval: 1
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#ccc'
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#f0f0f0'
                        }
                    }
                },
                series: [{
                    data: this.hourlyData && this.hourlyData.length > 0
                        ? this.hourlyData.map(item => item.num)
                        : data,
                    type: 'bar',
                    itemStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 1, x2: 0, y2: 0,
                            colorStops: [
                                { offset: 0, color: 'rgba(45, 137, 255, 0)' },
                                { offset: 1, color: 'rgba(45, 137, 255, 1)' }
                            ]
                        }
                    },
                    barWidth: '60%'
                }],
                tooltip: {
                    trigger: 'axis',
                    formatter: function (params) {
                        return params[0].name + '<br/>' + params[0].seriesName + ': ' + params[0].value;
                    }
                }
            };

            myChart.setOption(option);
        },
        async getQuantityList() {
            // 每小时平均效率  averageEfficiency
            // 每小时数据 hourlyData
            // 每10Min平均效率 minuteData
            let res = await request({
                url: `/ffs/scada/quantity/real/list?dbCode=5`,
                method: 'get',
                params: {}
            })
            console.log(res, 'getQuantityList')
            this.averageEfficiency = res.data.data.result.averageEfficiency;
            this.hourlyData = res.data.data.result.hourlyData;
            this.minuteData = res.data.data.result.minuteData;

        },

        getUrlParams(url) {
            const queryString = url.split('?')[1] || '';
            const params = {};
            queryString.split('&').forEach(pair => {
                const [key, value] = pair.split('=');
                if (key) params[decodeURIComponent(key)] = decodeURIComponent(value || '');
            });
            return params;
        },
        // 解析URL参数方法
        parseUrlParams() {
            console.log('解析到的URL参数:', window.location);
            const params = this.getUrlParams(window.location.href);
            // const data = Object.fromEntries(params.entries());
            console.log('解析到的URL参数:', params);
            // 截取dbCode和port参数
            this.urlParams.dbCode = params.dbCode; // 默认值为5
            this.urlParams.port = params.port; // 默认值为9006


        },
        fullScreen() {
            const element = document.getElementsByClassName('app-main')[0] || document.documentElement;

            if (!document.fullscreenElement) {
                // 请求全屏
                if (element.requestFullscreen) {
                    element.requestFullscreen();
                } else if (element.msRequestFullscreen) {
                    element.msRequestFullscreen();
                } else if (element.mozRequestFullScreen) {
                    element.mozRequestFullScreen();
                } else if (element.webkitRequestFullscreen) {
                    element.webkitRequestFullscreen();
                }



            } else {
                // 退出全屏
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                }
            }
        },
        // 获取密码
        async requirePassword(flag = 'deviceControlPassword') {
            try {
                const res = await request({
                    url: '/ffs/hardwareOperation/verify/password',
                    method: 'get',
                    params: {
                        password: this.form.chuteUnLockPwd,
                        dbCode: this.urlParams.dbCode,
                        flag: flag
                    }
                });
                return res.data; // 返回实际的数据而不是整个响应对象
            } catch (error) {
                console.error('密码验证失败:', error);
                return null;
            }
        },
        // 获取字典值
        getDict() {

            request({
                url: '/dict/data/type/runLogSource',
                method: 'get'
            }).then(res => {
                this.runLogSource = res.data.result.map(item => ({
                    label: item.dictLabel,
                    value: item.dictValue
                }))
            })
        },
        // 预警按钮选择时间
        changeAbnormal(e, type) {
            const time = moment(e).format("YYYY-MM-DD HH:mm:ss");
            this.AbnormalAlarmTime[type] = time.trim();
        },
        // 异常报警关闭
        abnormalClose() {
            this.childParent();
            this.abnormalAlarmVisible = false;
            let str = `201#1#${this.account}\r\n`; // 报警已读关闭
            this.sendMessage(str);
            // this.AbnormalAlarmInfo.messageNum = 0
        },
        handlePageChange({ currentPage, pageSize }) {
            this.AbnormalAlarmInfo.currentPage = currentPage;
            this.AbnormalAlarmInfo.pageSize = pageSize;
            this.abnormalAlarm();
        },
        //调试显示日志
        debugBtn() {
            this.isLog = !this.isLog;
        },
        // 设备控制
        equipmentSet() {
            this.modelType = '设备控制'
            this.chuteUnLockVisible = true
            this.tittleName = '设备控制'
            // this.equipmentVisible = true;
        },
        //异常报警
        abnormalAlarm() {
            this.abnormalAlarmVisible = true;
            return request({
                url: "/ffs/log/alarm/list",
                method: "post",
                data: {
                    dbCode: this.urlParams.dbCode,
                    alarmSource:
                        this.AbnormalAlarmInfo.logSource == " "
                            ? "null"
                            : this.AbnormalAlarmInfo.logSource,
                    message: this.AbnormalAlarmInfo.SearchCriteria
                        ? this.AbnormalAlarmInfo.SearchCriteria
                        : "",
                    ...this.AbnormalAlarmTime,
                    curPage: this.AbnormalAlarmInfo.currentPage,
                    pageSize: this.AbnormalAlarmInfo.pageSize,
                },
            })
                .then((response) => {
                    // this.AbnormalAlarmInfo.loading = false

                    let str = `201#0#${this.account}\r\n`; // 报警已读开启
                    this.sendMessage(str);
                    this.abnormalList = response.data.result.records;
                    this.AbnormalAlarmInfo.totalResult = response.data.result.total || 0;
                })
                .catch((error) => {
                    this.$message({
                        message: error,
                        type: "error",
                        duration: 2 * 1000,
                    });
                });
        },

        // 右下角告警接口
        async getAlarmList() {
            let res = await request({
                url: `/ffs/log/alarm/real/list?dbCode=${this.urlParams.dbCode}`,
                method: "get",

            })
            console.log(res, 'getAlarmList')
            this.errList = res.data.result
        },
        // 子组件调父组件
        postSuccess(val) {
            if (val) {
                this.abnormalAlarm();
            }
        },
        childParent() {
            this.changeAbnormal(
                moment().format("YYYY-MM-DD") + " 00:00:00",
                "startTime"
            );
            this.changeAbnormal(new Date(), "endTime");
            this.changeAbnormal("", "SearchCriteria");
            this.AbnormalAlarmInfo.clickGroup === "设备报警";
            // this.abnormalAlarm()
        },
        // 关闭解锁格口
        closeChuteUnLock() {
            this.chuteUnLockVisible = false;
        },
        async chuteUnLockAll() {
            const result = await this.requirePassword('deviceControlPassword');

            if (!result) {
                Message({
                    message: "密码验证失败",
                    type: 'error',
                    duration: 2 * 1000
                });
                return;
            }



            if (this.modelType === '一键锁格') {
                this.chuteUnLockVisible = false;
                this.sendMessage('354#1');
                this.form.chuteUnLockPwd = '';
            }

            if (this.modelType === '一键解锁') {
                this.chuteUnLockVisible = false;
                this.sendMessage('354#2');
                this.form.chuteUnLockPwd = '';
            }

            if (this.modelType === '清除公里数') {
                this.sendMessage('363#1');
                this.sendMessage('363#2');
                this.form.chuteUnLockPwd = '';
                this.chuteUnLockVisible = false;
            }
            if (this.modelType === '设备控制') {
                this.equipmentVisible = true;
                this.form.chuteUnLockPwd = '';
                this.chuteUnLockVisible = false;
            }
        },

        // 中间件 生成标准报文 | 结尾后如果无报文，则截取
        createCode(str) {
            //  305#3#2|306#3#1| -> 305#3#2|306#3#1 -> 转换为对象
            const entries = str.split('|').filter(entry => entry.trim() !== '');

            //   解析每个条目为对象
            return entries.map(entry => {
                // 按井号分割所有部分
                const parts = entry.split('#');
                const id = Number(parts[0]);

                // 特殊处理322报文（带有日期时间的报文）
                if (id === 322) {
                    const circleValue = Number(parts[1]) || 0; // 圈层(1:内, 2:外)
                    const timeValue = parts[2] || ''; // 保留时间字符串原样

                    return {
                        id: id + '_' + circleValue,
                        prop1: circleValue,
                        prop2: timeValue, // 保持时间字符串格式
                        prop3: 0
                    };
                }

                // 其他报文的处理
                // 确保所有值都被处理为数字，如果转换失败则为0
                const values = parts.slice(1).map(val => isNaN(Number(val)) ? 0 : Number(val));

                // 确保至少有3个值（包括id），如果没有则填充0
                while (values.length < 3) {
                    values.push(0);
                }

                // 报文有内外圈之分 
                if ([320, 318, 321, 329, 337].includes(id)) {
                    return {
                        id: id + '_' + values[0],
                        prop1: values[0],   // 扩展字段 最大支持4
                        prop2: values[1],
                        prop3: values[2]
                    }
                }

                // 301 313 314
                if ([301, 314].includes(id)) {
                    // 报文id, 格口/小车号, 状态, 内外圈区分(1上、2下)
                    // 确保values[2]存在，防止越界
                    const circleValue = values[2] !== undefined ? values[2] : 1;

                    return {
                        id: id + '_' + circleValue + '_' + values[0],
                        prop1: values[0],   // 格口/小车号
                        prop2: values[1],   // 状态
                        prop3: circleValue, // 内外圈区分
                        prop4: values[3]    // 额外参数(如果有)
                    }
                } else {
                    return {
                        id: id + '',        // 报文类型
                        prop1: values[0],   // 扩展字段 最大支持4
                        prop2: values[1],
                        prop3: values[2],
                        prop4: values[3]    // 额外参数(如果有)
                    };
                }
            });
        },
        arrayToIdKeyObject(arr) {
            return arr.reduce((acc, item) => {
                acc[item.id] = { ...item };  // 复制对象避免引用关联
                return acc;
            }, {});
        },
        transcoder(messages) {
            let that = this;

            // 将消息数组转换为对象
            const messageObj = messages.reduce((acc, message) => {
                acc[message.id] = message;
                return acc;
            }, {});

            // 更新设备状态
            Object.keys(messageObj).forEach(key => {
                if (that.equipment[key] !== undefined) {
                    that.equipment[key] = messageObj[key];
                }
            });

            // 处理特殊报文
            messages.forEach(message => {
                const messageId = message.id.split('_')[0];

                // 处理368报文 - 环线模式
                if (messageId === '368') {
                    const mode = message.prop1;


                    switch (mode) {
                        case 2:
                            this.currentMode = '等待头车模式';
                            break;
                        case 3:
                            this.currentMode = '校验模式';
                            break;
                        case 5:
                            this.currentMode = '学习模式';
                            break;
                        case 6:
                            this.currentMode = '正常运行模式';
                            break;
                        case 7:
                            this.currentMode = '维修模式';
                            break;
                        default:
                            this.currentMode = '其他模式';
                    }
                }

                // 处理301报文 - 小车状态
                if (messageId === '301') {
                    this.handleCarStatus(message);
                }

                // 处理314报文 - 格口状态
                if (messageId === '314') {
                    this.handleGridStatus(message);
                }

                // 处理312报文 - 小件快手
                if (messageId === '312') {
                    this.handleSmallScanStatus(message);
                }

                // 处理359报文 - 扫描框
                if (messageId === '359') {
                    this.handleScanFrameStatus(message);
                }

                // 处理364报文 - 急停
                // if (messageId === '364') {
                //     this.handleEmergencyStop(message);
                // }

                // 处理365报文 - 变频器
                if (messageId === '365') {
                    this.handleFrequencyConverter(message);
                }

                // 处理372报文 - 维修模式状态
                if (messageId === '372') {
                    this.handleMaintainModeStatus(message);
                }

                // 处理370报文 - 小车号
                if (messageId === '370') {
                    this.handleCarIdStatus(message);
                }
            });

            // 更新车速状态
            if (that.equipment['318_2'] && that.equipment['318_2'].prop2 > 0) {
                that.controlTrack('start');
            } else {
                that.controlTrack('stop');
            }
        },
        // 处理小车状态
        handleCarStatus(message) {
            const carId = message.prop1;
            const status = message.prop2;
            const circleValue = message.prop3;

            let carColor;
            if (status == 1) {
                carColor = '#FF9500'; // 装载
            } else if (status == 0) {
                carColor = '#5ECB80'; // 未装载
            } else {
                carColor = '#B2B2B2'; // 默认颜色
            }

            if (circleValue == 1) {
                this.carColorAPI.setColors([{
                    id: carId,
                    color: carColor,
                    lane: 'inner'
                }]);
            } else {
                this.carColorAPI.setColors([{
                    id: carId,
                    color: carColor,
                    lane: 'outer'
                }]);
            }
        },
        // 生成颜色
        getColor(id) {
            console.log('获取颜色，状态ID:', id, '状态列表:', this.fixedRect);
            let color = '#B2B2B2'; // 默认颜色
            const status = this.fixedRect.find(item => item.id == id);
            if (status) {
                color = status.value;
                console.log('找到对应颜色:', color, '状态:', status);
            } else {
                console.warn('未找到状态ID对应的颜色:', id);
            }
            return color;
        },
        // 处理格口状态
        handleGridStatus(message) {
            const gridId = message.prop1;
            const status = message.prop2;
            const circleValue = message.prop3;

            console.log('处理格口状态:', {
                gridId,
                status,
                circleValue,
                格口总数: this.fixedRectangles.length,
                当前格口: this.fixedRectangles.find(r => String(r.id) === String(gridId))
            });

            const newColor = this.getColor(status);
            console.log('获取到的颜色:', newColor);

            // 确保使用正确的内外圈标识
            const side = circleValue == 1 ? 'inner' : 'outer';
            // 将所有格口设置为红色
            // this.fixedRectangles.forEach(rect => {
            //     rect.color = 'red';
            //     rect.originalConfig.color = 'red';
            // });
            // 直接更新格口颜色
            const targetRect = this.fixedRectangles.find(r =>
                String(r.id) === String(gridId) &&
                r.originalConfig.side === side
            );



            if (targetRect) {
                console.log('直接更新格口颜色:', {
                    id: targetRect.id,
                    side: targetRect.originalConfig.side,
                    原颜色: targetRect.color,
                    新颜色: newColor
                });

                // 直接赋值更新颜色
                targetRect.color = newColor;
                targetRect.originalConfig.color = newColor;
            } else {
                console.warn('未找到目标格口:', {
                    gridId,
                    side,
                    格口列表: this.fixedRectangles
                });
            }
        },
        // 处理小件快手状态
        handleSmallScanStatus(message) {
            const deviceId = message.prop1;
            const status = message.prop2;
            const color = status == 1 ? '#5ECB80' : '#B2B2B2';

            const targetIndex = this.smallScanFrame.findIndex(item => item.id == deviceId);
            if (targetIndex >= 0) {
                this.smallScanFrame[targetIndex].color = color;
                this.smallScanFrame[targetIndex].border.color = color;
            }
        },
        // 处理扫描框状态
        handleScanFrameStatus(message) {
            const deviceId = message.prop1;
            const status = message.prop2;
            const color = status == 1 ? '#5ECB80' : '#B2B2B2';

            if (deviceId == this.scanFrame.id) {
                this.updateScanFrame(color);
            }
        },
        // 处理急停状态
        handleEmergencyStop(message) {
            const status = message.prop1;
            this.emergencyStop.isActive = status == 1;
        },
        // 处理变频器状态
        handleFrequencyConverter(message) {
            const status = message.prop2;
            const deviceId = message.prop1;
            console.log('变频器报文处理:', { deviceId, status, message });

            // 找到对应的变频器
            const targetIndex = this.frequencyConverter.findIndex(item => item.id == deviceId);
            console.log('找到变频器索引:', targetIndex, '设备ID:', deviceId);

            if (targetIndex >= 0) {
                console.log('更新前变频器状态:', this.frequencyConverter[targetIndex]);
                if (status == 0) {
                    this.frequencyConverter[targetIndex].color = 'red';
                }
                if (status == 1) {
                    this.frequencyConverter[targetIndex].color = '#5ECB80';
                }
                console.log('更新后变频器状态:', this.frequencyConverter[targetIndex]);
            } else {
                console.log('未找到对应的变频器，设备ID:', deviceId);
            }
        },
        // 一键锁格
        allLock() {
            this.modelType = '一键锁格'
            this.chuteUnLockVisible = true

            this.tittleName = this.$t('scada.OneKeyLockSlot')
        },
        allOpen() {
            this.modelType = '一键解锁'
            this.chuteUnLockPwd = "";
            this.chuteUnLockVisible = true;
            this.tittleName = this.$t('scada.OneKeyUnlock')

        },
        clearKm() {
            this.modelType = '清除公里数'
            this.chuteUnLockVisible = true
            this.tittleName = this.$t('scada.ClearTheKilometers')
        },
        // 发送圈速
        setSpeed() {
            this.sendMessage(`367#1#${this.baseSpeedNum}`)
        },
        //获取报文
        getCodeArr(params) {
            let codeArr = params.split('#')
            return codeArr
        },
        // 鼠标滚轮缩放
        handleWheel(e) {
            const delta = e.deltaY > 0 ? 0.9 : 1.1
            const newZoom = Math.min(Math.max(this.zoom * delta, this.minZoom), this.maxZoom)
            const rect = this.$refs.canvas.getBoundingClientRect()

            // 计算以鼠标为中心的缩放
            const mouseX = e.clientX - rect.left
            const mouseY = e.clientY - rect.top

            // 计算缩放后的偏移补偿
            const scaleFactor = newZoom / this.zoom
            this.offset.x = mouseX - (mouseX - this.offset.x) * scaleFactor
            this.offset.y = mouseY - (mouseY - this.offset.y) * scaleFactor

            this.zoom = newZoom
        },

        // 鼠标拖拽开始
        handleMouseDown(e) {
            this.isDragging = true
            this.dragStart = {
                x: e.clientX - this.offset.x,
                y: e.clientY - this.offset.y
            }
        },

        // 鼠标拖拽移动
        handleMouseMove(e) {
            if (!this.isDragging) return

            this.offset.x = e.clientX - this.dragStart.x
            this.offset.y = e.clientY - this.dragStart.y
        },

        // 鼠标释放
        handleMouseUp() {
            this.isDragging = false
        },

        // 使用旋转矩形碰撞检测
        checkRectHit(rect, mouseX, mouseY) {
            try {
                // 获取矩形中心点
                const centerX = rect.x + rect.width / 2;
                const centerY = rect.y + rect.height / 2;

                // 计算鼠标点到矩形中心的向量
                const dx = mouseX - centerX;
                const dy = mouseY - centerY;

                // 矩形的旋转角度（注意：这里需要取反，因为我们要将点击坐标转换到矩形的坐标系）
                const angle = -rect.rotate;

                // 应用旋转变换 (旋转鼠标坐标到矩形坐标系)
                const rotatedX = dx * Math.cos(angle) - dy * Math.sin(angle);
                const rotatedY = dx * Math.sin(angle) + dy * Math.cos(angle);

                // 判断点是否在未旋转的矩形内，使用较小的容差
                const halfWidth = rect.width / 2 + 2; // 2像素容差
                const halfHeight = rect.height / 2 + 2; // 2像素容差

                const isInside = Math.abs(rotatedX) <= halfWidth &&
                    Math.abs(rotatedY) <= halfHeight;

                // 如果检测到点击，添加额外调试信息
                if (isInside) {
                    console.log('矩形碰撞检测详情:', {
                        id: rect.id,
                        中心点: { x: centerX, y: centerY },
                        点击位置: { x: mouseX, y: mouseY },
                        相对位置: { dx, dy },
                        旋转后位置: { rotatedX, rotatedY },
                        旋转角度: rect.rotate * 180 / Math.PI,
                        尺寸: { width: rect.width, height: rect.height },
                        容差: { width: halfWidth * 2, height: halfHeight * 2 },
                        是否命中: isInside,
                        原始配置: rect.originalConfig,
                        格口间距: rect.originalConfig.spacing
                    });
                }

                return isInside;
            } catch (error) {
                console.error('碰撞检测出错:', error);
                return false;
            }
        },

        // 坐标转换方法（用于点击检测）
        getCanvasCoordinates(event) {
            // 获取canvas容器的位置
            const containerRect = this.$refs.canvas.parentElement.getBoundingClientRect();

            // 获取ScreenAdaptive组件的实例
            const screenAdaptive = this.$refs.screenAdaptive;
            const screenScale = screenAdaptive ? {
                x: screenAdaptive.scale.x,
                y: screenAdaptive.scale.y
            } : { x: 1, y: 1 };

            // 计算鼠标在容器中的相对位置
            const mouseX = event.clientX - containerRect.left;
            const mouseY = event.clientY - containerRect.top;

            // 应用ScreenAdaptive的变换逆变换
            const scaledX = mouseX / screenScale.x;
            const scaledY = mouseY / screenScale.y;

            // 应用Canvas的缩放和偏移的逆变换
            const finalX = (scaledX - this.offset.x) / this.zoom;
            const finalY = (scaledY - this.offset.y) / this.zoom;

            // 添加调试日志
            console.log('坐标转换详情:', {
                原始坐标: { x: mouseX, y: mouseY },
                缩放值: screenScale,
                缩放后: { x: scaledX, y: scaledY },
                最终坐标: { x: finalX, y: finalY },
                格口位置: this.fixedRectangles.find(r => r.id === '1'),
                容器信息: {
                    left: containerRect.left,
                    top: containerRect.top,
                    width: containerRect.width,
                    height: containerRect.height
                }
            });

            return {
                x: finalX,
                y: finalY
            };
        },
        //创立链接  
        clientSocket() {
            // socket = new WebSocket(`ws://192.168.150.198:${this.urlParams.port}/ws`)
            socket = new WebSocket(`ws://192.168.10.166:${this.urlParams.port}/ws`)
            const that = this
            socket.onopen = function (e) {
                console.log('连接已建立:', e);
                that.initTrackConfig()
                that.generateMultiSegRects()
                that.initializeSymmetryCars()
                that.startAnimation()
                // 确保API在连接建立时被正确赋值
                that.$nextTick(() => {
                    if (!window.carColorAPI) {
                        window.carColorAPI = that.carColorAPI
                    }
                    if (!window.gridColorAPI) {
                        window.gridColorAPI = that.gridColworAPI
                    }
                })
            };

            socket.onmessage = function (event) {
                try {
                    // 使用消息缓冲区处理报文
                    that.messageBuffer.addMessage(event.data, (messages) => {
                        that.transcoder(messages);
                    });
                } catch (e) {
                    console.error('解析消息错误:', e);
                }
            };

            socket.onclose = (event) => {
                that.controlTrack('stop')
                // 不要在这里清除carColorAPI
                // window.carColorAPI = null
            };
        },
        sendMessage(message) {
            if (socket.readyState === WebSocket.OPEN) { // 确保连接已打开
                socket.send(message);
                console.log('已发送:', message);
            } else {
                console.error('连接未建立，无法发送');
            }
        },
        // 小车颜色控制方法
        setCarColors(arr) {
            if (!arr || !Array.isArray(arr)) return;

            arr.forEach(item => {
                if (!item || !item.id) return;

                const target = this.cars.pairs.find(c => c.id === item.id);
                if (!target) {
                    console.warn(`未找到ID为${item.id}的小车`);
                    return;
                }

                if (item.lane === 'inner') {
                    this.$set(target.inner, 'customColor', item.color || this.cars.colorConfig.baseColor);
                } else {
                    this.$set(target.outer, 'customColor', item.color || this.cars.colorConfig.baseColor);
                }
            });
        },
        // 初始化轨道配置
        initTrackConfig() {
            // 1. 计算每个轨道段的长度，更精确处理圆弧长度
            this.trackConfig.segments = this.trackConfig.segments.map(segment => {
                const newSeg = { ...segment } // 创建新对象保持响应性

                // 计算轨道段长度
                if (newSeg.type === 'line') {
                    // 直线长度计算不变
                    newSeg.length = Math.hypot(
                        newSeg.end.x - newSeg.start.x,
                        newSeg.end.y - newSeg.start.y
                    )
                } else {
                    // 修正圆弧长度的计算（使用实际弧长计算）
                    let angleDiff = Math.abs(newSeg.endAngle - newSeg.startAngle)
                    // 确保使用最短弧
                    if (angleDiff > Math.PI * 2) {
                        angleDiff = Math.PI * 2 - angleDiff
                    }
                    // 弧长 = 半径 × 角度
                    newSeg.length = angleDiff * newSeg.radius
                }

                console.log(`轨道段类型: ${newSeg.type}, 长度: ${newSeg.length.toFixed(2)}`);
                return newSeg
            })

            // 调整小车数量以匹配轨道长度，更均匀分布
            const totalLength = this.trackConfig.segments.reduce((sum, s) => sum + s.length, 0);
            this.trackConfig.totalLength = totalLength;

            // 打印总长度
            console.log(`轨道总长度: ${totalLength.toFixed(2)}`);

            // 建议的小车间距（可调整）
            const desiredCarSpacing = 50; // 理想的小车间距（像素）
            // 根据轨道长度和期望的间距计算小车数量
            const suggestedCarCount = Math.max(20, Math.floor(totalLength / desiredCarSpacing));
            console.log(`建议的小车数量: ${suggestedCarCount}，当前设置: ${this.cars.count}`);

            // 3. 生成轨道段元数据
            let accumulated = 0
            this.segmentMeta = this.trackConfig.segments.map(seg => {
                const meta = {
                    startDistance: accumulated,
                    length: seg.length,
                    endDistance: accumulated + seg.length
                }
                accumulated += seg.length
                return meta
            })

            // 4. 初始化格口
            this.generateMultiSegRects()

            // 5. 初始化对称小车
            this.initializeSymmetryCars()

            // 6. 启动动画循环（如果需要立即启动）
            this.startAnimation()
        },

        // 初始化对称小车 - 改进等分算法
        initializeSymmetryCars() {
            // 计算轨道间隔（确保精确等分）
            const totalLength = this.trackConfig.totalLength;
            const count = this.cars.count;

            // 精确计算每个小车的间隔距离
            const interval = totalLength / count;
            console.log(`小车间隔: ${interval.toFixed(2)}像素`);

            // 生成对称车组数组
            const newPairs = Array.from({ length: count }, (_, index) => {
                // 计算小车的精确位置
                const basePosition = interval * index;
                const baseHue = (360 / count) * index;

                return {
                    id: 1 + index, // 从1开始递增的ID
                    basePosition: basePosition, // 基准轨道位置
                    speed: this.baseSpeed, // 同步基础速度
                    inner: {
                        customColor: this.cars.colorConfig.baseColor, // 初始颜色
                        offset: -this.cars.offset, // 内侧偏移量
                        hue: (baseHue + 180) % 360 // 互补色
                    },
                    outer: {
                        customColor: this.cars.colorConfig.baseColor,
                        offset: this.cars.offset,
                        hue: baseHue
                    }
                }
            });

            this.cars.pairs = newPairs;
            console.log(`生成了${newPairs.length}对小车`);
        },

        // 动画相关方法
        startAnimation() {
            this.lastTime = performance.now()
            this.animationFrame = requestAnimationFrame(this.animate)
        },

        animate(timestamp) {
            const ctx = this.$refs.canvas.getContext('2d')
            const deltaTime = timestamp - (this.lastTime || timestamp)
            this.lastTime = timestamp

            // 清空画布
            ctx.clearRect(0, 0, this.width, this.height)

            // 绘制轨道
            this.drawTrack(ctx)

            // 绘制格口
            this.drawFixedRects(ctx)

            // 绘制小件快扫
            // this.drawGoodsPort(ctx)

            // 绘制扫描框
            this.drawScanFrame(ctx)

            // 绘制急停按钮
            // this.drawEmergencyStop(ctx)

            // 绘制变频器
            this.drawFrequencyConverter(ctx)

            // 更新小车位置
            this.cars.pairs.forEach(pair => {
                pair.basePosition = (pair.basePosition + pair.speed * deltaTime) % this.trackConfig.totalLength
            })

            // 绘制小车
            this.drawCars(ctx)

            // 请求下一帧
            this.animationFrame = requestAnimationFrame(this.animate)
        },
        // 绘制轨道
        drawTrack(ctx) {
            // 先绘制所有直线段
            ctx.beginPath()
            ctx.strokeStyle = '#B2B2B2'
            ctx.lineWidth = 3
            this.trackConfig.segments.forEach(segment => {
                if (segment.type === 'line') {
                    ctx.moveTo(segment.start.x, segment.start.y)
                    ctx.lineTo(segment.end.x, segment.end.y)
                }
            })
            ctx.stroke()

            // 再绘制所有圆弧段
            ctx.beginPath()
            ctx.strokeStyle = '#B2B2B2'
            ctx.lineWidth = 3
            this.trackConfig.segments.forEach(segment => {
                if (segment.type === 'arc') {
                    ctx.moveTo(
                        segment.center.x + Math.cos(segment.startAngle) * segment.radius,
                        segment.center.y + Math.sin(segment.startAngle) * segment.radius
                    )
                    ctx.arc(
                        segment.center.x,
                        segment.center.y,
                        segment.radius,
                        segment.startAngle,
                        segment.endAngle,
                        !segment.clockwise
                    )
                }
            })
            ctx.stroke()
        },

        // 绘制格口
        drawFixedRects(ctx) {
            this.fixedRectangles.forEach(rect => {
                ctx.save();
                ctx.translate(rect.x + rect.width / 2, rect.y + rect.height / 2);
                ctx.rotate(rect.rotate);

                // 绘制矩形
                ctx.fillStyle = rect.color || '#B2B2B2';
                ctx.fillRect(-rect.width / 2, -rect.height / 2, rect.width, rect.height);

                // 绘制文字
                if (!this.textConfig.rotateWithRect) {
                    ctx.rotate(-rect.rotate);
                }
                ctx.fillStyle = '#FFFFFF';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(rect.id.toString().padStart(1, '0'), 0, 0);

                ctx.restore();
            });
        },

        // 绘制小件快扫
        drawGoodsPort(ctx) {
            this.smallScanFrame.forEach(rect => {
                ctx.save();

                // 检查并显示当前颜色值用于调试
                // console.log(`绘制小件快手(ID:${rect.id}), 颜色: ${rect.color}`);

                // 绘制内部矩形
                ctx.fillStyle = rect.color || '#B2B2B2'; // 确保有一个默认颜色
                ctx.fillRect(
                    rect.x + rect.borderWidth,
                    rect.y + rect.borderWidth,
                    rect.width - 2 * rect.borderWidth,
                    rect.height - 2 * rect.borderWidth
                );

                // 绘制四个角
                ctx.strokeStyle = rect.border.color || '#B2B2B2'; // 确保有一个默认颜色
                ctx.lineWidth = rect.border.width;
                ctx.setLineDash([]); // 使用实线

                const cornerLength = 8; // 角的长度
                const x = rect.x;
                const y = rect.y;
                const w = rect.width;
                const h = rect.height;

                // 左上角
                ctx.beginPath();
                ctx.moveTo(x, y + cornerLength);
                ctx.lineTo(x, y);
                ctx.lineTo(x + cornerLength, y);
                ctx.stroke();

                // 右上角
                ctx.beginPath();
                ctx.moveTo(x + w - cornerLength, y);
                ctx.lineTo(x + w, y);
                ctx.lineTo(x + w, y + cornerLength);
                ctx.stroke();

                // 右下角
                ctx.beginPath();
                ctx.moveTo(x + w, y + h - cornerLength);
                ctx.lineTo(x + w, y + h);
                ctx.lineTo(x + w - cornerLength, y + h);
                ctx.stroke();

                // 左下角
                ctx.beginPath();
                ctx.moveTo(x + cornerLength, y + h);
                ctx.lineTo(x, y + h);
                ctx.lineTo(x, y + h - cornerLength);
                ctx.stroke();

                // 绘制编号
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(rect.id.toString(), x + w / 2, y + h / 2);

                ctx.restore();
            });
        },

        // 绘制小车
        drawCars(ctx) {
            this.cars.pairs.forEach(pair => {
                // 外层小车
                this.drawTrapezoidOuterRing(
                    ctx,
                    this.getTrackPosition(pair.basePosition, pair.outer.offset),
                    this.getTrackAngle(pair.basePosition, pair.outer.offset),
                    pair
                )

                // 内层小车（根据翻板机状态）
                if (this.replicaMachine === '双层') {
                    this.drawTrapezoid(
                        ctx,
                        this.getTrackPosition(pair.basePosition, pair.inner.offset),
                        this.getTrackAngle(pair.basePosition, pair.inner.offset),
                        pair
                    )
                }
            })
        },

        // 梯形绘制方法（内层）
        drawTrapezoid(ctx, pos, angle, pair) {
            ctx.save()
            ctx.translate(pos.x, pos.y)
            ctx.rotate(angle)

            // 梯形参数
            const frontWidth = 30
            const rearWidth = 23
            const length = 23

            ctx.beginPath()
            ctx.moveTo(-frontWidth / 2, -length / 2)
            ctx.lineTo(frontWidth / 2, -length / 2)
            ctx.lineTo(rearWidth / 2, length / 2)
            ctx.lineTo(-rearWidth / 2, length / 2)
            ctx.closePath()

            ctx.fillStyle = pair.inner.customColor
            ctx.fill()

            // 绘制编号
            // ctx.fillStyle = 'white'
            // ctx.font = 'bold 10px Arial'
            // ctx.textAlign = 'center'
            // ctx.textBaseline = 'middle'
            // ctx.fillText(pair.id.toString(), 0, 0)

            ctx.restore()
        },

        // 梯形绘制方法（外层）
        drawTrapezoidOuterRing(ctx, pos, angle, pair) {
            ctx.save()
            ctx.translate(pos.x, pos.y)
            ctx.rotate(angle + Math.PI)

            // 反转梯形参数
            const frontWidth = 30
            const rearWidth = 23
            const length = 23

            ctx.beginPath()
            ctx.moveTo(-frontWidth / 2, -length / 2)
            ctx.lineTo(frontWidth / 2, -length / 2)
            ctx.lineTo(rearWidth / 2, length / 2)
            ctx.lineTo(-rearWidth / 2, length / 2)
            ctx.closePath()

            ctx.fillStyle = pair.outer.customColor
            ctx.fill()

            // 绘制编号
            // ctx.fillStyle = 'white'
            // ctx.font = 'bold 10px Arial'
            // ctx.textAlign = 'center'
            // ctx.textBaseline = 'middle'
            // ctx.fillText(pair.id.toString(), 0, 0)

            ctx.restore()
        },

        // 根据轨道距离获取坐标位置和角度(优化版)
        getTrackPosition(baseDistance, offset) {
            const epsilon = 0.0001
            let accumulated = 0
            let currentSegment = null
            const total = this.trackConfig.totalLength

            // 处理循环轨道的位置计算
            let validDistance = ((baseDistance % total) + total) % total
            if (validDistance > total - epsilon) validDistance = 0

            // 遍历轨道段查找当前位置
            for (const segment of this.trackConfig.segments) {
                if (validDistance <= accumulated + segment.length + epsilon) {
                    currentSegment = segment
                    break
                }
                accumulated += segment.length
            }

            // 边界情况处理（最后一段）
            if (!currentSegment) {
                currentSegment = this.trackConfig.segments[this.trackConfig.segments.length - 1]
                accumulated = total - currentSegment.length
            }

            const segmentDistance = Math.max(0, validDistance - accumulated)

            // 直线轨道处理
            if (currentSegment.type === 'line') {
                const ratio = segmentDistance / currentSegment.length
                const dx = currentSegment.end.x - currentSegment.start.x
                const dy = currentSegment.end.y - currentSegment.start.y
                const length = Math.hypot(dx, dy)

                // 计算法线方向（外侧为正）
                const normalX = (dy / length) * offset
                const normalY = (-dx / length) * offset

                return {
                    x: currentSegment.start.x + dx * ratio + normalX,
                    y: currentSegment.start.y + dy * ratio + normalY
                }
            }

            // 圆弧轨道处理
            if (currentSegment.type === 'arc') {
                // 处理反转轨道的方向
                const radiusOffset = currentSegment.isReversed ? -offset : offset
                const effectiveRadius = currentSegment.radius + radiusOffset

                // 计算实际角度
                const angleRange = currentSegment.endAngle - currentSegment.startAngle
                const direction = currentSegment.clockwise ? 1 : -1
                const actualAngle = currentSegment.startAngle +
                    (segmentDistance / currentSegment.length) *
                    Math.abs(angleRange) *
                    direction

                return {
                    x: currentSegment.center.x + Math.cos(actualAngle) * effectiveRadius,
                    y: currentSegment.center.y + Math.sin(actualAngle) * effectiveRadius
                }
            }

            // 默认返回（理论上不可达）
            return { x: 0, y: 0 }
        },

        // 根据轨道距离获取角度(优化版)
        getTrackAngle(baseDistance, offset) {
            const epsilon = 0.1 // 微小增量用于计算方向
            const pos1 = this.getTrackPosition(baseDistance, offset)
            const pos2 = this.getTrackPosition(
                (baseDistance + epsilon) % this.trackConfig.totalLength, // 处理循环轨道
                offset
            )

            // 计算两点间的角度差
            return Math.atan2(pos2.y - pos1.y, pos2.x - pos1.x)
        },
        // 切换翻板机状态
        checkCar() {
            this.replicaMachine = this.replicaMachine === '单层' ? '双层' : '单层'

            // 停止当前动画
            cancelAnimationFrame(this.animationFrame)

            // 重新初始化小车配置
            this.initializeSymmetryCars()

            // 重启动画
            this.startAnimation()
        },

        // 速度控制
        speedCheck(speedType) {
            // 更新速度值
            switch (speedType) {
                case '高速':
                    this.baseSpeed = 0.3
                    break
                case '中速':
                    this.baseSpeed = 0.15
                    break
                case '低速':
                    this.baseSpeed = 0.01
                    break
                case '停止':
                    this.baseSpeed = 0
                    break
            }

            // 停止当前动画
            cancelAnimationFrame(this.animationFrame)

            // 更新所有小车的速度
            this.cars.pairs.forEach(pair => {
                pair.speed = this.baseSpeed
            })

            // 重启动画
            this.startAnimation()
        },

        // 处理Canvas点击事件
        handleCanvasClick(event) {
            // 过滤拖拽误操作
            if (this.isDragging) return;

            // 获取精确坐标
            const { x, y } = this.getCanvasCoordinates(event);

            // 按ID排序格口，确保检测顺序正确
            const sortedRects = [...this.fixedRectangles].sort((a, b) => parseInt(a.id) - parseInt(b.id));

            // 遍历检测格口
            for (const rect of sortedRects) {
                if (this.checkRectHit(rect, x, y)) {
                    // 保存当前选中格口信息
                    this.currentGridId = rect.id;
                    this.currentGridSide = rect.originalConfig.side == 'inner' ? '1' : '2';
                    this.currentGridIndex = this.fixedRectangles.indexOf(rect);

                    // 显示确认弹窗
                    this.gridConfirmVisible = true;
                    break; // 找到第一个命中的格口就停止
                }
            }
        },
        checkLane() {
            this.laneStr = this.laneStr === '外' ? '内' : '外'
            this.currentLane = this.currentLane === 'outer' ? 'inner' : 'outer'

            // 如果需要重新初始化小车
            this.initializeSymmetryCars()
        },
        generateMultiSegRects() {
            // 清空现有格口
            this.fixedRectangles = []

            this.fixedRectConfigs.forEach(config => {
                config.segments.forEach(segIndex => {
                    // 验证轨道段索引
                    if (segIndex < 0 || segIndex >= this.trackConfig.segments.length) {
                        console.error('无效的轨道段索引:', segIndex)
                        return
                    }

                    const segment = this.trackConfig.segments[segIndex]
                    const meta = this.segmentMeta[segIndex]

                    // 计算有效偏移量（考虑反转和方向）
                    const calculateEffectiveOffset = () => {
                        let offset = Math.abs(config.offset)
                        if (config.side === 'inner') offset = -offset
                        if (segment.isReversed) offset = -offset
                        return offset
                    }
                    const effectiveOffset = calculateEffectiveOffset()

                    // 验证起始位置
                    const validStart = Math.min(Math.max(config.start, 0), meta.length)

                    try {
                        let currentDistance = validStart
                        let count = 0
                        while (count < config.count && currentDistance <= meta.length) {
                            // 计算全局位置
                            const globalPos = meta.startDistance + currentDistance
                            // 获取坐标和角度
                            const pos = this.getTrackPosition(globalPos, effectiveOffset)
                            const angle = this.getTrackAngle(globalPos, effectiveOffset)

                            // 生成格口ID（两位数格式）
                            const padLength = 1 // ID显示位数
                            const gridId = String(config.startId + count).padStart(padLength, '0')

                            // 推送到格口数组（使用Vue.set保证响应式）
                            this.$set(this.fixedRectangles, this.fixedRectangles.length, {
                                id: gridId,
                                x: pos.x - config.size.width / 2,
                                y: pos.y - config.size.height / 2,
                                width: config.size.width,
                                height: config.size.height,
                                color: config.color,
                                rotate: angle + Math.PI / 2, // 垂直对齐轨道
                                segment: segIndex,
                                border: {
                                    width: 1,
                                    color: '#000',
                                    style: 'solid'
                                },
                                originalConfig: { ...config } // 保留原始配置
                            })

                            currentDistance += config.spacing
                            count++
                        }

                        // 调试信息
                        console.log(`轨道段 ${segIndex} 生成的格口数量:`, count)
                    } catch (error) {
                        console.error('生成格口时出错:', {
                            config,
                            segIndex,
                            error
                        })
                    }
                })
            })

            // 调试日志
            console.log('生成的格口总数:', this.fixedRectangles.length)
        },
        // 绘制扫描框
        drawScanFrame(ctx) {
            const frame = this.scanFrame;
            ctx.save();

            // 绘制内部矩形
            ctx.fillStyle = frame.color;
            ctx.fillRect(
                frame.x + frame.borderWidth,
                frame.y + frame.borderWidth,
                frame.width - 2 * frame.borderWidth,
                frame.height - 2 * frame.borderWidth
            );

            // 绘制边框
            ctx.strokeStyle = frame.color;
            ctx.lineWidth = frame.border.width;
            ctx.setLineDash(frame.border.style === 'dashed' ? [5, 3] : []);
            ctx.strokeRect(
                frame.x,
                frame.y,
                frame.width,
                frame.height
            );

            // 绘制中间凸出的竖线
            const centerX = frame.x + frame.width / 2;
            const lineLength = 20; // 竖线长度

            ctx.strokeStyle = frame.color;
            ctx.lineWidth = 2; // 加粗竖线
            ctx.setLineDash([]); // 使用实线

            // 根据direction参数决定竖线方向
            const direction = frame.direction || 'up'; // 默认为向上

            if (direction === 'up') {
                // 向上延伸
                ctx.beginPath();
                ctx.moveTo(centerX, frame.y - lineLength);
                ctx.lineTo(centerX, frame.y);
            } else {
                // 向下延伸
                ctx.beginPath();
                ctx.moveTo(centerX, frame.y + frame.height);
                ctx.lineTo(centerX, frame.y + frame.height + lineLength);
            }
            ctx.stroke();

            // 绘制编号
            const textX = centerX;
            let textY;
            textY = frame.y + 15; // 15像素调整


            // 绘制文本背景
            // ctx.fillStyle = '#000000';
            // ctx.fillRect(textX - 15, textY - 10, 30, 20);

            // 绘制文本
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = '#FFFFFF';
            ctx.fillText(frame.id.toString(), textX, textY);

            ctx.restore();
        },

        // 更新扫描框状态
        updateScanFrame(color) {
            this.scanFrame.color = color;
            this.scanFrame.borderColor = color;
        },
        // 更新小件快手状态
        updateSmallScanFrame(color) {
            console.log(`统一更新所有小件快手颜色: ${color}`);
            // 更新所有小件快手
            this.smallScanFrame.forEach((item, index) => {
                // 直接设置属性而不是使用$set
                this.smallScanFrame[index].color = color;
                this.smallScanFrame[index].border.color = color;
                console.log(`已更新小件快手ID:${item.id} 颜色为 ${color}`);
            });
        },
        // 控制轨道运行
        controlTrack(controlType, speed = 0.15) {
            // 停止当前动画
            cancelAnimationFrame(this.animationFrame)

            switch (controlType) {
                case 'start':
                    // 启动轨道
                    this.baseSpeed = speed
                    // 更新所有小车的速度
                    this.cars.pairs.forEach(pair => {
                        pair.speed = this.baseSpeed
                    })
                    // 启动动画
                    this.startAnimation()
                    break

                case 'stop':
                    // 停止轨道
                    this.baseSpeed = 0
                    // 更新所有小车的速度
                    this.cars.pairs.forEach(pair => {
                        pair.speed = 0
                    })
                    // 启动动画（速度为0时小车会停止）
                    this.startAnimation()
                    break

                case 'speed':
                    // 调整速度
                    this.baseSpeed = speed
                    // 更新所有小车的速度
                    this.cars.pairs.forEach(pair => {
                        pair.speed = this.baseSpeed
                    })
                    // 启动动画
                    this.startAnimation()
                    break
            }
        },
        // 确认格口操作
        async confirmGridOperation() {
            const result = await this.requirePassword('deviceControlPassword');

            if (!result) {
                Message({
                    message: "密码验证失败",
                    type: 'error',
                    duration: 2 * 1000
                });
                return;
            }



            if (this.currentGridId !== null) {
                const message = `351#${this.currentGridId}#0#${this.currentGridSide}`;
                this.sendMessage(message);

                // 更新格口颜色
                this.$set(this.fixedRectangles, this.currentGridIndex, {
                    ...this.fixedRectangles[this.currentGridIndex],
                });

                // 关闭弹窗
                this.gridConfirmVisible = false;

                // 重置当前格口信息
                this.currentGridId = null;
                this.currentGridColor = null;
                this.currentGridSide = null;
                this.currentGridIndex = null;
                // 重置密码
                this.form.chuteUnLockPwd = '';
            }
        },

        // 取消格口操作
        cancelGridOperation() {
            // 关闭弹窗
            this.gridConfirmVisible = false;

            // 重置当前格口信息
            this.currentGridId = null;
            this.currentGridColor = null;
            this.currentGridSide = null;
            this.currentGridIndex = null;
            // 重置密码
            this.form.chuteUnLockPwd = ''
        },
        // 绘制急停按钮
        drawEmergencyStop(ctx) {
            const stop = this.emergencyStop;
            ctx.save();

            // 绘制正方形
            const halfSize = stop.size / 2;
            ctx.fillStyle = stop.isActive ? stop.activeColor : stop.color;
            ctx.fillRect(
                stop.x - halfSize,
                stop.y - halfSize,
                stop.size,
                stop.size
            );

            // 绘制延伸的直线（竖直方向向上）
            const lineLength = 30; // 延伸线的长度
            ctx.strokeStyle = stop.isActive ? stop.activeColor : stop.color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(stop.x, stop.y - halfSize); // 从正方形顶部中心开始
            ctx.lineTo(stop.x, stop.y - halfSize - lineLength); // 向上延伸
            ctx.stroke();

            // 根据状态绘制不同的图标
            ctx.fillStyle = 'white';
            if (stop.isActive) {
                // 绘制两根竖线（启动状态）
                const lineWidth = 4;
                const lineSpacing = 8;
                const lineHeight = stop.size * 0.6;

                // 第一根竖线
                ctx.fillRect(
                    stop.x - lineSpacing / 2 - lineWidth / 2,
                    stop.y - lineHeight / 2,
                    lineWidth,
                    lineHeight
                );

                // 第二根竖线
                ctx.fillRect(
                    stop.x + lineSpacing / 2 - lineWidth / 2,
                    stop.y - lineHeight / 2,
                    lineWidth,
                    lineHeight
                );
            } else {
                // 绘制三角形（停止状态）
                const triangleSize = stop.size * 0.48;
                ctx.beginPath();
                ctx.moveTo(stop.x - triangleSize / 2, stop.y - triangleSize / 2);
                ctx.lineTo(stop.x - triangleSize / 2, stop.y + triangleSize / 2);
                ctx.lineTo(stop.x + triangleSize / 2, stop.y);
                ctx.closePath();
                ctx.fill();
            }

            ctx.restore();
        },

        // 绘制变频器
        drawFrequencyConverter(ctx) {
            // 遍历所有变频器进行绘制
            this.frequencyConverter.forEach(converter => {
                ctx.save();

                // 主体参数
                const w = converter.width;
                const h = converter.height;
                const x = converter.x;
                const y = converter.y;
                const cut = 6; // 切角大小

                // 绘制带切角的矩形
                ctx.beginPath();
                ctx.moveTo(x - w / 2 + cut, y - h / 2);
                ctx.lineTo(x + w / 2 - cut, y - h / 2);
                ctx.lineTo(x + w / 2, y - h / 2 + cut);
                ctx.lineTo(x + w / 2, y + h / 2 - cut);
                ctx.lineTo(x + w / 2 - cut, y + h / 2);
                ctx.lineTo(x - w / 2 + cut, y + h / 2);
                ctx.lineTo(x - w / 2, y + h / 2 - cut);
                ctx.lineTo(x - w / 2, y - h / 2 + cut);
                ctx.closePath();
                 ctx.fillStyle = converter.color;
                ctx.fill();

                // 左上角两个小白色矩形（更小，整体右移）
                const winW = 6, winH = 4, gap = 2, offsetX = 8;
                ctx.fillStyle = 'white';
                ctx.fillRect(x - w / 2 + offsetX, y - h / 2 + 4, winW, winH);
                ctx.fillRect(x - w / 2 + offsetX + winW + gap, y - h / 2 + 4, winW, winH);

                // 绘制ID数字，居中偏下
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(converter.id.toString(), x, y + h / 6);

                ctx.restore();
            });
        },
        // 处理按钮点击
        handleButtonClick(type) {
            // type: 1-启动, 2-停止, 3-复位
            this.currentDeviceOperationType = type;

            // 设置弹窗标题
            switch (type) {
                case 1:
                    this.deviceControlTitle = '确认启动设备';
                    break;
                case 2:
                    this.deviceControlTitle = '确认停止设备';
                    break;
                case 3:
                    this.deviceControlTitle = '确认复位设备';
                    break;
                default:
                    this.deviceControlTitle = '确认设备操作';
            }

            // 显示确认弹窗
            this.deviceControlVisible = true;
        },
        // 添加格口颜色控制方法
        setGridColors(arr) {
            if (!arr || !Array.isArray(arr)) {
                console.warn('setGridColors: 无效的输入数组');
                return;
            }

            arr.forEach(item => {
                if (!item || !item.id) {
                    console.warn('setGridColors: 无效的格口项', item);
                    return;
                }

                console.log('正在处理格口:', {
                    id: item.id,
                    side: item.side,
                    color: item.color,
                    当前格口总数: this.fixedRectangles.length
                });

                // 查找所有匹配ID的格口
                const gridRects = this.fixedRectangles.filter(r => String(r.id) === String(item.id));
                console.log('找到的格口:', gridRects);

                if (gridRects.length === 0) {
                    console.warn(`未找到ID为${item.id}的格口`);
                    return;
                }

                // 根据内外圈筛选目标格口
                let targetRect = null;
                if (item.side === 'inner') {
                    targetRect = gridRects.find(r => r.originalConfig && r.originalConfig.side === 'inner');
                } else {
                    targetRect = gridRects.find(r => r.originalConfig && r.originalConfig.side === 'outer');
                }

                if (targetRect) {
                    console.log('更新格口颜色:', {
                        id: targetRect.id,
                        side: targetRect.originalConfig.side,
                        原颜色: targetRect.color,
                        新颜色: item.color
                    });

                    // 直接更新颜色
                    targetRect.color = item.color || '#B2B2B2';
                    targetRect.originalConfig.color = item.color || '#B2B2B2';


                } else {
                    console.warn(`未找到匹配的格口配置: ID=${item.id}, side=${item.side}`);
                }
            });
        },
        handleLearnMode() {
            // 启用学习模式逻辑
            this.sendMessage(`373#1`)
            this.$message.success('学习模式已启用');
        },
        handleCarStop(enable) {
            // 定点停车启用/关闭逻辑
            if (!this.carStopId) {
                this.$message.warning('请输入小车号');
                return;
            }

            // 发送定点停车报文 369#小车号#状态（0关1开）
            const status = enable ? 1 : 0;
            const message = `369#${this.carStopId}#${status}`;
            this.sendMessage(message);

            if (enable) {
                this.$message.success(`小车${this.carStopId}定点停车已启用`);
            } else {
                this.$message.info(`小车${this.carStopId}定点停车已关闭`);
            }
        },
        handleSpeedConfirm() {
            // 速度切换逻辑
            // 验证速度范围
            if (this.speedValue < 0.4 || this.speedValue > 1.5) {
                this.$message.warning('速度值必须在0.4-1.5范围内');
                return;
            }

            const message = `367#${this.speedValue}`;
            this.sendMessage(message);
            this.$message.success(`速度已切换为${this.speedValue}`);
        },
        async confirmDeviceOperation() {
            const result = await this.requirePassword('deviceControlPassword');

            if (!result) {
                Message({
                    message: "密码验证失败",
                    type: 'error',
                    duration: 2 * 1000
                });
                return;
            }

            // 密码验证成功，发送设备控制消息
            if (this.currentDeviceOperationType !== null) {
                const currentState = this.equipment['362'].prop2;
                const newState = currentState === 1 ? 0 : 1;
                const message = `364#${this.currentDeviceOperationType}#${newState}`;
                this.sendMessage(message);

                // 显示成功消息
                let operationName = '';
                switch (this.currentDeviceOperationType) {
                    case 1:
                        operationName = '启动';
                        break;
                    case 2:
                        operationName = '停止';
                        break;
                    case 3:
                        operationName = '复位';
                        break;
                }

                Message({
                    message: `设备${operationName}指令已发送`,
                    type: 'success',
                    duration: 2 * 1000
                });
            }

            // 关闭弹窗并重置
            this.deviceControlVisible = false;
            this.form.chuteUnLockPwd = '';
            this.currentDeviceOperationType = null;
        },
        cancelDeviceOperation() {
            // 关闭弹窗并重置
            this.deviceControlVisible = false;
            this.form.chuteUnLockPwd = '';
            this.currentDeviceOperationType = null;
        },
        handleMaintainModeChange(value) {
            // 阻止立即更新视图，先恢复到原来的状态
            this.$nextTick(() => {
                this.maintainMode = !value;
            });

            // 处理维修模式变化，发送报文371#状态（0关1开）
            const status = value ? 1 : 0;
            const message = `371#${status}`;
            this.sendMessage(message);

            console.log('发送维修模式报文:', message, '等待372报文回复更新状态');
        },
        handleMaintainModeStatus(message) {
            // 处理维修模式状态报文372#状态（1开0关）
            const status = message.prop1;
            const oldStatus = this.maintainMode;
            this.maintainMode = status === 1;

            // 只有当状态真正改变时才显示提示
            if (oldStatus !== this.maintainMode) {
                const statusText = this.maintainMode ? '开启' : '关闭';
                this.$message.success(`定点停车模式已${statusText}`);
            }

            console.log('收到维修模式状态报文:', {
                status: status,
                oldStatus: oldStatus,
                newStatus: this.maintainMode
            });
        },
        handleCarIdStatus(message) {
            // 处理370报文 - 小车号报文：370#小车号
            const carId = message.prop1; // 第一个参数就是小车号

            if (carId && carId > 0) {
                this.carStopId = carId.toString();
                console.log('收到小车号报文，自动设置小车号:', carId);
                // this.$message.info(`自动获取到小车号: ${carId}`);
            } else {
                console.warn('收到无效的小车号:', carId);
            }
        },
    }


}
</script>

<style scoped lang="scss">
::v-deep .el-row {
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }
}


.scada {
    display: flex;
    padding: 20px;
    width: 100%;

    // height: calc(100vh - 84px);
    height: 100%;
    background-color: #F7F8F9;
    // background: url(../../assets/scada/bg.png);
    box-sizing: border-box;



    .left {
        width: 1236px;
        height: 1040px;
        background-color: #ffff;
        padding: 20px;
        box-sizing: border-box;
        border-radius: 5px;

        .efficiencyStatistics {
            width: 118px;
            height: 44px;
            background: url(../../assets/scada/btnBg.png) no-repeat;
            background-size: 100% 100%;
            margin-bottom: 20px;
            cursor: pointer;
            align-items: center;
            justify-content: center;
            display: flex;

            .efficiencyStatistics_icon {
                background: url(../../assets/scada/xltj.png) no-repeat;
                background-size: 100% 100%;
                width: 20px;
                height: 20px;
                margin-right: 8px;
            }

            .efficiencyStatistics_txt {
                font-weight: 500;
                font-size: 14px;
                color: #FFFFFF;
            }
        }


        // background-color: seagreen;
        .btn {
            width: 120px;
            height: 40px;
            text-align: center;
            line-height: 40px;
            // padding: 5px;
            background-color: red;
            border-radius: 5px;
            cursor: pointer;
        }

        /* 保持原有样式不变 */
        .handleBtnBox {
            display: flex;
            margin: 10px 0;

            .btn {
                padding: 5px;
                background-color: skyblue;
                border-radius: 5px;
                margin-right: 20px;
                cursor: pointer;
            }
        }

        .equipment_list {
            height: 80px;
            display: flex;

            .equipment_item {
                flex: 1;
                font-size: 13px;

                .equipment_icon_green {
                    width: 6px;
                    height: 6px;
                    background: #5ECB80;
                    border-radius: 50%;
                    display: inline-block;
                    margin: 0 0 4px 0;
                }

                .equipment_icon_red {
                    width: 6px;
                    height: 6px;
                    background: #FF5161;
                    border-radius: 50%;
                    display: inline-block;
                    margin: 0 0 4px 0;
                }
            }
        }

        .equipment_btn {
            display: flex;
            justify-content: right;
            padding-top: 30px;
            position: relative;

            .scadaStatus {
                position: absolute;
                left: 20px;
                display: flex;

                .icon {
                    height: 44px;
                    width: 44px;
                    background: url(../../assets/scada/hxms.png) no-repeat;
                    background-size: 100% 100%;

                }

                .txt_box {
                    height: 44px;
                    margin-left: 12px;

                    .tittle_txt {
                        font-size: 14px;
                        color: #737373;
                    }

                    .status_txt {
                        font-weight: bold;
                        font-size: 16px;
                        color: #2D89FF;
                    }
                }
            }

            .btn {
                width: 130px;
                height: 44px;
                text-align: center;
                line-height: 44px;
                background: url(../../assets/scada/btnBg.png) no-repeat;
                background-size: 100% 100%;
                cursor: pointer;
                // background-color: transparent;
                margin-right: 20px;
                border-radius: 5px;
                display: flex;

                .icon {
                    height: 20px;
                    width: 20px;
                    // background-color: skyblue;
                    margin: 12px 6px 0 12px;

                    img {
                        height: 100%;
                        width: 100%;
                        display: block;
                    }
                }

                .txt {
                    font-size: 16px;
                    color: #FFFFFF;
                }
            }
        }

        .canvas-container {
            width: 1196px;
            height: 658px;
            position: relative;
            overflow: hidden;
            margin: auto;
            background: #ffff;

        }

        .speedBox {
            display: flex;
            // position: absolute;
            // bottom: 5px;
            // right: 75px;
            margin-left: 850px;
            padding-top: 50px;

            .btn {
                height: 28px;
                line-height: 28px;
                background: url(../../assets/scada/btnBg.png) no-repeat;
                background-size: 100% 100%;
                padding: 0 15px;
                width: 72px;
                color: white;
                font-weight: 400;
                font-size: 14px;
                margin-left: 10px;
            }
        }

        canvas {
            position: absolute;
            top: 0;
            left: 0;
            transform-origin: 0 0;
        }

        .startBox {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;

            .startBtn {
                height: 52px;
                width: 52px;
                cursor: pointer;

                img {
                    height: 100%;
                    width: 100%;
                }
            }

            .stopBtn {
                height: 52px;
                width: 52px;
                cursor: pointer;

                img {
                    height: 100%;
                    width: 100%;
                }
            }

            .resetBtn {
                height: 52px;
                width: 52px;
                cursor: pointer;

                img {
                    height: 100%;
                    width: 100%;
                }
            }
        }

        .status_box {
            margin-top: 60px;

            .line_box {
                display: flex;
                padding-bottom: 10px;

                .line_item {
                    display: flex;
                    margin-right: 40px;

                    .txt {
                        font-size: 13px;
                        color: #737373;
                    }

                    .icon_green {
                        width: 8px;
                        height: 8px;
                        background: #5ECB80;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .icon_red {
                        width: 8px;
                        height: 8px;
                        background: red;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .icon_orange {
                        width: 8px;
                        height: 8px;
                        background: #FF9500;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .icon_purple {
                        width: 8px;
                        height: 8px;
                        background: #8C008B;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .icon_blue {
                        width: 8px;
                        height: 8px;
                        background: #0101B5;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .icon_black {
                        width: 8px;
                        height: 8px;
                        background: #262626;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .icon_grey {
                        width: 8px;
                        height: 8px;
                        background: #B2B2B2;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .icon_blue_d {
                        width: 8px;
                        height: 8px;
                        background: #4A428F;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .icon_brown {
                        width: 8px;
                        height: 8px;
                        background: #977965;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .icon_blue_s {
                        width: 8px;
                        height: 8px;
                        background: #82CFFF;
                        border-radius: 50%;
                        margin: 6px 0 2px 0;
                    }

                    .status {
                        font-size: 13px;
                        color: #191919;
                        display: inline-block;
                        margin-left: 5px;
                    }

                }
            }

        }
    }

    .right {
        height: 1040px;
        width: 624px;
        background-color: transparent;
        margin-left: 20px;
        border-radius: 5px;
        box-sizing: border-box;

        .software {
            width: 624px;
            height: 428px;
            background-color: #ffff;

            padding: 25px 25px 0 25px;
            display: flex;
            flex-wrap: wrap;

            .item {
                width: 176px;
                height: 52px;
                display: flex;
                margin-right: 10px;

                .icon {
                    height: 52px;
                    width: 52px;

                    // background-color: red;
                    img {
                        height: 100%;
                        width: 100%;
                    }
                }

                .txtBox {
                    .txt {
                        font-size: 14px;
                        color: #191919;
                        text-indent: 12px;
                    }

                    .num {
                        font-weight: bold;
                        font-size: 20px;
                        color: #2D89FF;
                        text-indent: 12px;
                    }
                }


            }
        }

        .echarts_box {
            height: 337px;
            // background-color: red;
            background-color: #ffff;
            width: 624px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;

            .echarts_item {
                width: 220px;
                height: 118px;
                // background-color: #2D89FF;
                background: url(../../assets/scada/echartsBg.png) no-repeat;
                background-size: 100% 100%;

                .unit {
                    font-size: 12px;
                    color: #191919;
                    text-align: center;
                    margin-top: 53px;
                }

                .num {
                    font-size: 18px;
                    color: #2D89FF;
                    text-align: center;
                }

                .txt {
                    font-size: 12px;
                    color: #191919;
                    text-align: center;
                }
            }
        }

        .warning {
            width: 624px;
            height: 255px;
            // background-color: bisque;
            padding: 18px 16px;
            box-sizing: border-box;
            background-color: #ffff;

            .tittle {
                font-weight: 400;
                font-size: 16px;
                color: #191919;
                // padding-bottom: 18px;
            }

            .list {
                padding: 18px 0 0 0;
                overflow: auto;
                height: 185px;

                .list_item {
                    display: flex;
                    padding: 0 0 16px 0;
                    // overflow: auto;

                    .icon {
                        width: 5px;
                        height: 5px;
                        background: #FF5161;
                        margin: 7px 5px 0 0;
                        border-radius: 10px;
                        // flex: 1;
                    }

                    .txt {
                        font-size: 13px;
                        color: #737373;
                        flex: 40;
                        white-space: nowrap;
                        /* 禁止换行 */
                        overflow: hidden;
                        /* 隐藏溢出内容 */
                        text-overflow: ellipsis;
                        /* 显示省略号 */
                    }

                    .time {
                        font-size: 13px;
                        color: #262626;
                        flex: 11;
                    }
                }
            }
        }
    }


}

// 效率统计弹窗样式
.efficiency-dialog-content {
    display: flex;

    .chart_container_left {
        width: 836px;

        .chart_container_left_top {
            .chart_title {
                text-align: center;
            }
        }

        .chart_container_left_bottom {
            padding-top: 50px;

            .chart_title {
                text-align: center;
            }
        }
    }

    .chart_container_right {
        width: 420px;
        padding: 70px 0;
        // border: 2px dashed #ccc;
        border-radius: 8px;
        // background-color: #f9f9f9;
        display: flex;
        align-items: center;
        justify-content: center;

        .progressChart {
            width: 318px;
            height: 298px;
            background: url(../../assets/scada/xlbj.png) no-repeat;
            background-size: 100% 100%;
            position: relative;

            .chart_txt {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
            }
        }
    }
}
</style>