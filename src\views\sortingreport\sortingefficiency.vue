<template>
  <div style="padding: 5px">
    <el-form :model="form" style="margin: 15px 0 15px 0">
      <el-form-item>
        <!-- <span>
          {{ $t('common.Date') }}:
          <el-date-picker
            v-model="form.date"
            :clearable="false"
            type="date"
            placeholder="日期"
            value-format="yyyy-MM-dd"
            style="width:130px"
          />

          <el-time-picker
            v-model="form.times"
            is-range
            :clearable="false"
            type="time"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="HH:mm:ss"
            style="width:170px"
          />
        </span> -->

        <span>
          {{ $t("common.Date") }}:
          <el-date-picker
            v-model="form.start"
            type="datetime"
            :picker-options="minPickerOptions"
            @change="handleMinChange"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 185px"
            :placeholder="$t('page.usermanagement.StartDate')"
          ></el-date-picker>
          -
          <el-date-picker
            v-model="form.end"
            type="datetime"
            :picker-options="maxPickerOptions"
            @change="handleMaxChange"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 185px"
            :placeholder="$t('page.usermanagement.EndDate')"
          ></el-date-picker>
        </span>

        <span style="margin: 0 0 0 20px">
          {{ $t("common.Granularity") }}:
          <el-select v-model="form.minSpan" style="width: 65px">
            <el-option
              v-for="item in minSpanOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          min
        </span>

        <el-checkbox
          v-if="isWcs"
          v-model="form.bySup"
          style="margin: 0 0 0 20px"
          :label="$t('common.ByServicePackage')"
        />
        <el-checkbox
          v-if="!isWcs"
          v-model="form.bySup"
          style="margin: 0 0 0 20px"
          :label="$t('common.ByDwsNo')"
        />

        <span style="margin: 0 0 0 20px">
          {{ $t("common.SystemType") }}:
          <el-select v-model="systemTypeCode" style="width:120px" @change="handleOptionChange">
          <el-option v-for="item in systemOptions" :key="item.dbCode" :label="item.dbName" :value="item.dbCode" />
        </el-select>
        </span>

        <span style="margin: 0 0 0 20px">
          <el-button-group>
            <el-button
              style="margin-right: 15px"
              icon="el-icon-search"
              @click="loadData"
              type="primary"
              >{{ $t("common.Select") }}</el-button
            >
            <el-button
              style="margin-right: 15px"
              icon="el-icon-refresh-left"
              @click="resetForm"
              type="warning"
              >{{ $t("common.Reset") }}</el-button
            >
            <el-button icon="el-icon-download" @click="exportDataEvent">{{
              $t("common.Export")
            }}</el-button>

            <el-button
              v-if="!isWcs"
              style="margin-left: 15px"
              icon="el-icon-right"
              @click="viewPeak"
              type="primary"
              >{{ $t("common.ViewDWSPeakEffect") }}</el-button
            >
          </el-button-group>
        </span>
      </el-form-item>
    </el-form>
    <vxe-table
      ref="xTable"
      resizable
      show-overflow
      :height="tableHeight"
      :loading="loading"
      :footer-span-method="footerColspanMethod"
      :footer-method="footerMethod"
      :data="tableData"
      v-if="isWcs"
    >
      <vxe-table-column
        :resizable="true"
        type="seq"
        :title="$t('common.SerialNumber')"
        width="120"
      />
      <vxe-table-column
        :resizable="true"
        field="timespan"
        :title="$t('common.TimeInterval')"
      />
      <vxe-table-column
        :resizable="true"
        field="supNo"
        :title="$t('common.Supply')"
      />

      <vxe-table-column
        :resizable="true"
        field="scanBarcodeQuantity"
        :title="$t('common.NumberOfScannedBarcodeRecords')"
      />
      <vxe-table-column
        :resizable="true"
        field="packageLoaderQuantity"
        :title="$t('common.RecordTheNumberOfPackagesLoadedOntoTheVehicle')"
      />
      <vxe-table-column
        :resizable="true"
        field="fallFeedbackQuantity"
        :title="$t('common.NumberOfDropFeedbackRecords')"
      />
    </vxe-table>

    <vxe-table
      ref="xTable"
      resizable
      show-overflow
      :height="tableHeight"
      :loading="loading"
      :footer-span-method="footerColspanMethod"
      :footer-method="footerMethod"
      :data="tableData"
      v-if="!isWcs"
    >
      <vxe-table-column
        :resizable="true"
        type="seq"
        :title="$t('common.SerialNumber')"
        width="120"
      />
      <vxe-table-column
        :resizable="true"
        field="timespan"
        :title="$t('common.TimeInterval')"
      />
      <vxe-table-column
        :resizable="true"
        field="dwsNo"
        :title="$t('common.dwsNo')"
      />

      <vxe-table-column
        :resizable="true"
        field="scanQuantity"
        :title="$t('common.scanQuantity')"
      />
      <vxe-table-column
        :resizable="true"
        field="arrivalQuantity"
        :title="$t('common.arrivalQuantity')"
      />
      <vxe-table-column
        :resizable="true"
        field="passBackQuantity"
        :title="$t('common.passBackQuantity')"
      />
    </vxe-table>

    <!-- 右侧弹框  -->
    <div class="echartTable">
      <ShowDrawerC
        v-if="showModal"
        @send-data="handleDataFromChild"
        @close="closeModal"
        @changeDate="handleChangeDate"
        moduleTitle=""
        :chartAllData="chartAllData"
        :size="'50%'"
        :chartTable="chartTable"
        :drawerOpen="showModal"
        :isWcs="isWcs"
        :echartsDate="echartsDate"
        :dbCode="systemTypeCode"
        :sort="sort"
        :drawerLoading="drawerLoading"
      />
    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
import XEUtils from "xe-utils";
import moment from "moment";
import ShowDrawerC from "./drawerEcharts.vue";

export default {
  name: "SortingEfficiency",
  components: {
    ShowDrawerC,
  },
  data() {
    return {
      minSpanOptions: [
        { value: 5 },
        { value: 10 },
        { value: 15 },
        { value: 30 },
        { value: 60 },
      ],
      form: {
        start: this.getDate(0),
        end: this.getDate(1),
        // date: this.getDate(),
        // times: ['00:00:00', '23:59:59'],
        minSpan: 60,
        bySup: false,
      },
      tableHeight: window.innerHeight - 180,
      loading: false,
      tableData: [],
      dataTotal: "",
      minPickerOptions: {},
      maxPickerOptions: {
        disabledDate: (time) => {
          // 最大时间不能选择比最小时间早五天以上的时间
          if (this.form.start) {
            const minTimestamp = new Date(this.form.start).getTime();
            const maxTimestamp = time.getTime();
            return (
              maxTimestamp < minTimestamp - 1000 ||
              maxTimestamp > minTimestamp + 5 * 24 * 60 * 60 * 1000 - 1000
            );
          }
          return false;
        },
      },
      systemOptions: this.getBoxValue(),
      systemTypeCode: "1",
      isWcs: true,
      chartAllData: [], // 存放echarts数据
      showModal: false, // 展示弹框
      chartTable: [], // 表格
      echartsDate: moment().format("YYYY/MM/DD"),
      drawerLoading: false, // 子表loading
      sort: true, // echarts表排序 true: 升; false: 降  YYYYYYYYYYYY----需要搬
    };
  },
  methods: {
    handleDataFromChild(value, flag) {
      if (flag) {
        this.echartsDate = value;
      } else {
        this.echartsDate = value;
        this.viewPeak();
      }
    },
    handleChangeDate(value, num) {
      // if (this.sort === value && num === 1) {
      //   return this.$message({
      //     message: "请勿重复点击!",
      //     type: "warning",
      //     duration: 2 * 1000
      //   });
      // }
      this.sort = value;
      this.viewPeak();
    },
    // 点击查看峰值
    viewPeak() {
      this.drawerLoading = true;
      let url = "/dws/report/efficiency/peak";
      this.$nextTick(() => {
        this.showModal = true;
      });
      return request({
        url: url,
        method: "post",
        data: {
          curDate: this.echartsDate,
          dbCode: this.systemTypeCode,
          sort: this.sort,
        },
      })
        .then((resp) => {
          this.drawerLoading = false;
          let lineData = [];
          let xAxis = [];
          if (resp.data.result && resp.data.result.length <= 0) {
            this.chartTable = [];
            this.chartAllData = [];
            return this.$message({
              // $t('common.NoData') ||
              message: "暂无数据",
              type: "warning",
              duration: 2 * 1000,
            });
          }
          this.chartTable = resp.data.result;
          // console.log("9999999999999////////////", this.chartTable);
          resp.data.result.forEach((item) => {
            lineData.push(item.quantity);
            xAxis.push(item.dwsNo);
          });
          if (this.sort) {
            lineData.reverse();
            xAxis.reverse();
          }
          this.chartAllData = [lineData, xAxis];
          // console.log(
          //   "9999999999999----chartAllData*********",
          //   this.chartAllData
          // );
        })
        .catch((error) => {
          this.drawerLoading = false;
          // console.log("error----chartAllData*********", this.drawerLoading);
          this.$message({
            message: error || "加载失败！",
            type: "error",
            duration: 2 * 1000,
          });
        });
    },
    handleMinChange() {
      // 最小时间改变后，需要更新最大时间的可选范围
      if (this.form.start && this.form.end) {
        const minTimestamp = new Date(this.form.start).getTime();
        const maxTimestamp = new Date(this.form.end).getTime();
        if (
          maxTimestamp < minTimestamp ||
          maxTimestamp > minTimestamp + 5 * 24 * 60 * 60 * 1000
        ) {
          // 最大时间不符合要求，修改为符合要求的时间
          this.form.end = moment(
            new Date(
              Math.min(
                minTimestamp + 5 * 24 * 60 * 60 * 1000 - 1000,
                Date.now()
              )
            )
          ).format("YYYY-MM-DD HH:mm:ss");
        }
      }
    },
    // 关闭抽屉
    closeModal(value) {
      this.showModal = value;
    },
    handleMaxChange() {
      // 最大时间改变后，需要更新最小时间的可选范围
      if (this.form.start && this.form.end) {
        const minTimestamp = new Date(this.form.start).getTime();
        const maxTimestamp = new Date(this.form.end).getTime();
        if (
          maxTimestamp < minTimestamp ||
          maxTimestamp > minTimestamp + 5 * 24 * 60 * 60 * 1000
        ) {
          // 最大时间不符合要求，修改为符合要求的时间
          //this.form.end = moment(new Date(Math.min(minTimestamp + 5 * 24 * 60 * 60 * 1000, Date.now()))).format("YYYY-MM-DD hh:mm:ss");
        }
      }
    },
    loadData() {
      this.loading = true;
      let typeCode = this.systemTypeCode;
      let type = this.systemOptions.find(
        (option) => option.dbCode === typeCode
      ).dbType;

      let url = "/report/efficiency/list";
      if (type == "dws") {
        url = "/dws/report/efficiency/list";
      }
      if (type == "ffs") {
        url = "/ffs/report/efficiency/list";
      }
      return request({
        url: url,
        method: "post",
        data: {
          startTime: this.form.start,
          endTime: this.form.end,
          minSpan: this.form.minSpan,
          bySup: this.form.bySup,
          dbCode: typeCode,
        },
      })
        .then((resp) => {
          resp.data.result.forEach((element) => {
            if (element.supNo === 0) element.supNo = "";
          });
          this.dataTotal = resp.data.result[resp.data.result.length - 1];
          this.tableData = resp.data.result;
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
          this.$message({
            message: error || "加载失败！",
            type: "error",
            duration: 2 * 1000,
          });
        });
    },
    handleOptionChange() {
      let typeCode = this.systemTypeCode;
      let type = this.systemOptions.find(
        (option) => option.dbCode === typeCode
      ).dbType;
      if (type == "dws") {
        this.isWcs = false;
      } else {
        this.isWcs = true;
      }
      this.loadData();
    },
    footerMethod({ columns, data }) {
      const maxs = [];
      const sums = [];
      maxs.push("最大值");
      maxs.push(this.dataTotal.scanBarcodeQuantityMaximum);
      maxs.push(this.dataTotal.packageLoaderQuantityMaximum);
      maxs.push(this.dataTotal.fallFeedbackQuantityMaximum);

      sums.push("求和值");
      sums.push(this.dataTotal.scanBarcodeQuantityTotal);
      sums.push(this.dataTotal.packageLoaderQuantityTotal);
      sums.push(this.dataTotal.fallFeedbackQuantityTotal);
      // const maxs = []
      // const sums = []
      // columns.forEach((column, columnIndex) => {
      //   if (columnIndex === 0) {
      //     maxs.push('最大值')
      //     sums.push('求和值')
      //   } else {
      //     let maxCell1 = null
      //     let maxCell2 = null
      //     let maxCell3 = null
      //     let sumCell1 = null
      //     let sumCell2 = null
      //     let sumCell3 = null
      //     switch (column.property) {
      //       case 'scanQuantity':
      //         if (data.length === 0) {
      //           maxCell1 = 0
      //           maxs.push(maxCell1)
      //         } else {
      //           maxCell1 = (XEUtils.max(data, column.property)).scanQuantity
      //           maxs.push(maxCell1)
      //         }
      //         sumCell1 = XEUtils.sum(data, column.property)
      //         sums.push(sumCell1)
      //         break
      //       case 'supplyQuantity':
      //         if (data.length === 0) {
      //           maxCell2 = 0
      //           maxs.push(maxCell2)
      //         } else {
      //           maxCell2 = (XEUtils.max(data, column.property)).supplyQuantity
      //           maxs.push(maxCell2)
      //         }
      //         sumCell2 = XEUtils.sum(data, column.property)
      //         sums.push(sumCell2)
      //         break
      //       case 'sortingQuantity':
      //         if (data.length === 0) {
      //           maxCell3 = 0
      //           maxs.push(maxCell3)
      //         } else {
      //           maxCell3 = (XEUtils.max(data, column.property)).sortingQuantity
      //           maxs.push(maxCell3)
      //         }
      //         sumCell3 = XEUtils.sum(data, column.property)
      //         sums.push(sumCell3)
      //         break
      //     }
      //   }
      // })
      // 返回一个二维数组的表尾合计
      return [maxs, sums];
    },
    footerColspanMethod({ _columnIndex }) {
      if (_columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3,
        };
      }
    },
    getDate(format) {
      var today = new Date();
      var year = today.getFullYear();
      var month = today.getMonth() + 1;
      var date = today.getDate();

      if (format === 0) {
        return year + "-" + month + "-" + date + " 00:00:00";
      } else if (format === 1) {
        return year + "-" + month + "-" + date + " 23:59:59";
      }
      // var datetime = new Date()
      // var year = datetime.getFullYear()
      // var month = datetime.getMonth() + 1
      // var date = datetime.getDate()
      // return year + '-' + month + '-' + date
    },
    resetForm() {
      this.form = {
        start: this.getDate(0),
        end: this.getDate(1),
        // date: this.getDate(),
        // times: ['00:00:00', '23:59:59'],
        minSpan: 60,
        bySup: false,
      };
      this.tableData = [];
      this.tablePage = {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0,
      };
    },
    exportDataEvent() {
      //this.$refs.xTable.exportData({ filename: '效率统计（' + this.form.date + ' ' + this.form.times[0] + '至' + this.form.times[1] + '）', type: 'csv', isFooter: false })

      let typeCode = this.systemTypeCode;
      let type = this.systemOptions.find(
        (option) => option.dbCode === typeCode
      ).dbType;

      let url = "/report/efficiency/export";
      if (type == "dws") {
        url = "/dws/report/efficiency/export";
      }
      if (type == "ffs") {
        url = "/ffs/report/efficiency/export";
      }
      return request({
        url: url,
        method: "post",
        data: {
          startTime: this.form.start,
          endTime: this.form.end,
          minSpan: this.form.minSpan,
          bySup: this.form.bySup,
          dbCode: typeCode,
        },
        responseType: "blob",
      })
        .then((resp) => {
          const blob = new Blob([resp], { type: "application/vnd.ms-excel" });
          const a = document.createElement("a");
          a.href = URL.createObjectURL(blob);
          a.download =
            "效率统计（" +
            this.form.start +
            "至" +
            this.form.end +
            "）" +
            ".xlsx";
          a.style.display = "none";
          document.body.appendChild(a);
          a.click();
          URL.revokeObjectURL(a.href);
          document.body.removeChild(a);
          this.$message({
            message: this.$t("common.BeginExport"),
            type: "success",
            duration: 2 * 1000,
          });
        })
        .catch((error) => {
          this.$message({
            message: error || this.$t("common.ExportFailed"),
            type: "error",
            duration: 2 * 1000,
          });
        });
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params:{paramName: 'ScadaConnInfo'}
      }).then((res) => {
        console.log(res,'测试')
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    }
  },
};
</script>
