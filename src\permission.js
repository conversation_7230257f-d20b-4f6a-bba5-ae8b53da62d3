import router, { constantRoutes } from "./router";
import Layout from "@/layout";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { getToken } from "@/utils/auth"; // get token from cookie
import getPageTitle from "@/utils/get-page-title";
NProgress.configure({ showSpinner: false }); // NProgress Configuration
const whiteList = ["/login"]; // no redirect whitelist
import i18n from "./lang";

router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start();
  // 设置页面标题
  document.title = getPageTitle(to.meta.title);
  // 检查用户是否登录
  const hasToken = getToken();

  if (hasToken) {
    if (to.path === "/login") {
      // 已登录时访问登录页，重定向到首页
      next({ path: "/" });
      NProgress.done();
    } else {
      const hasGetUserInfo = store.getters.name;
      if (hasGetUserInfo) {
        next();
      } else {
        try {
          // 获取用户信息和路由
          await store.dispatch("user/getInfo");
          await store.dispatch("user/getRouters");
          const getAccessRoutes = store.state.user.userInfo;
          // 生成可访问路由
          const routeList = filterAsyncRouter(getAccessRoutes);
          // 合并常驻路由和动态路由
          const newRouters = constantRoutes.concat(routeList);

          // 动态添加路由
          router.options.routes = newRouters;
          router.addRoutes(newRouters);

          next({ ...to, replace: true });
        } catch (error) {
          // 移除token并跳转到登录页
          await store.dispatch("user/resetToken");
          Message.error(error || "发生错误");
          next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
          NProgress.done();
        }
      }
    }
  } else {
    /* 未登录 */
    if (whiteList.includes(to.path)) {
      // 在白名单中直接放行
      next();
    } else {
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`);
      NProgress.done();
    }
  }
});

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap) {
  return asyncRouterMap.filter(route => {
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component == "Layout") {
        route.component = Layout;
      } else {
        route.component = loadView(route.component);
      }
    }
    let str = "sidebar." + route.name;
    route.meta.title = i18n.t(str);

    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children);
    } else {
      delete route["children"];
      delete route["redirect"];
    }
    return true;
  });
}

export const loadView = view => {
  // 使用 import 实现生产环境的路由懒加载
  //return () => import(`@/views/${view}`)
  return resolve => require([`@/views/${view}`], resolve);
};

router.afterEach(() => {
  // finish progress bar
  NProgress.done();
});
