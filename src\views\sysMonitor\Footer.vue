<template>
  <div>
    <div id="gauge" style="width:500px;height:350px;"></div>
  <!-- <Footer></Footer> -->
  </div>
</template>

<script>
//import Footer from '@/views/sysMonitor/Footer.vue';
import echarts from 'echarts'
//require('echarts/theme/macarons') // echarts theme
export default {
  // components: {Footer},
  name: 'Footer',
  mounted() {
    this.SetGaugeEchart(0,0);
  },
  methods: {
    SetGaugeEchart(str1,str2) {
      let myChart = echarts.init(document.getElementById("gauge"));
      var option = {
        tooltip: {
          // a 系列名称  b  数据项名称  c  数值
          formatter: "{a} <br/>{c} {b}"
        },
        toolbox: {
          show: true,
          feature: {
            restore: { show: false },
            saveAsImage: { show: false }
          }
        },
        series: [
          {
            name: "运行速度",
            type: "gauge",
            center: ["25%", "50%"], // 默认全局居中
            // 定义居于上层，否则会被覆盖
            z: 3,
            min: 0,
            max: 10,
            // 分成多少等份
            splitNumber: 10,
            // 半径
            radius: "60%",
            axisLine: {
              // 坐标轴线
              lineStyle: {
                // 属性lineStyle控制线条样式
                width: 10
              }
            },
            axisTick: {
              // 坐标轴小标记
              length: 15, // 属性length控制线长
              lineStyle: {
                // 属性lineStyle控制线条样式
                color: "auto"
              }
            },
            splitLine: {
              // 分隔线
              length: 20, // 属性length控制线长
              lineStyle: {
                // 属性lineStyle（详见lineStyle）控制线条样式
                color: "auto"
              }
            },
            // 指针
            pointer: {
              width: 5
            },
            // 仪表盘内刻度提示显示样式
            axisLabel: {
              backgroundColor: "auto",
              borderRadius: 2,
              color: "#eee",
              padding: 3,
              textShadowBlur: 2,
              textShadowOffsetX: 1,
              textShadowOffsetY: 1,
              textShadowColor: "#222"
            },
            // 仪表盘内 单位 样式 km/h
            title: {
              // 其余属性默认使用全局文本样式，详见TEXTSTYLE
              fontWeight: "bolder",
              fontSize: 14,
              // 文字倾斜样式
              fontStyle: "italic"
            },
            detail: {
              // 其余属性默认使用全局文本样式，详见TEXTSTYLE
              // 设置内容提示格式
              formatter: function(value) {
                value = (value + "").split(".");
                value.length < 2 && value.push("00");
                return (
                  ("00" + value[0]).slice(-2) +
                  "." +
                  (value[1] + "00").slice(0, 2)
                );
              },
              // 内容文字粗细
              fontWeight: "bolder",
              // 内容盒子边框圆角
              // borderRadius: 3,
              // 内容盒子背景色
              // backgroundColor: '#444',
              // 内容盒子颜色
              // borderColor: '#aaa',
              // 阴影
              // shadowBlur: 5,
              // shadowColor: '#333',
              // shadowOffsetX: 0,
              // shadowOffsetY: 3,
              // 边框
              // borderWidth: 2,
              // 文字
              // textBorderColor: '#000',
              // textBorderWidth: 2,
              // textShadowBlur: 2,
              // textShadowColor: '#fff',
              // textShadowOffsetX: 0,
              // textShadowOffsetY: 0,
              fontSize: 18,
              fontFamily: "Arial",
              width: 50,
              // color: '#eee',
              rich: {}
            },
            // 当前的 值 和 单位
            data: [{ value: 0.2, name: "m/s" }]
          },
          {
            name: "小车占有率",
            type: "gauge",
            center: ["75%", "50%"], // 默认全局居中
            // 定义居于上层，否则会被覆盖
            z: 3,
            min: 0,
            max: 100,
            // 分成多少等份
            splitNumber: 10,
            // 半径
            radius: "60%",
            axisLine: {
              // 坐标轴线
              lineStyle: {
                // 属性lineStyle控制线条样式
                width: 10
              }
            },
            axisTick: {
              // 坐标轴小标记
              length: 15, // 属性length控制线长
              lineStyle: {
                // 属性lineStyle控制线条样式
                color: "auto"
              }
            },
            splitLine: {
              // 分隔线
              length: 20, // 属性length控制线长
              lineStyle: {
                // 属性lineStyle（详见lineStyle）控制线条样式
                color: "auto"
              }
            },
            // 指针
            pointer: {
              width: 5
            },
            // 仪表盘内刻度提示显示样式
            axisLabel: {
              backgroundColor: "auto",
              borderRadius: 2,
              color: "#eee",
              padding: 3,
              textShadowBlur: 2,
              textShadowOffsetX: 1,
              textShadowOffsetY: 1,
              textShadowColor: "#222"
            },
            // 仪表盘内 单位 样式 km/h
            title: {
              // 其余属性默认使用全局文本样式，详见TEXTSTYLE
              fontWeight: "bolder",
              fontSize: 14,
              // 文字倾斜样式
              fontStyle: "italic"
            },
            //
            detail: {
              // 其余属性默认使用全局文本样式，详见TEXTSTYLE
              // 设置内容提示格式
              formatter: function(value) {
                value = (value + "").split(".");
                value.length < 2 && value.push("00");
                return (
                  ("00" + value[0]).slice(-2) +
                  "." +
                  (value[1] + "00").slice(0, 2)
                );
              },
              // 内容文字粗细
              fontWeight: "bolder",
              // 内容盒子边框圆角
              // borderRadius: 3,
              // 内容盒子背景色
              // backgroundColor: '#444',
              // 内容盒子颜色
              // borderColor: '#aaa',
              // 阴影
              // shadowBlur: 5,
              // shadowColor: '#333',
              // shadowOffsetX: 0,
              // shadowOffsetY: 3,
              // 边框
              // borderWidth: 2,
              // 文字
              // textBorderColor: '#000',
              // textBorderWidth: 2,
              // textShadowBlur: 2,
              // textShadowColor: '#fff',
              // textShadowOffsetX: 0,
              // textShadowOffsetY: 0,
              fontSize: 18,
              fontFamily: "Arial",
              width: 50,
              // color: '#eee',
              rich: {}
            },
            // 当前的 值 和 单位
            data: [{ value: 0.2, name: "%" }]
          },
          // {
          //   name: "上层小车占有率",
          //   type: "gauge",
          //   // 圆心位置
          //   center: ["75%", "36%"], // 默认全局居中
          //   radius: "55%", // 圆半径
          //   min: 0,
          //   max: 100,
          //   startAngle:200,
          //   // 结束角度
          //   endAngle: -20,
          //   // 分成多少等份
          //   splitNumber: 10,
          //   axisLine: {
          //     // 坐标轴线
          //     lineStyle: {
          //       // 属性lineStyle控制线条样式
          //       width: 8
          //     }
          //   },
          //   axisTick: {
          //     // 坐标轴小标记
          //     length: 12, // 属性length控制线长
          //     lineStyle: {
          //       // 属性lineStyle控制线条样式
          //       color: "auto"
          //     }
          //   },
          //   splitLine: {
          //     // 分隔线
          //     length: 20, // 属性length控制线长
          //     lineStyle: {
          //       // 属性lineStyle（详见lineStyle）控制线条样式
          //       color: "auto"
          //     }
          //   },
          //   // 指针
          //   pointer: {
          //     width: 5
          //   },
          //   title: {
          //     // 位置
          //     //offsetCenter: [0, "-30%"] // x, y，单位px
          //   },
          //   detail: {
          //     show: false
          //     // 其余属性默认使用全局文本样式，详见TEXTSTYLE
          //     //fontWeight: "bolder"
          //   },
          //   data: [{ value: 0.2, name: "%" }]
          // },
          // {
          //   name: "下层小车占有率",
          //   type: "gauge",
          //   // 圆心位置
          //   center: ["75%", "76%"], // 默认全局居中
          //   radius: "55%", // 圆半径
          //   min: 0,
          //   max: 100,
          //   //clockWise : false,
          //   startAngle: 200,
          //   endAngle: -20,
          //   // 分成多少等份
          //   splitNumber: 10,
          //   axisLine: {
          //     // 坐标轴线
          //     lineStyle: {
          //       // 属性lineStyle控制线条样式
          //       width: 8
          //     }
          //   },
          //   axisTick: {
          //     // 坐标轴小标记
          //     length: 12, // 属性length控制线长
          //     lineStyle: {
          //       // 属性lineStyle控制线条样式
          //       color: "auto"
          //     }
          //   },
          //   splitLine: {
          //     // 分隔线
          //     length: 20, // 属性length控制线长
          //     lineStyle: {
          //       // 属性lineStyle（详见lineStyle）控制线条样式
          //       color: "auto"
          //     }
          //   },
          //   // 指针
          //   pointer: {
          //     width: 5
          //   },
          //   title: {
          //     // 位置
          //     //offsetCenter: [0, "-30%"] // x, y，单位px
          //   },
          //   detail: {
          //     show: false
          //     // 其余属性默认使用全局文本样式，详见TEXTSTYLE
          //     //fontWeight: "bolder"
          //   },
          //   data: [{ value: 0.2, name: "%" }]
          // },
          // {
          //   name: "转速",
          //   type: "gauge",
          //   // 圆心位置
          //   center: ["20%", "55%"], // 默认全局居中
          //   radius: "35%", // 圆半径
          //   min: 0,
          //   max: 7,
          //   // 结束角度
          //   endAngle: 45,
          //   // 分成多少等份
          //   splitNumber: 7,
          //   axisLine: {
          //     // 坐标轴线
          //     lineStyle: {
          //       // 属性lineStyle控制线条样式
          //       width: 8
          //     }
          //   },
          //   axisTick: {
          //     // 坐标轴小标记
          //     length: 12, // 属性length控制线长
          //     lineStyle: {
          //       // 属性lineStyle控制线条样式
          //       color: "auto"
          //     }
          //   },
          //   splitLine: {
          //     // 分隔线
          //     length: 20, // 属性length控制线长
          //     lineStyle: {
          //       // 属性lineStyle（详见lineStyle）控制线条样式
          //       color: "auto"
          //     }
          //   },
          //   // 指针
          //   pointer: {
          //     width: 5
          //   },
          //   title: {
          //     // 位置
          //     offsetCenter: [0, "-30%"] // x, y，单位px
          //   },
          //   detail: {
          //     // 其余属性默认使用全局文本样式，详见TEXTSTYLE
          //     fontWeight: "bolder"
          //   },
          //   data: [{ value: 1.5, name: "x1000 r/min" }]
          // },
          // {
          //   name: "油表",
          //   type: "gauge",
          //   // 圆心
          //   center: ["77%", "50%"], // 默认全局居中
          //   // 半径
          //   radius: "25%",
          //   min: 0,
          //   max: 2,
          //   // 开始角度
          //   startAngle: 135,
          //   // 结束角度
          //   endAngle: 45,
          //   // 分几等份
          //   splitNumber: 2,
          //   axisLine: {
          //     // 坐标轴线
          //     lineStyle: {
          //       // 属性lineStyle控制线条样式
          //       width: 8
          //     }
          //   },
          //   axisTick: {
          //     // 坐标轴小标记
          //     splitNumber: 5,
          //     length: 10, // 属性length控制线长
          //     lineStyle: {
          //       // 属性lineStyle控制线条样式
          //       color: "auto"
          //     }
          //   },
          //   // 设置内容提示格式
          //   axisLabel: {
          //     formatter: function(v) {
          //       switch (v + "") {
          //         case "0":
          //           return "E";
          //         case "1":
          //           return "Gas";
          //         case "2":
          //           return "F";
          //       }
          //     }
          //   },
          //   splitLine: {
          //     // 分隔线
          //     length: 15, // 属性length控制线长
          //     lineStyle: {
          //       // 属性lineStyle（详见lineStyle）控制线条样式
          //       color: "auto"
          //     }
          //   },
          //   pointer: {
          //     width: 2
          //   },
          //   title: {
          //     show: false
          //   },
          //   detail: {
          //     show: false
          //   },
          //   data: [{ value: 0.5, name: "gas" }]
          // },
          // {
          //   name: "水表",
          //   type: "gauge",
          //   center: ["77%", "50%"], // 默认全局居中
          //   radius: "25%",
          //   min: 0,
          //   max: 2,
          //   startAngle: 315,
          //   endAngle: 225,
          //   splitNumber: 2,
          //   axisLine: {
          //     // 坐标轴线
          //     lineStyle: {
          //       // 属性lineStyle控制线条样式
          //       width: 8
          //     }
          //   },
          //   axisTick: {
          //     // 坐标轴小标记
          //     show: false
          //   },
          //   axisLabel: {
          //     formatter: function(v) {
          //       switch (v + "") {
          //         case "0":
          //           return "H";
          //         case "1":
          //           return "Water";
          //         case "2":
          //           return "C";
          //       }
          //     }
          //   },
          //   splitLine: {
          //     // 分隔线
          //     length: 15, // 属性length控制线长
          //     lineStyle: {
          //       // 属性lineStyle（详见lineStyle）控制线条样式
          //       color: "auto"
          //     }
          //   },
          //   pointer: {
          //     width: 2
          //   },
          //   title: {
          //     show: false
          //   },
          //   detail: {
          //     show: false
          //   },
          //   data: [{ value: 0.5, name: "gas" }]
          // }
        ]
      };

      option.series[0].data[0].value = str1;
        option.series[1].data[0].value = str2;
        myChart.setOption(option, true);
      // setInterval(function() {
      //   option.series[0].data[0].value = (Math.random() * 2).toFixed(2) - 0;
      //   option.series[1].data[0].value = (Math.random() * 7).toFixed(2) - 0;
      //   // option.series[2].data[0].value = (Math.random() * 2).toFixed(2) - 0;
      //   // option.series[3].data[0].value = (Math.random() * 2).toFixed(2) - 0;
      //   myChart.setOption(option, true);
      // }, 2000);
    }
  }
};
</script>