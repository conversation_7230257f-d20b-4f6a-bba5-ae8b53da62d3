<template>
  <div style="padding:5px">

    <el-form :model="form" style="margin:15px 0 15px 0">
      <el-form-item>
        <span>
          {{ $t('common.Date') }}:
          <el-date-picker v-model="form.start" :clearable="false" type="datetime"
            :placeholder="$t('page.usermanagement.StartDate')" value-format="yyyy-MM-dd HH:mm:ss" style="width:185px" />
          -
          <el-date-picker v-model="form.end" :clearable="false" type="datetime"
            :placeholder="$t('page.usermanagement.EndDate')" value-format="yyyy-MM-dd HH:mm:ss" style="width:185px" />
        </span>

        <span style="margin:0 0 0 20px">
          {{ $t('common.Chute') }}:
          <el-input v-model="form.chuteNo" style="width:136px" />
        </span>

        <span style="margin:0 0 0 20px">
          {{ $t('common.PackageGrade') }}:
          <el-input v-model="form.packageNumber" style="width:136px" />
        </span>

        <span style="margin:0 0 0 20px">
          {{ $t('common.ChipNumber') }}:
          <el-input v-model="form.rfId" style="width:150px" />
        </span>

        <span style="margin: 0 0 0 20px">
          {{ $t('common.SystemType') }}:
          <el-select v-model="systemTypeCode" style="width:120px">
            <el-option v-for="item in systemOptions" :key="item.dbCode" :label="item.dbName" :value="item.dbCode" />
          </el-select>
        </span>

        <span style="margin:0 0 0 20px">
          <el-button-group>
            <el-button style="margin-right:15px" icon="el-icon-search" @click="setloadData" type="primary">{{
              $t('common.Select') }}</el-button>
            <el-button style="margin-right:15px" icon="el-icon-refresh-left" @click="resetForm" type="warning">{{
              $t('common.Reset') }}</el-button>
            <el-button icon="el-icon-download" @click="exportDataEvent">{{ $t('common.Export') }}</el-button>
          </el-button-group>
        </span>
      </el-form-item>
    </el-form>
    <vxe-table ref="xTable" resizable show-overflow :height="tableHeight" row-id="id" :loading="loading"
      :data="tableData">
      <vxe-table-column :resizable="true" type="seq" :title="$t('common.SerialNumber')" width="120" />
      <vxe-table-column :resizable="true" field="businessDate" :title="$t('common.BindingTime')" />
      <vxe-table-column :resizable="true" field="chuteNo" :title="$t('common.Chute')" />
      <vxe-table-column :resizable="true" field="packageNumber" :title="$t('common.PacketNumber')" />
      <vxe-table-column :resizable="true" field="rfId" :title="$t('common.ChipNumber')" />
      <vxe-table-column :resizable="true" field="userCode" :title="$t('common.BindingPersonnel')" />
    </vxe-table>
    <vxe-pager background :loading="loading" :current-page="tablePage.currentPage" :page-size="tablePage.pageSize"
      :total="tablePage.totalResult" :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange" />
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'PackageNumberLog',
  data() {
    return {
      form: {
        start: this.getDate(0),
        end: this.getDate(1),
        step: ''
      },
      tableHeight: window.innerHeight - 210,
      loading: false,
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      },
      systemOptions:[],
      
      systemTypeCode: '1',
      systemType: '',
    }
  },
  methods: {
    getBoxValue() {
      return request({
        url: '/dataSource/list?selectType=WCS&selectType=FFS',
        method: 'get',
        // params: { paramName: 'ScadaConnInfo',selectType:'WCS',selectType:'FFS' }
      }).then((res) => {

        this.systemOptions = res.data.result
        console.log(this.systemOptions, '测试')
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    },
    getDate(format) {
      var today = new Date()
      var year = today.getFullYear()
      var month = today.getMonth() + 1
      var date = today.getDate()

      if (format === 0) {
        return year + '-' + month + '-' + date + ' 00:00:00'
      } else if (format === 1) {
        return year + '-' + month + '-' + date + ' 23:59:59'
      }
    },
    getSelectType() {
      this.systemOptions.forEach(item => {
        if (item.dbCode == this.systemTypeCode) {
          this.systemType = item.dbType
        }
      })

    },
    /** 搜索按钮操作 */
    setloadData() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    loadData() {

      this.getSelectType()
      let url = ""
      console.log(this.systemType,'测试')
      if (this.systemType == 'ffs') {
        url = '/ffs/log/chute/list'
      } else {
        url = '/log/chute/list'
      }

      this.loading = true
      return request({
        url: url,
        method: 'post',
        data: { startTime: this.form.start, dbCode: this.systemTypeCode,endTime: this.form.end, chuteNo: this.form.chuteNo, packageNumber: this.form.packageNumber, rfId: this.form.rfId, pageSize: this.tablePage.pageSize, curPage: this.tablePage.currentPage }
      }).then(resp => {
        this.tableData = resp.data.result.records
        this.tablePage.currentPage = resp.data.result.current
        this.tablePage.pageSize = resp.data.result.size
        this.tablePage.totalResult = resp.data.result.total
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || '加载失败！',
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.loadData()
    },
    resetForm() {
      this.form = {
        start: this.getDate(0),
        end: this.getDate(1)
      }
      this.tableData = []
      this.tablePage = {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      }
    },
    exportDataEvent() {
      this.loading = true
      this.getSelectType()
      let url = ""
      console.log(this.systemType,'测试')
      if (this.systemType == 'ffs') {
        url = '/ffs/log/chute/export'
      } else {
        url = '/log/chute/export'
      }
      return request({
        url:url,
        method: 'post',
        data: { startTime: this.form.start,  dbCode: this.systemTypeCode,endTime: this.form.end, chuteNo: this.form.chuteNo, packageNumber: this.form.packageNumber, rfId: this.form.rfId },
        responseType: 'blob'
      }).then(resp => {
        const blob = new Blob([resp], { type: 'application/vnd.ms-excel' })
        const a = document.createElement('a')
        a.href = URL.createObjectURL(blob)
        a.download = '历史包号.xlsx'
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(a.href)
        document.body.removeChild(a)
        this.$message({
          message: this.$t('common.BeginExport'),
          type: 'success',
          duration: 2 * 1000
        })
        // this.$refs.xTable.exportData({
        //   filename: '历史包号',
        //   type: 'csv',
        //   data: resp.data.result
        // })
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || this.$t('common.ExportFailed'),
          type: 'error',
          duration: 2 * 1000
        })
      })
    }

  },
  mounted() {
    this.getBoxValue()
  }

}
</script>
