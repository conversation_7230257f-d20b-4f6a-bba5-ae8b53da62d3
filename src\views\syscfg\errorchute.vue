<template>
  <div style="padding:5px">

    <el-button-group style="margin-top:15px;margin-bottom:15px">
      <el-button style="margin-right:10px" icon="el-icon-refresh" @click="loadData" type="primary">{{
        $t('page.Serviceusermanagement.Refresh') }}</el-button>
      <el-button style="margin-right:10px" icon="el-icon-edit" @click="updateData" type="info">{{
        $t('page.Serviceusermanagement.Save') }}</el-button>
      <el-button icon="el-icon-upload2" @click="updateData2">{{ $t('common.UpdateCache') }}</el-button>
    </el-button-group>
    <span style="margin: 0 0 0 20px">
      {{ $t('common.SystemType') }}:
      <el-select v-model="systemTypeCode" style="width:120px" @change="handleOptionChange">
        <el-option v-for="item in systemOptions" :key="item.dbCode" :label="item.dbName" :value="item.dbCode" />
      </el-select>
    </span>
    <!-- <span style="margin: 0 0 0 20px">
      内外:
      <el-select v-model="circleType" style="width:120px" @change="handleOptionChange">
        <el-option v-for="item in circleTypeOption" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </span> -->

    <vxe-table resizable show-overflow :loading="loading" :data="tableData"
      :edit-config="{ trigger: 'click', mode: 'cell' }" @edit-closed="handlerEditClosed">
      <vxe-table-column type="seq" :title="$t('common.SerialNumber')" width="120" :resizable="true" />
      <vxe-table-column v-if="systemTypeCode == '5'" type="paramType" title="内外" width="120" :resizable="true">
        <template #default="{ row }">

          <div> {{ row.paramType == '2' ? "外圈" : "内圈" }}</div>
        </template>
      </vxe-table-column>
      <vxe-table-column field="paramName" :title="$t('common.ParameterName')" :resizable="true" />
      <vxe-table-column field="paramValue" :title="$t('common.ParameterValue')" :resizable="true"
        :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
      <vxe-table-column field="paramRemark" :title="$t('common.Memo')" :resizable="true" />
      <vxe-table-column field="updateDate" :title="$t('common.UpdateDate')" :resizable="true" />
    </vxe-table>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'ErrorChute',
  data() {
    return {
      tableData: [],
      loading: false,
      edited: false,
      checked: false,
      systemOptions: this.getBoxValue(),
      // showParamType:false,
      // circleTypeOption:[{
      //   label:'内',
      //   value:'1'
      // },
      // {
      //   label:'外',
      //   value:'2'
      // }],
      systemTypeCode: "1",
      circleType: '2',//外圈
    }
  },
  mounted() {
     let url = '/cfg/chute/list'
     this.loading = true
      return request({
        url: `${url}?dbCode=${this.systemTypeCode}`,
        method: 'post',
        data: { curPage: 1, pageSize: 999, dbCode: this.systemTypeCode }
      }).then(response => {
        this.tableData = response.data.result
        this.edited = false
        this.checked = false
        this.loading = false
      }).catch(error => {
        this.$message({
          message: error,
          type: 'error',
          duration: 2 * 1000
        })
        this.loading = false
      })
  },
  methods: {
    handlerEditClosed({ row }) {
      var regValue = /^([1-9],|[1-9]\d,|[1-3]\d\d,|4[0-7]\d,|480,)*([1-9]|[1-9]\d|[1-3]\d\d|4[0-7]\d|480)$/
      if (!regValue.test(row.paramValue)) {
        this.$message({
          message: '参数值格式错误！',
          type: 'warning',
          duration: 2 * 1000
        })
      } else {
        this.checked = true
        this.edited = true
        // row.dbCode = this.systemTypeCode
        // return request({
        //     url: '/cfg/chute/edit',
        //     method: 'post',
        //     data: row
        //   }).then(response => {
        //     this.checked = true
        //     this.edited = true
        //     this.loadData()
        //     this.$message({
        //       message: this.$t('common.EditSuccess'),
        //       type: 'success',
        //       duration: 2 * 1000
        //     })
        //   }).catch(error => {
        //     this.loading = false
        //     this.$message({
        //       message: error,
        //       type: 'error',
        //       duration: 2 * 1000
        //     })
        //   })
      }
    },
    loadData() {

      let url = '/cfg/chute/list'

      let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;
      console.log(type, '测试数据')

      if (type == 'ffs') {
        url = '/ffs/cfg/chute/list'
      }

      this.loading = true
      return request({
        url: `${url}?dbCode=${this.systemTypeCode}`,
        method: 'post',
        data: { curPage: 1, pageSize: 999, dbCode: this.systemTypeCode }
      }).then(response => {
        this.tableData = response.data.result
        this.edited = false
        this.checked = false
        this.loading = false
      }).catch(error => {
        this.$message({
          message: error,
          type: 'error',
          duration: 2 * 1000
        })
        this.loading = false
      })
    },
    updateData() {

      let url = `/cfg/chute/batch/edit?dbCode=${this.systemTypeCode}`

      let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;
      console.log(type, '测试数据')

       if (type == 'ffs') {
            url = `/ffs`+url
          }

      if (this.edited) {
        if (this.checked) {
          this.loading = true

          this.tableData.forEach(element => {
            element.dbCode = this.systemTypeCode
          })
          return request({
            url: url,
            method: 'post',
            data: this.tableData
          }).then(response => {
            this.edited = false
            this.checked = false
            this.loading = false
            this.loadData()
            this.$message({
              message: this.$t('common.EditSuccess'),
              type: 'success',
              duration: 2 * 1000
            })
          }).catch(error => {
            this.loading = false
            this.$message({
              message: error,
              type: 'error',
              duration: 2 * 1000
            })
          })
        } else {
          this.$message({
            message: '参数值格式错误',
            type: 'warning',
            duration: 2 * 1000
          })
        }
      } else {
        this.$message({
          message: '无需要保存的数据',
          type: 'warning',
          duration: 2 * 1000
        })
      }
    },
    handleOptionChange() {
      this.loadData()
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params: { paramName: 'ScadaConnInfo' }
      }).then((res) => {
        console.log(res, '测试')
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    },
    updateData2() {
      if (true) {
        this.$confirm('Updating the cache will immediately update the WCS exception grid. Please confirm whether to continue execution？', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(() => {
          this.loading = true
          let url = '/cfg/chute/update/cache'

          let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;
          console.log(type, '测试数据')

          if (type == 'ffs') {
            url = '/ffs/cfg/chute/update/cache'
          }
          return request({
            url: url,
            method: 'get',
            params: { dbCode: this.systemTypeCode }
            //data: this.tableData
          }).then(response => {
            this.loading = false
            this.$message({
              message: response.msg,
              type: 'success',
              duration: 2 * 1000
            })
          }).catch(error => {
            this.loading = false
            this.$message({
              message: error,
              type: 'error',
              duration: 2 * 1000
            })
          })
        })
      } else {
        this.$message({
          message: '请先保存数据！',
          type: 'warning',
          duration: 2 * 1000
        })
      }
    }
  }
}
</script>
