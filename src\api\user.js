import request from '@/utils/request'
import { getToken } from '@/utils/auth'

export function login(data) {
  return request({
    url: '/login',
    method: 'post',
    data
  })
}

export function getInfo(token) {
  return request({
    url: '/getInfo',
    method: 'get',
    headers: {
      'token': getToken()
    }

    //params: { token }
  })
}


export function getRouters(token) {
  return request({
    url: '/getRouters',
    method: 'get',
    headers: {
      'token': getToken()
    }

    //params: { token }
  })
}

export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}


export function captchaImage() {
  return request({
    url: '/captchaImage',
    method: 'get',
    params: {  },
    headers:{

    }
  })
}
