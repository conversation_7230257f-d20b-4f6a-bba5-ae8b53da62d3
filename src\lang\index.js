// 导入必要的依赖
import Vue from 'vue'
import VueI18n from 'vue-i18n'

// 引入各个语言配置文件
import zh from './zh'  // 中文语言包
import th from './th'  // 泰文语言包
import en from './en'  // 英文语言包
import ph from './ph'  // 菲律宾语言包
import id from './id'  // 印尼语言包
import ms from './ms'  // 马来语言包
import vi from './vi'  // 越南语言包
import es from './es'  // 西班牙语言包
import pt from './pt'  // 葡萄牙语言包
import Cookies from 'js-cookie'  // Cookie操作工具

// 安装 VueI18n 插件
Vue.use(VueI18n)

// 获取当前语言设置，如果 Cookie 中没有设置，默认使用中文
let loc = Cookies.get('locale') ? Cookies.get('locale') : 'zh'

// 创建 VueI18n 实例
const i18n = new VueI18n({
  // 设置默认语言
  locale: loc,  // 当前语言标识
  
  // 配置所有支持的语言包
  messages: {
    zh,    // 中文
    en,    // 英文
    ph,    // 菲律宾语
    th,    // 泰文
    ms,    // 马来语
    vi,    // 越南语
    es,    // 西班牙语
    pt,    // 葡萄牙语
    id ,    // 印尼语
  }
})

// 导出 i18n 实例供其他模块使用
export default i18n 