<template>
  <!--  -->

  <div class="error_warning" :style="{ height: !isTwo ? '502px' : '406px' }">
    <div class="tittle">{{ $t("scada.AbnormalInfo") }}</div>
    <div
      class="list"
      :style="{ height: !isTwo ? '450px' : '360px', overflow: 'auto' }"
    >
      <div class="list_item" v-for="(item, index) in alerts" :key="index">
        <div class="icon"></div>
        <div class="txt">{{ item.message }}</div>
        <div class="time">{{ item.createTime }}</div>
      </div>
    </div>
  </div>
</template>
  
  <script>
import request from "../../../utils/request";
import moment from "moment";
export default {
  name: "AndInfo",
  props: {
    isTwo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // alerts: [{message: "test", createTime: "2025-01-20 10:10"},{message: "test", createTime: "2025-01-20 10:10"},{message: "test", createTime: "2025-01-20 10:10"},{message: "test", createTime: "2025-01-20 10:10"},{message: "test", createTime: "2025-01-20 10:10"},{message: "test", createTime: "2025-01-20 10:10"},{message: "test", createTime: "2025-01-20 10:10"},{message: "test", createTime: "2025-01-20 10:10"},{message: "test", createTime: "2025-01-20 10:10"},{message: "test", createTime: "2025-01-20 10:10"},],
      alerts: [],
      setTimeoutId: null,
    };
  },
  mounted() {
    this.getContent();
    this.setTimeoutId = null;
    this.startTimer();
  },
  beforeDestroy() {
    clearTimeout(this.setTimeoutId); //
  },
  methods: {
    startTimer() {
      this.setTimeoutId = setTimeout(() => {
        this.getContent();
        this.startTimer(); // 递归调用以实现定时效果
      }, 60000);
    },
    getContent() {
      return request({
        url: `/log/alarm/real/list?dbCode=${this.$route.query.dbCode}`,
        method: "get",
      })
        .then((response) => {
          if (
            response &&
            response.data &&
            Array.isArray(response.data.result)
          ) {
            const optionsList = response.data.result.map((item) => {
              const message = item.message || "无内容";
              const createTime = moment(item.createTime).format(
                "YYYY-MM-DD HH:mm"
              );
              return { message, createTime };
            });
            this.alerts = optionsList;
          } else {
            this.alerts = [];
          }
        })
        .catch((error) => {
          console.log("error", error);
          this.alerts = [];
        });
    },
  },
};
</script>

<style scoped lang="scss">
.error_warning {
  width: 624px;
  height: 502px;
  // height: 255px;
  // background-color: bisque;
  padding: 18px 16px;
  box-sizing: border-box;
  background-color: #ffffff;

  .tittle {
    font-weight: 400;
    font-size: 16px;
    color: #191919;
    // padding-bottom: 18px;
  }

  .list {
    padding: 18px 0 0 0;
    overflow: auto;
    height: 455px;
    .list_item {
      display: flex;
      padding: 0 0 16px 0;
      // overflow: auto;

      .icon {
        width: 5px;
        height: 5px;
        background: #ff5161;
        margin: 7px 5px 0 0;
        border-radius: 10px;
        // flex: 1;
      }

      .txt {
        font-size: 13px;
        color: #737373;
        flex: 40;
        white-space: nowrap;
        /* 禁止换行 */
        overflow: hidden;
        /* 隐藏溢出内容 */
        text-overflow: ellipsis;
        /* 显示省略号 */
      }

      .time {
        font-size: 13px;
        color: #262626;
        flex: 11;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .grid-container {
    width: 95%;
    margin: 0.75rem auto;
  }

  .alert-item {
    padding: 0.5rem 0;
  }

  .alert-content {
    min-width: 150px;
  }
}

@media screen and (max-width: 480px) {
  .grid-container {
    width: 98%;
    padding: 0.75rem;
  }

  .alert-list {
    height: clamp(6rem, 30vh, 12rem);
  }

  .alert-item {
    flex-direction: column;
    gap: 0.25rem;
  }

  .alert-time {
    padding-left: 1rem;
  }
}

.no-data {
  height: 17.4rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 14px;
}
</style>