<template>
  <div style="padding:5px">

    <el-button-group style="margin-top:15px;margin-bottom:15px">
      <el-button style="margin-right:10px" icon="el-icon-refresh" @click="loadData" type="primary">{{ $t('page.Serviceusermanagement.Refresh') }}</el-button>
      <!-- <el-button icon="el-icon-edit" @click="updateData" type="info">{{ $t('page.Serviceusermanagement.Save') }}</el-button> -->
    </el-button-group>

    
    <span style="margin: 0 0 0 20px">
        {{$t('common.SystemType')}}:
          <el-select v-model="systemTypeCode" style="width:120px" @change="handleOptionChange">
            <el-option
              v-for="item in systemOptions"
              :key="item.dbCode"
              :label="item.dbName"
              :value="item.dbCode"
            />
          </el-select>
        </span>

    <vxe-table
      resizable
      show-overflow
      :loading="loading"
      :data="tableData"
      :edit-config="{trigger: 'click', mode: 'cell'}"
      @edit-closed="handlerEditClosed"
    >
      <vxe-table-column type="seq" :title="$t('common.SerialNumber')" width="120" :resizable="true" />
      <vxe-table-column field="paramName" :title="$t('common.ParameterName')" :resizable="true" />
      <vxe-table-column field="paramValue" :title="$t('common.ParameterValue')" :resizable="true" :edit-render="{name: 'input', immediate: true, attrs: {type: 'number',min:1}}" />
      <vxe-table-column field="paramRemark" :title="$t('common.Memo')" :resizable="true" />
      <vxe-table-column field="updateDate" :title="$t('common.UpdateDate')" :resizable="true" />
    </vxe-table>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'SaveDay',
  data() {
    return {
      loading: false,
      tableData: [],
      edited: false,
      systemOptions:this.getBoxValue(),
      systemTypeCode:"1"
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handlerEditClosed({ row }) {
      row.dbCode = this.systemTypeCode
      return request({
          url: '/cfg/save/edit',
          method: 'post',
          data: row
        }).then(response => {
          this.edited = true;
          this.loadData()
          this.$message({
            message: this.$t('common.EditSuccess'),
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.loading = false
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
    },
    handleOptionChange() {
      this.loadData()
    },
    loadData() {
      this.loading = true
      return request({
        url: '/cfg/save/list',
        method: 'post',
        data: {curPage:1,pageSize:999,dbCode: this.systemTypeCode }
      }).then(response => {
        this.tableData = response.data.result
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error,
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    updateData() {
      if (this.edited) {
        this.loading = true
        return request({
          url: '/saveday/updatedata',
          method: 'post',
          data: this.tableData
        }).then(response => {
          this.edited = false
          this.loading = false
          this.loadData()
          this.$message({
            message: '保存成功！',
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.loading = false
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      } else {
        this.$message({
          message: '无需要保存的数据！',
          type: 'warning',
          duration: 2 * 1000
        })
      }
    },
    getBoxValue() {
      return request({
        url: '/obtain/db/getParamValueByName',
        method: 'get',
        params:{paramName: 'ScadaConnInfo'}
      }).then((response) => {
        this.systemOptions = JSON.parse(response.msg)
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    }
  }
}
</script>
