<template>
  <div style="padding:5px;overflow: hidden; width: 100%;">

    <el-button-group style="margin-top:15px;margin-bottom:15px">
      <!-- <el-button icon="el-icon-refresh" style="margin-right:10px" @click="loadUserInfo">{{ $t('common.Select') }}</el-button> -->
      <el-button icon="el-icon-refresh" style="margin-right:10px" @click="loadUserInfo" type="primary">{{$t('page.Serviceusermanagement.Refresh')}}</el-button>
      <el-button icon="el-icon-circle-plus-outline" style="margin-right:10px" @click="showNewUserInfoModal" type="success">{{$t('page.Serviceusermanagement.AddUser')}}</el-button>
      <el-button icon="el-icon-delete" @click="deleteUserInfos" type="danger">{{$t('page.Serviceusermanagement.BulkDelete')}}</el-button>
      <span style="margin: 0 0 0 20px">
        {{$t('page.Serviceusermanagement.FullName')}}:
          <el-input v-model="form.nameStr" style="width: 136px" />
        </span>
        <span style="margin: 0 0 0 20px">
          {{$t('page.Serviceusermanagement.EmployeeID')}}:
          <el-input v-model="form.codeStr" style="width: 136px" />
        </span>
        <span style="margin: 0 0 0 20px">
      {{ $t('common.SystemType') }}:
      <el-select v-model="systemTypeCode" style="width:120px" @change="handleOptionChange">
        <el-option v-for="item in systemOptions" :key="item.dbCode" :label="item.dbName" :value="item.dbCode" />
      </el-select>
    </span>

        <!-- <span style="margin: 0 0 0 20px">
        {{$t('common.SystemType')}}:
          <el-select v-model="form.systemType" style="width:95px">
            <el-option
              v-for="item in systemOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </span> -->

        <span style="margin: 0 0 0 20px">
          <el-button-group>
            <el-button style="margin-right: 15px" icon="el-icon-search" @click="loadUserInfo" type="primary">{{$t('page.Serviceusermanagement.Query')}}</el-button>
            <el-upload
          style="display:inline-block;margin-right:15px"
          :action="uploadUrl"
          accept=".xls"
          :data="{
            dbCode:systemTypeCode
          }"
          :show-file-list="false"
          :on-success="importDetailSucc"
          :headers="headerObj"
        >
          <el-button style="margin-right:15px" icon="el-icon-upload2">{{$t('page.Serviceusermanagement.Import')}}</el-button>
          <span slot="tip" class="el-upload__tip">{{$t('page.Serviceusermanagement.OnlyExcel')}}</span>
        </el-upload>
          <el-button icon="el-icon-download" @click="exportDataEvent">{{ $t('common.Export') }}</el-button>
          </el-button-group>
        </span>
      
    </el-button-group>
       


    <vxe-table
      ref="xTableUserInfo"
      show-overflow
      row-id="id"
      :loading="loading"
      :data="tableData"
      :checkbox-config="{highlight: true, range: true}"
    >
      <vxe-table-column type="checkbox" width="50" :resizable="true" />
      <vxe-table-column type="seq" :title="$t('page.Serviceusermanagement.SerialNumber')" width="120" :resizable="true" />
      <vxe-table-column field="userCode" :title="$t('page.Serviceusermanagement.UserEmployeeID')" :resizable="true" />
      <vxe-table-column field="userName" :title="$t('page.Serviceusermanagement.UserName')" :resizable="true" />
      <vxe-table-column field="id" :title="$t('page.Serviceusermanagement.UserID')" :resizable="true" v-if="false" />
      <vxe-table-column field="createBy" :title="$t('page.Serviceusermanagement.Creator')" :resizable="true" />
      <vxe-table-column field="createTime" :title="$t('page.Serviceusermanagement.CreationTime')" :resizable="true" />
      <vxe-table-column :title="$t('page.Serviceusermanagement.Operation')" fixed="right" :resizable="true">
        <template v-slot="{ row }">
          <el-row>
            <el-button type="primary" @click="showUpdateUserInfoModal(row)" icon="el-icon-edit" circle></el-button>
            <el-button type="danger" @click="deleteUserInfo(row)" icon="el-icon-delete" circle></el-button>
          </el-row>
        </template>
      </vxe-table-column>
    </vxe-table>

    <vxe-modal ref="newUserInfoModal" v-model="newUserInfoModal" :title="$t('page.Serviceusermanagement.AddUser')" width="500" destroy-on-close>
      <vxe-form ref="newUserInfoForm" title-align="top" :data="newUserInfoForm" :rules="newUserInfoFormRules" @submit="addUserInfo">
        <vxe-form-item :title="$t('page.Serviceusermanagement.UserEmployeeID')" field="userCode" span="24" :item-render="{name: 'input', attrs: {placeholder: $t('common.EmployeeID')}}" />
        <vxe-form-item :title="$t('page.Serviceusermanagement.UserName')" field="userName" span="24" :item-render="{name: 'input', attrs: {placeholder: $t('common.FullName')}}" />
        <vxe-form-item :title="$t('page.Serviceusermanagement.UserPassword')" field="userPassword" span="24" :item-render="{name: 'input', attrs: {placeholder:$t('common.Password')}}" />
        <vxe-form-item :title="$t('page.Serviceusermanagement.ConfirmPassword')" field="userPassword2" span="24" :item-render="{name: 'input', attrs: {placeholder: $t('common.ConfirmPassword')}}" />
        <vxe-form-item align="right" span="24">
          <vxe-button @click="$refs.newUserInfoModal.close()">{{$t('page.Serviceusermanagement.Cancel')}}</vxe-button>
          <vxe-button type="submit" status="danger">{{$t('page.Serviceusermanagement.Save')}}</vxe-button>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>

    <vxe-modal ref="updateUserInfoModal" v-model="updateUserInfoModal" :title="$t('page.Serviceusermanagement.UpdateUser')" width="500" destroy-on-close>
      <vxe-form ref="updateUserInfoForm" title-align="top" :data="updateUserInfoForm" :rules="updateUserInfoFormRules" @submit="updateUserInfo">
        <vxe-form-item :title="$t('page.Serviceusermanagement.UserEmployeeID')" field="userCode" span="24" :item-render="{name: 'input', attrs: {placeholder: $t('common.EmployeeID'),readonly:true}}" />
        <vxe-form-item :title="$t('page.Serviceusermanagement.UserName')" field="userName" span="24" :item-render="{name: 'input', attrs: {placeholder:  $t('common.FullName')}}" />
        <vxe-form-item :title="$t('page.Serviceusermanagement.UserPassword')" field="userPassword" span="24" :item-render="{name: 'input', attrs: {placeholder: $t('common.Password')}}" />
        <!-- <vxe-form-item title="确认密码" field="userPassword2" span="24" :item-render="{name: 'input', attrs: {placeholder: '请再次输入密码'}}" /> -->
        <vxe-form-item :title="　　$t('page.Serviceusermanagement.NewPassword')" field="newPassword" span="24" :item-render="{name: 'input', attrs: {placeholder: $t('common.ConfirmPassword')}}" />
        <vxe-form-item align="right" span="24">
          <vxe-button type="submit" status="danger">{{$t('page.Serviceusermanagement.Save')}}</vxe-button>
          <vxe-button @click="$refs.updateUserInfoModal.close()">{{$t('page.Serviceusermanagement.Cancel')}}</vxe-button>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </div>
</template>

<script>
import request from '@/utils/request'
import { getToken } from '@/utils/auth' 

export default {
  name: 'UserInfoSup',
  data() {
    return {
      uploadUrl: process.env.VUE_APP_URL + '/supply/user/import',
      loading: false,
      systemTypeCode: "1",
      form: {
      },
      systemOptions: this.getBoxValue(),
      tableData: [],
      newUserInfoModal: false,
      newUserInfoForm: {},
      newUserInfoFormRules: {
        userCode: [
          { required: true, message: '请输入工号', trigger: 'blur' },
          { min: 3, max: 32, message: '长度在 3 到 32 个字符', trigger: 'blur' },
          { required: true, pattern: /^[a-zA-Z0-9]+$/, message: '工号只支持大小写英文字母+数字组合', trigger: 'blur' }
        ],
        userName: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 32, message: '长度在 2 到 32 个字符', trigger: 'blur' },
          { required: true, pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/, message: '姓名只支持中文+大小写英文字母+数字组合', trigger: 'blur' }
        ],
        userPassword: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 32, message: '长度在 6 到 32 个字符', trigger: 'blur' },
          { required: true, pattern: /^[a-zA-Z0-9!@#$%^&*]+$/, message: '密码只支持大小写英文字母+数字+!@#$%^&*组合', trigger: 'blur' }
        ],
        userPassword2: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { min: 6, max: 32, message: '长度在 6 到 32 个字符', trigger: 'blur' },
          { required: true, pattern: /^[a-zA-Z0-9!@#$%^&*]+$/, message: '密码只支持大小写英文字母+数字+!@#$%^&*组合', trigger: 'blur' }
        ]
      },

      updateUserInfoModal: false,
      updateUserInfoForm: {},
      updateUserInfoFormRules: {
        userCode: [
          { required: true, message: '用户工号不能为空', trigger: 'blur' },
          { min: 3, max: 32, message: '长度在 3 到 32 个字符', trigger: 'blur' }
        ],
        userName: [
          { required: true, message: '用户姓名不能为空', trigger: 'blur' },
          { min: 1, max: 32, message: '长度在 1 到 32 个字符', trigger: 'blur' }
        ],
        userPassword: [
          { required: true, message: '用户密码不能为空', trigger: 'blur' },
          { min: 3, max: 32, message: '长度在 3 到 32 个字符', trigger: 'blur' }
        ],
        userPassword2: [
          { required: true, message: '确认密码不能为空', trigger: 'blur' },
          { min: 3, max: 32, message: '长度在 3 到 32 个字符', trigger: 'blur' }
        ],
        newPassword: [
          { required: false, message: '密码不能为空', trigger: 'blur' },
          { min: 3, max: 32, message: '长度在 3 到 32 个字符', trigger: 'blur' }
        ]
      },
      headerObj: {
        token: getToken(),
      },
    }
  },
  mounted() {
    this.loadUserInfo()
  },
  methods: {
    getBoxValue() {
      return request({
        url: '/dataSource/list?selectType=WCS&selectType=FFS',
        method: 'get',
        params: { paramName: 'ScadaConnInfo' }
      }).then((res) => {
        console.log(res, '测试')
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    },
    handleOptionChange() {
      this.loadData()
    },
    handlerEditClosed({ row }) {
      this.edited = true
    },
    showNewUserInfoModal() {
      this.newUserInfoModal = true
      this.newUserInfoForm = {
        userCode: '',
        userName: '',
        userPassword: '',
        userPassword2: ''
      }
    },
    showUpdateUserInfoModal(row) {
      this.updateUserInfoModal = true
      this.updateUserInfoForm = {
        id: row.id,
        userCode: row.userCode,
        userName: row.userName,
        userPassword: row.userPassword,
        //userPassword2: row.userPassword
      }
    },
    loadUserInfo() {
      this.loading = true
      return request({
        url: '/supply/user/list',
        method: 'post',
        data:{pageSize:99999,userName: this.form.nameStr, userCode: this.form.codeStr,dbCode: this.systemTypeCode}
      }).then(resp => {
        this.edited = false
        this.loading = false
        this.tableData = resp.data.result.records
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error,
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    updateUserInfo() {
      // if (this.updateUserInfoForm.userPassword !== this.updateUserInfoForm.userPassword2) {
      //   this.$message({
      //     message: '确认密码输入不一致！',
      //     type: 'warning',
      //     duration: 2 * 1000
      //   })
      // } else {
        this.loading = true
        return request({
          url: '/supply/user/edit',
          method: 'post',
          data: {...this.updateUserInfoForm,dbCode: this.systemTypeCode}
        }).then(response => {
          this.loading = false
          this.loadUserInfo()
          this.updateUserInfoModal = false
          this.$message({
            message: this.$t('common.EditSuccess'),
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.loading = false
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      // }
    },
    isExistUserCode() {
      let flag = false
      this.tableData.forEach(element => {
        if (element.userCode === this.newUserInfoForm.userCode) flag = true
      })
      return flag
    },
    addUserInfo() {
      if (this.newUserInfoForm.userPassword !== this.newUserInfoForm.userPassword2) {
        this.$message({
          message: 'The confirmation password input is inconsistent!',
          type: 'warning',
          duration: 2 * 1000
        })
      } else if (this.isExistUserCode()) {
        this.$message({
          message: 'User ID already exists!',
          type: 'warning',
          duration: 2 * 1000
        })
      } else {
        return request({
          url: '/supply/user/add',
          method: 'post',
          data: {...this.newUserInfoForm,dbCode: this.systemTypeCode}
        }).then(response => {
          this.loadUserInfo()
          this.newUserInfoModal = false
          this.$message({
            message: this.$t('common.AddSuccess'),
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      }
    },
    deleteUserInfo(row) {
      this.$confirm('This operation will permanently delete the job number as【' + row.userCode + '】users, do you want to continue ?', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(() => {
        return request({
          url: '/supply/user/delete',
          method: 'post',
          data: {ids:[row.id],dbCode: this.systemTypeCode}
        }).then(response => {
          this.loadUserInfo()
          this.$message({
            message: this.$t('common.DelSuccess'),
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      }).catch(() => {
        this.$message({
          message: this.$t('common.OperationCancellation'),
          type: 'info',
          duration: 2 * 1000
        })
      })
    },
    deleteUserInfos(row) {
      const selectRecords = this.$refs.xTableUserInfo.getCheckboxRecords()
      if (selectRecords.length > 0) {
        this.$confirm('This action will permanently delete the selected【' + selectRecords.length + '】users, do you want to continue??', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(() => {
          const ids =[]
          selectRecords.forEach((item) => {
            if (item.id) {
              ids.push(item.id)
            }
          })
          return request({
            url: '/supply/user/delete',
            method: 'post',
            data: {ids:ids,dbCode: this.systemTypeCode}
          }).then(response => {
            this.loadUserInfo()
            this.$message({
              message: this.$t('common.DelSuccess'),
              type: 'success',
              duration: 2 * 1000
            })
          }).catch(error => {
            this.$message({
              message: error,
              type: 'error',
              duration: 2 * 1000
            })
          })
        }).catch(() => {
          this.$message({
            message: this.$t('common.OperationCancellation'),
            type: 'info',
            duration: 2 * 1000
          })
        })
      } else {
        this.$message({
          message: 'No data selected!',
          type: 'warning',
          duration: 2 * 1000
        })
      }
    },
    importDetailSucc(response, file, fileList) {
      if (response.code == "200") {
        this.$message({
          message: this.$t('common.ImportSuccessful'),
          type: 'success',
          duration: 2 * 1000
        })
      } else {
        this.$message({
          message: response.msg,
          type: 'error',
          duration: 3 * 1000
        })
      }

      this.loadUserInfo()
    },
    exportDataEvent() {
      this.loading = true
      return request({
        url: '/supply/user/export',
        method: 'post',
        data: {pageSize:99999,userName: this.form.nameStr, userCode: this.form.codeStr,dbCode: this.systemTypeCode,dbCode: this.systemTypeCode},
        responseType: 'blob'
      }).then(resp => {
        const blob = new Blob([resp], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        const a = document.createElement('a')
        a.href = URL.createObjectURL(blob)
        //const fileName = decodeURIComponent(resp.headers['content-disposition'].split('filename*=utf-8\'\'')[1]);
        a.download = '供包台用户.xlsx'
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(a.href)
        document.body.removeChild(a)
        this.$message({
          message: this.$t('common.BeginExport'),
          type: 'success',
          duration: 2 * 1000
        })
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || this.$t('common.ExportFailed'),
          type: 'error',
          duration: 2 * 1000
        })
      })
    }


  }
}
</script>
