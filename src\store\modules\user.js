import { login, logout, getInfo,captchaImage,getRouters } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: '',
    userInfo: {},
    img:'',
    uuid:''
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  USER_INFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  Captcha_Img: (state, img) => {
    state.img = img
  },
  Captcha_Uuid: (state, uuid) => {
    state.uuid = uuid
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { account, password,code,uuid, language} = userInfo
    return new Promise((resolve, reject) => {
      login({ username: account.trim(), password: password,code:code,uuid:uuid,language:language }).then(response => {
        const { data } = response
        commit('SET_TOKEN', data.result.token)
        setToken(data.result.token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        const { data } = response
        if (!data) {
          reject('验证失败，请重新登录！')
        }
        const { user, roles } = data
        commit('SET_NAME', user.username)
        commit('SET_AVATAR', user.account)
       
        // const { name, avatar } = data
        // commit('SET_NAME', name)
        // commit('SET_AVATAR', avatar)
        // commit('USER_INFO', data) // 将登录信息存入store中
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  getRouters({ commit, state }) {
    return new Promise((resolve, reject) => {
      getRouters(state.token).then(response => {
        const { data } = response
        if (!data) {
          reject('验证失败，请重新登录！')
        }
        const { result } = data
        commit('USER_INFO', result) // 将登录信息存入store中
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },

  captchaImage({ commit, state }) {
    return new Promise((resolve, reject) => {
      captchaImage().then(response => {
        const { img, uuid } = response
        commit('Captcha_Img', img)
        commit('Captcha_Uuid', uuid)
        resolve(response)
      }).catch(error => {
        reject(error)
      })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        removeToken() // must remove  token  first
        resetRouter()
        commit('RESET_STATE')
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

