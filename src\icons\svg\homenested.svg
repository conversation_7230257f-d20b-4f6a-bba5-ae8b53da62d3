<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="ic_sy_qxj">
<rect id="Rectangle 9" width="60" height="60" rx="4" fill="#FFF5F5"/>
<g id="Image">
<g id="Group 1982663613">
<mask id="mask0_382_18317" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="12" y="12" width="36" height="36">
<path id="&#232;&#183;&#175;&#229;&#190;&#132;" fill-rule="evenodd" clip-rule="evenodd" d="M12.5 12.5H47.5V47.5H12.5V12.5Z" fill="white"/>
</mask>
<g mask="url(#mask0_382_18317)">
<path id="&#232;&#183;&#175;&#229;&#190;&#132;_2" fill-rule="evenodd" clip-rule="evenodd" d="M36.3 24.4C36.3 30.9722 30.9722 36.3 24.4 36.3C17.8278 36.3 12.5 30.9722 12.5 24.4C12.5 17.8278 17.8278 12.5 24.4 12.5C30.9722 12.5 36.3 17.8278 36.3 24.4Z" fill="url(#paint0_linear_382_18317)"/>
<g id="&#232;&#183;&#175;&#229;&#190;&#132;_3" filter="url(#filter0_bi_382_18317)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.875 30C16.875 21.5417 23.6563 14.6875 32.0246 14.6875C40.393 14.6875 47.1742 21.5417 47.1742 30C47.1742 38.4583 40.393 45.3125 32.0246 45.3125C31.9636 45.3125 31.9026 45.3121 31.8418 45.3114L31.8443 45.3125H19.2196L23.7268 42.8142C19.6 40.079 16.875 35.3618 16.875 30Z" fill="#FDB2B2" fill-opacity="0.4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.875 30C16.875 21.5417 23.6563 14.6875 32.0246 14.6875C40.393 14.6875 47.1742 21.5417 47.1742 30C47.1742 38.4583 40.393 45.3125 32.0246 45.3125C31.9636 45.3125 31.9026 45.3121 31.8418 45.3114L31.8443 45.3125H19.2196L23.7268 42.8142C19.6 40.079 16.875 35.3618 16.875 30Z" stroke="url(#paint1_linear_382_18317)" stroke-width="0.2625"/>
</g>
<g id="Vector" filter="url(#filter1_d_382_18317)">
<path d="M33.3595 29.8808V32.6096L37.8328 28.1049L33.3595 23.5667V25.8896C32.4268 25.8896 26.6328 25.9367 26.6328 31.6141C26.6328 34.6702 28.81 37.1681 31.624 37.5672C29.8706 37.1928 28.5522 35.5694 28.5522 33.6253C28.5522 29.9122 32.0052 29.8808 33.3595 29.8808Z" fill="white"/>
</g>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_bi_382_18317" x="12.8047" y="10.6188" width="38.4375" height="38.7625" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="1.96875"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_382_18317"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_382_18317" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.196875"/>
<feGaussianBlur stdDeviation="0.984375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_382_18317"/>
</filter>
<filter id="filter1_d_382_18317" x="25.66" y="22.5938" width="13.1488" height="15.9463" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.486431"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.317647 0 0 0 0 0.317647 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_382_18317"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_382_18317" result="shape"/>
</filter>
<linearGradient id="paint0_linear_382_18317" x1="12.5" y1="12.5" x2="36.3" y2="36.3" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF4A4A"/>
<stop offset="1" stop-color="#FFAAAA"/>
</linearGradient>
<linearGradient id="paint1_linear_382_18317" x1="32.0246" y1="14.6875" x2="32.0246" y2="45.3125" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.61"/>
<stop offset="1" stop-color="#FFF3F3" stop-opacity="0.4"/>
</linearGradient>
</defs>
</svg>
