<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="ic_sy_sdmwppgs">
<rect id="Rectangle 9" width="60" height="60" rx="4" fill="#FFFAF1"/>
<g id="Video">
<g id="Group 1982663611">
<path id="&#232;&#183;&#175;&#229;&#190;&#132;" fill-rule="evenodd" clip-rule="evenodd" d="M12.6406 17.3059C12.6406 15.6944 13.947 14.3881 15.5585 14.3881H26.5004C34.155 14.3881 40.3602 20.5933 40.3602 28.2479V28.2479C40.3602 35.9024 34.155 42.1077 26.5004 42.1077V42.1077C18.8459 42.1077 12.6406 35.9024 12.6406 28.2479V17.3059Z" fill="url(#paint0_linear_382_18350)"/>
<g id="&#232;&#183;&#175;&#229;&#190;&#132;_2" filter="url(#filter0_bi_382_18350)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.6797 31.356C17.6797 22.8957 24.5381 16.0372 32.9984 16.0372V16.0372C41.4587 16.0372 48.3171 22.8957 48.3171 31.356V31.356C48.3171 39.8163 41.4587 46.6747 32.9984 46.6747V46.6747C24.5381 46.6747 17.6797 39.8163 17.6797 31.356V31.356Z" fill="#FDDFA1" fill-opacity="0.4"/>
<path d="M32.9984 46.4997C24.6348 46.4997 17.8547 39.7196 17.8547 31.356C17.8547 22.9923 24.6348 16.2122 32.9984 16.2122C41.3621 16.2122 48.1421 22.9923 48.1421 31.356C48.1421 39.7196 41.3621 46.4997 32.9984 46.4997Z" stroke="url(#paint1_linear_382_18350)" stroke-width="0.35"/>
</g>
<g id="Group 1982663614" filter="url(#filter1_d_382_18350)">
<path id="Vector" d="M32.9743 38.658C28.9517 38.658 25.6797 35.3859 25.6797 31.3634C25.6797 27.3408 28.9517 24.0687 32.9743 24.0687C36.9969 24.0687 40.269 27.3408 40.269 31.3634C40.269 35.3859 36.9969 38.658 32.9743 38.658ZM32.9743 25.0807C29.5083 25.0807 26.6917 27.8974 26.6917 31.3634C26.6917 34.8294 29.5083 37.646 32.9743 37.646C36.4403 37.646 39.257 34.8294 39.257 31.3634C39.257 27.8974 36.4403 25.0807 32.9743 25.0807Z" fill="white" stroke="white" stroke-width="0.291785"/>
<path id="Vector_2" d="M32.9833 33.4295C32.6038 33.4295 32.2918 33.1175 32.2918 32.738V27.4504C32.2918 27.0709 32.6038 26.7589 32.9833 26.7589C33.3628 26.7589 33.6748 27.0709 33.6748 27.4504V32.738C33.6748 33.1175 33.3628 33.4295 32.9833 33.4295ZM32.9833 35.9932C32.5195 35.9932 32.1484 35.6221 32.1484 35.1583C32.1484 34.6945 32.5195 34.3234 32.9833 34.3234C33.4471 34.3234 33.8182 34.6945 33.8182 35.1583C33.8182 35.6221 33.4471 35.9932 32.9833 35.9932Z" fill="white"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_bi_382_18350" x="12.4297" y="10.7872" width="41.1406" height="41.1375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2.625"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_382_18350"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_382_18350" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.2625"/>
<feGaussianBlur stdDeviation="1.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.91 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_382_18350"/>
</filter>
<filter id="filter1_d_382_18350" x="24.0723" y="22.4639" width="17.8007" height="17.7988" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.729463"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.999788 0 0 0 0 0.726825 0 0 0 0 0.193693 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_382_18350"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_382_18350" result="shape"/>
</filter>
<linearGradient id="paint0_linear_382_18350" x1="12.6406" y1="14.3881" x2="40.3602" y2="42.1077" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAB836"/>
<stop offset="1" stop-color="#FEDD93"/>
</linearGradient>
<linearGradient id="paint1_linear_382_18350" x1="32.9984" y1="46.6747" x2="32.9984" y2="16.0372" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDB930" stop-opacity="0.23"/>
<stop offset="0.574544" stop-color="white" stop-opacity="0.53"/>
</linearGradient>
</defs>
</svg>
