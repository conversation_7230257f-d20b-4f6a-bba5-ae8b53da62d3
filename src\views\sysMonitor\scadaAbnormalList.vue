<template>
  <div class="table">
    <el-table
      stripe
      border
      :data="abnormalList"
      style="width: 100%"
      v-loading="loading"
    >
      <el-table-column
        v-for="el in AbnormalAlarmInfo.abnormalListColumn"
        :width="el.width"
        :prop="el.prop"
        :key="el.id"
        :label="el.label"
        :align="el.align"
        :fixed="el.fixed"
      >
        <template slot-scope="scope">
          <div v-if="el.prop === 'SerialNumber'">
            <span>{{ scope.$index + 1 }}</span>
          </div>
          <div v-else-if="el.prop === 'alarmSource'">
            <span
              class="alarmSourceSty"
              :style="{ background: getColor(scope.row) }"
              >{{ getAlarmSource(scope.row) }}</span
            >
          </div>
          <div v-else-if="el.prop === 'message'" class="content">
            <el-popover
              placement="top-start"
              class="custom-popover"
              title=""
              width="400"
              trigger="hover"
              popper-class="popStyle"
              :content="getContent(scope.row, 'message')"
            >
              <el-button class="ContentStyleNone" slot="reference"
                >{{ getContent(scope.row, "message") }}
              </el-button>
            </el-popover>
          </div>

          <div
            v-else-if="el.prop === 'alarmHelpUrl'"
            style="display: flex; justify-content: center;"
          >
            <div
              v-if="scope.row.alarmHelpUrl !== null"
              class="card-panel-icon-wrapper"
              style="display: flex;justify-content: center;"
            >
              <img
                src="../../assets/img/abnormal/url.png"
                style="width: 25px; height: 25px;"
                @click="openUrl(scope.row.alarmHelpUrl)"
              />
            </div>
          </div>


          <div  v-else-if="el.prop === 'processState'">
            <div>
              {{  getProcessState(scope.row)  }}
            
            </div>
          </div>

          <div v-else-if="el.prop === 'Operation'">
            <el-select
              v-if="scope.row.id === setOptionsIndex"
              v-model="searchQuery[scope.$index].remark"
              @change="changeOptionVal"
              filterable
              :placeholder="$t('common.PleaseSelect')"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <div
              v-else
              style="display: flex;justify-content: center; align-items: center;color:rgb(102, 177, 255);"
              @click="getAllListOptions(scope.row)"
            >
              <svg-icon
                icon-class="edit"
                class-name="card-panel-icon-edit"
              ></svg-icon>
            </div>
          </div>
          <div v-else>
            <span>{{ scope.row[el.prop] }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import request from "@/utils/request";
export default {
  name: "SACADAABNORMALLIST",
  props: ["abnormalList", "AbnormalAlarmInfo"],
  data() {
    return {
      loading: false,
      setOptionsIndex: "-1",
      searchQuery: [],
      options: []
    };
  },

  mounted() {},
  methods: {
    getAllListOptions(row) {
      this.setOptionsIndex = row.id;
      // console.log("this.setOptionsIndex", this.setOptionsIndex);
      request({
        url: "/alarm/url/list/all",
        method: "get"
      }).then(response => {
        const optionsList = response.data.result;
        this.options = optionsList.map(el => ({
          label: el.remark,
          url: el.url,
          value: el.id,
          curPage: el.curPage,
          pageSize: el.pageSize
        }));
      });
    },

    changeOptionVal(id) {
      // console.log("id", id);
      request({
        url: "/log/alarm/edit",
        method: "post",
        data: {
          alarmHelpUrl: id,
          id: this.setOptionsIndex
        }
      }).then(async response => {
        if (response.msg === "操作成功") {
          // console.log("this.searchQuery", this.searchQuery);
          // console.log("abnormalList", this.abnormalList);
          this.searchQuery.forEach(item => {
            if (item.id === this.setOptionsIndex) {
              // console.log("this.options[id - 1]", this.options[id - 1]);
              item.alarmHelpUrl = this.options[id - 1].url;
            }
          });
          this.$emit("postSuccess", true);
          this.$emit("postSuccess", true);
        }
        // console.log("this.setOptionsIndex", this.setOptionsIndex);
      });
    },
    openUrl(url) {
      window.open(url, "_blank");
    },
    getContent(row, messageKey) {
      return row[messageKey];
    },
    callParentMethod() {
      this.$emit("getParentFn");
    },
    abnormalAlarm() {
      this.$emit("abnormalAlarm");
    },

    getColor(row) {
      const colorMap = {
        WCS: "rgb(241, 169, 18)",
        SUP: "rgb(16, 81, 249)",
        default: "#606266"
      };
      return colorMap[row.alarmSource] || colorMap.default;
    },
    getAlarmSource(row) {
      const SourceMap = {
        SUP: "供包台",
        WCS: "WCS",
        DEVICE_ALARM:'DEVICE',
        default: "WEB"
      };
      return SourceMap[row.alarmSource] || SourceMap.default;
    },
    getProcessState(row){
      console.log(row,'数据')
      if(row.processState==1){
        return '已完成'
      }
       if(row.processState==0){
        return '未完成'
      }
      
     
    }
  },
  beforeDestroy() {
    this.callParentMethod();
  },

  watch: {
    abnormalList(oldVal, newVal) {
      if (newVal.length > 0) {
        this.searchQuery = newVal.map(el => ({
          remark: el.alarmHelpUrlName || "",
          id: el.id,
          url: el.alarmHelpUrl
        }));
      } else {
        this.searchQuery = oldVal.map(el => ({
          id: el.id,
          remark: el.alarmHelpUrlName || "",
          url: el.alarmHelpUrl
        }));
      }
      this.loading = false;
    },
    deep: true,
    immediate: true
  }
};
</script>
<style lang="scss">
.popStyle >>> .el-popover__arrow {
  border-color: transparent transparent #000000 transparent; /* 替换为你想要的颜色 */
}
.custom-popover::before {
  border-color: transparent transparent #000000 transparent; /* 替换为你想要的颜色 */
}

.custom-popover::after {
  border-color: transparent transparent #000000 transparent; /* 替换为你想要的颜色 */
}

.popStyle::before {
  border-color: transparent transparent #000000 transparent; /* 替换为你想要的颜色 */
}

.popStyle::after {
  border-color: transparent transparent #000000 transparent; /* 替换为你想要的颜色 */
}
.popStyle {
  background: rgba(0, 0, 0, 0.8) !important;
  color: #f0f0f0;
  padding: 8px;
}
</style>

<style lang="scss" scoped>
::v-deep thead tr th {
  background: rgba(0, 0, 0, 0.2) !important;
  color: #333;
  font-size: 14px;
}

::v-deep .el-table__body-wrapper {
  font-weight: 600;
  font-size: 12px;
}
.alarmSourceSty {
  display: inline-block;
  padding: 1px 6px;
  background: rgb(241, 169, 18);
  width: 64px;
  color: #fff;
  border-radius: 4px;
}
::v-deep .ContentStyleNone {
  border: none !important;
  background: rgba(0, 0, 0, 0);
  padding: 7px 0px;
  width: 390px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content {
  white-space: nowrap; /* 阻止文本换行 */
  /* 或者使用 overflow: auto; 来添加滚动条，但这通常用于高度受限的容器 */
  text-overflow: ellipsis; /* 可选：在文本末尾显示省略号，与overflow: hidden;一起使用效果最佳 */

  ::v-deep .el-popover {
    background-color: #f0f0f0 !important; /* 使用 !important 以确保覆盖默认样式 */
  }
}

::v-deep .el-table__row.hover-row {
  background-color: #f0f0f0 !important; /* 使用 !important 以确保覆盖默认样式 */
}
::v-deep .hover-row {
  background-color: #f0f0f0 !important; /* 使用 !important 以确保覆盖默认样式 */
}

.card-panel-icon-wrapper {
  float: none !important;
  width: 100%;
  height: 100%;
  margin: 0 !important;

  .svg-icon {
    display: block;
    float: none !important;
  }
}
.centered {
  display: flex;
  justify-content: center;
}
.card-panel-icon {
  float: left;
  font-size: 24px;
  width: 24px;
  height: 24px;
}
.card-panel-icon-edit {
  float: left;
  font-size: 18px;
}
/* 使用深度选择器来覆盖子组件的样式 */
</style>
