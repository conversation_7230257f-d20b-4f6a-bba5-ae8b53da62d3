// 葡萄牙语
const pt = {
  login: {
    changelanguage: "cambiar de idioma",
    index: "I'm six",
    title: "Sistema de consulta de clasificación COGY",
    Success: "Login bem-sucedido",
    plsuaername: "Por favor insira o nome de usuário",
    plspassword: "Por favor insira a senha",
    plscode: "Por favor insira a senha do código de verificação",
    login: "Login",
    code: "Código de verificação",
       chinese: "中文",
    english: "english",
    thai: "ภาษาไทย",
    ms: "Bahasa Melayu",
    vi: "Tiếng Việt",
    es: "Español",
    pt: "Bahasa Portugis",
    id: "Bahasa Indonesia",
    ph: "Filipino",
    SwipeRight: "deslize para a direita",
  },
  welcome: {
    title: "Bem-vindo ao sistema de consulta de log de triagem",
  },
  headers: {
    title: "Sistema de consulta de dados de triagem",
    loginout: "Saia do sistema",
  },
  sidebar: {
    Home: "Tela Inicial",
    Syscfg: "Configuração do sistema",
    Userinfosup: "Gerenciamento de usuários de pacotes",
    User: "Gerenciamento de usuários",
    Role: "gerenciamento de funções",
    Menu: "Gerenciamento de menu",
    Sysparam: "Parâmetros do sistema",
    Errchute: "Compartimento de anormal( rejeito)",
    Sysurl: "Número de dias para manter os registros",
    Sortingplan: "Plano de Triagem",
    Datamgr: "Gerenciamento de dados",
    Runninglog: "Registro de execução",
    Locklog: "registro de bloqueio",
    Sortinglog: "Registro de triagem",
    Packagenumberlog: "Número de Pacotes Históricos",
    Sortingrepush: "Dados suplementares",
    Sortingreport: "Estatísticas de relatório",
    Chutereport: "Quantidade triada por compartimento",
    Sortingcountsup: "Estatísticas de quantidade (Cabine)",
    Sortingcountuser: "Estatísticas de quantidade (ID de funcionário)",
    Packagenumbercount: "Estatísticas gerais de lotes",
    Sortingefficiency: "Estatísticas de eficiência",
    Errorreport: "Estatísticas por Categoria",
    Monitor: "Monitoramento do sistema",
    Scada: "Monitoramento SCADA",
    Job: "tarefas agendadas",
    Config: "Configurações de parâmetros",
    Dict: "Gerenciamento de dicionário",
  },
  page: {
    homepage: {
      boardingcount: "Número de carrinhos",
      scancount: "Contagem de Escaneamentos",
      cellcount: "Quantidade de itens no compartimento de triagem",
      cancelleditems: "Encomenda cancelada",
      rabbitinterceptions: "Número total de interceptações de J&T",
      unmatchedcodes: "O código de três segmentos não corresponde ao padrão",
      overbounds: "Número total de super voltas",
      anomalies: "Número total de exceções",
      sortingvolume: "Quantidade triada",
      monday: "2ª-feira",
      tuesday: "Terça-feira",
      wednesday: "Quarta-feira",
      thursday: "Quinta-feira",
      friday: "Sexta-feira",
      saturday: "Sábado",
      sunday: "Domingo",
    },
    Serviceusermanagement: {
      Refresh: "atualizar",
      AddUser: "Adicionar novo usuário",
      BulkDelete: "Exclusão em lote",
      FullName: "Nome",
      EmployeeID: "ID de empregado",
      Query: "Consulta",
      Import: "importar",
      OnlyExcel: "(Apenas arquivos no formato excel.xlsx podem ser importados)",
      SerialNumber: "número de série",
      UserEmployeeID: "ID do usuário",
      UserName: "Nome de usuário",
      UserID: "ID do usuário",
      Creator: "Criador",
      CreationTime: "hora de criação",
      Operation: "Operação",
      UserPassword: "Senha do usuário",
      ConfirmPassword: "Confirme a senha",
      NewPassword: "Nova Senha",
      Save: "salvar",
      Cancel: "Cancelar",
      UpdateUser: "Modificar usuario",
    },
    usermanagement: {
      UserID: "Número de usuário",
      UserName: "Nome de usuário",
      UserAccount: "Conta de usuário",
      PlaseUserAccount: "Por favor insira a conta do usuário",
      PhoneNumber: "número de telefone",
      PleasePhoneNumber: "Por favor, o número do telefone",
      Status: "Status",
      PleaseStatus: "Por favor insira o status",
      StartDate: "data de início",
      EndDate: "data de término",
      CreationTime: "hora de criação",
      Operation: "Operação",
      UserGender: "Gênero do usuário",
      UserNickname: "Apelido do usuário",
      Email: "Email",
      UserPassword: "Senha do usuário",
      Active: "normal",
      Inactive: "desativar",
      Role: "Função",
      Note: "Nota",
      Search: "Pesquisar",
      Reset: "Resetar",
      Add: "Adicionar",
      Delete: "excluir",
      Update: "Rever",
      More: "Mais",
      ResetPassword: "redefinir senha",
    },
  },
  common: {
    DerivedData: "Exportar dados",
    ViewDWSPeakEffect: "Veja a eficiência máxima do DWS",
    peakEffect: "eficiência máxima",
    SearchData: "Dados de pesquisa",
    NoData: "Sem dados disponíveis",
    Arrival: "Chegada",
    Departure: "Expedidas",
    ArrivalAndDeparture: "Chegada e expedida",
    CloseOther: "Encerrar outro",
    CloseAll: "Encerrar tudo",
    RoleName: "Nome da função",
    PlaseRoleName: "Insira um nome de função",
    PermissionCharacters: "caracteres de permissão",
    PlasePermissionCharacters: "Por favor insira caracteres de permissão",
    Order: "ordem",
    Status: "Status",
    Search: "Pesquisar",
    Reset: "Resetar",
    Delete: "excluir",
    Update: "Rever",
    More: "Mais",
    CreationTime: "hora de criação",
    Operation: "Operação",
    Update: "Rever",
    Add: "Adicionar",
    UpdateCache: "Atualizar cache",
    Select: "Selecionar",
    Import: "importar",
    Export: "Exportar",
    SerialNumber: "número de série",
    RoleNumber: "número da função",
    Memo: "Observação",
    UpdateDate: "hora da modificação",
    ParameterName: "Nome do parâmetro",
    ParameterValue: "Valor do parâmetro",
    PleaseSelectARole: "Selecione uma função",
    PleaseInputnickname: "Por favor insira o apelido do usuário",
    PleaseInputPhoneNumber: "Por favor insira o número do telefone",
    PleaseInputEmail: "Por favor insira o e-mail",
    PleaseInputUserName: "Por favor insira o nome de usuário",
    PleaseInputPassWord: "Por favor insira a senha",
    RoleStatus: "status do função",
    RoleCode: "Número de usuário",
    PlaseInputRoleCode: "Por favor insira o número do usuário",
    CreatorID: "ID do criador",
    Determine: "Determinar",
    Cancellation: "Cancelamento",
    Task: "Nome da tarefa",
    PlaseTask: "Insira um nome de tarefa",
    TaskCode: "Código da tarefa",
    PlaseTaskCode: "Por favor insira o código da tarefa",
    MenuName: "Nome do menu",
    PlaseMenuName: "Por favor insira um nome de menu",
    MenuStatus: "Status oe menu",
    ExpandAndCollapse: "expandir/recolher",
    SelectAllDonteSelectAll: "Selecionar tudo/Desmarcar tudo",
    ParentChildLinkage: "Vínculo pai e filho",
    MenuPermissions: "Permissões do menu",
    PleaseEnterContent: "Por favor insira o conteúdo",
    Icon: "ícone",
    ComponentPath: "Trajeto do Componente",
    SchemeID: "ID do esquema",
    ModeType: "Tipo de triagem",
    PlanName: "Nome do plano",
    PlanCode: "Código do plano",
    PlanDesc: "Descrição do plano",
    PlanFlag: "Tipo de plano",
    IsSelected: "Foi Selecionado",
    UpdateDate: "hora da modificação",
    Detail: "Detalhes",
    Enable: "habilitar",
    Date: "tempo",
    Grade: "nota",
    Source: "fonte",
    Message: "Informação",
    ExtraData: "dados extras",
    plcType: "Tipo de PLC",
    Number: "número de série",
    Component: "Componente",
    Chute: "chute( compartimento)",
    Supply: "Cabine",
    dwsNo: "Numeração DWS",
    BigBarRate: "Proporção de pacotes grandes",
    Quantity: "quantidade",
    SmallBarRate: "Proporção de pacote pequeno",
    ExceptionCode: "Código de exceção",
    Code: "Código de verificação",
    ScanTime: "Tempo de Escaneamento",
    BoardingTime: "Horário de embarque (Indução)",
    DropTime: "Tempo de caída",
    NextPieceTime: "建包时间",
    passBackTime: "Hora de retorno",
    arrivalTime: "Hora de chegada",
    PacketNumber: "Número do pacote",
    TimeType: "tipo de tempo",
    turns: "número de voltas",
    Image: "Imagem",
    UpperLevel: "Nível Superior",
    LowerLevel: "Nível Inferior",
    LayerNumber: "Número de Nível",
    PleaseEnterTheDictionaryLabel: "Por favor, insira o etiqueta do dicionário",
    DictionaryName: "Nome do Dicionário",
    DictionaryId: "ID do Dicionário",
    DictionaryType: "Tipo do Dicionário",
    PleaseEnterTheDictionaryType: "Por favor, insira o tipo do dicionário",
    PleaseEnterTheDictionaryName: "Por favor, insira o nome do dicionário",
    BagBindingBfficer: "Responsável pela vinculação do pacote",
    DictionaryEncoding: "Codificação do Dicionário",
    DictionaryTag: "Etiqueta do Dicionário",
    DictionaryValue: "Valor do Dicionário",
    DictionarySort: "Ordenação do Dicionário",
    DictionaryRemark: "Observação",
    DictionaryCreateTime: "Hora da Criação",
    DataTag: "Etiqueta de Dados",
    PleaseEnterTheDataLabel: "Por favor, insira a etiqueta de dados",
    DataKey: "Chave de Dados",
    StyleAttribute: "Atributo de Estilo",
    DisplayOrder: "Ordem de Exibição",
    EchoStyle: "Estilo de Eco",
    ListClass: "Classe de Lista",
    PleaseEnterTheDataKey: "Por favor, insira a chave de dados",
    PleaseEnterTheStyleAttribute: "Por favor, insira o atributo de estilo",
    PleaseEnterTheDisplayOrder: "Por favor, insira a ordem de exibição",
    PleaseEnterTheEchoStyle: "Por favor, insira o estilo de eco",
    PleaseEnterTheListClass: "Por favor, insira a classe de lista",
    PleaseEnterTheRemark: "Por favor, insira uma observação",
    PleaseEnterTheContent: "Por favor, insira o conteúdo",
    TheAddressNeedsToBehttp:
      "Se for um link externo, o endereço deve começar com `http(s)://`",
    TheAddressNeedsToBehttpUser:
      "Endereço de acesso, por exemplo: `user`. Se for um endereço externo que precisa ser acessado internamente, deve começar com `http(s)://`",
    TheAddressNeedsToBehttpCatalogue:
      "Caminho do componente de acesso, por exemplo: `system/user/index`. Por padrão, está no diretório `views`",
    TheDefaultPassingParametersForTheRoute:
      "Parâmetros padrão de transferência de acesso, por exemplo: `{'id': 1, 'name': 'ry'}`",
    TheComponentWillBeCachedByKeepAlive:
      "Se selecionado, será armazenado em cache por `keep-alive`. Precisa que o `name` do componente e o endereço sejam consistentes",
    SelectHiddenThenTheRoute:
      "Se selecionado oculto, a rota não aparecerá na barra lateral, mas ainda pode ser acessada",
    SelectDisableThenTheRouteSidebar:
      "Se selecionado desativado, a rota não aparecerá na barra lateral e também não poderá ser acessada",
    PleaseEnterTheRouteParameters: "Por favor, insira os parâmetros de rota",
    Yes: "Sim",
    No: "Não",
    PermissionCharactersString: "String de permissão definida no controller, por exemplo: @PreAuthorize(`@ss.hasRole('admin')`)",
    Cache1: "Cache",
    NoCache: "Sem Cache",
    AddUser: "Adicionar Usuário",
    BatchCancelAuthorization: "Cancelar Autorização em Lote",
    Close: "Fechar",
    CancelAuthorization: "Cancelar Autorização",
    View: "Ver",
    UserType: "Tipo de Usuário",
    PleaseSelectUserType: "Por favor, selecione o tipo de usuário",
    Forward: "Normal",
    Reverse: "Invertido",
    Lock: "Bloquear",
    Unlock: "Desbloquear",
    SendMessage: "Enviar Mensagem",
    TaskGroup: "Nome do Grupo de Tarefas",
    PleaseSelectTaskGroup: "Por favor, selecione o nome do grupo de tarefas",
    TaskStatus: "Status da Tarefa",
    PleaseSelectTaskStatus: "Por favor, selecione o status da tarefa",
    Log: "Log",
    InvokeTarget: "String de Destino de Chamada",
    CronExpression: "Expressão de Execução Cron",
    ExecuteOnce: "Executar Uma Vez",
    TaskDetails: "Detalhes da Tarefa",
    DispatchLog: "Log de Despacho",
    InvokeTarget: "Método de Chamada",
    BeanCallExample: "Exemplo de Chamada de Bean: ryTask.ryParams('ry')",
    ClassCallExample:
      "Exemplo de Chamada de Classe: com.ruoyi.quartz.task.RyTask.ryParams('ry')",
    ParameterDescription:
      "Descrição de Parâmetro: Suporta string, booleano, long, float, inteiro",
    PleaseInputInvokeTarget: "Por favor, insira a string de destino de chamada",
    PleaseInputCronExpression: "Por favor, insira a expressão de execução cron",
    GenerateExpression: "Gerar Expressão",
    ExecuteStrategy: "Estratégia de Execução",
    MisfirePolicy: "Política de Falha de Execução",
    ImmediateExecution: "Execução Imediata",
    DelayExecution: "Execução com Delay",
    AbandonExecution: "Abandonar Execução",
    PleaseSelectExecuteStrategy:
      "Por favor, selecione a estratégia de execução",
    Concurrent: "Concorrência",
    Allow: "Permitir",
    Prohibit: "Proibir",
    PleaseSelectConcurrent: "Por favor, selecione se é concorrente",
    CronExpressionGenerator: "Gerador de Expressão Cron",
    NextExecutionTime: "Próxima Hora de Execução",
    TaskDetails: "Detalhes da Tarefa",
    TaskGroup1: "Grupo de Tarefas",
    DefaultStrategy: "Estratégia Padrão",
    ExecuteStatus: "Status de Execução",
    PleaseSelectExecuteStatus: "Por favor, selecione o status de execução",
    ExecutionTime: "Hora de Execução",
    PleaseSelectExecutionTime: "Por favor, selecione a hora de execução",
    Clear: "Limpar",
    JobLogId: "ID do Log de Trabalho",
    JobMessage: "Mensagem do Log",
    Detail1: "Detalhes",
    DispatchLogDetails: "Detalhes do Log de Despacho",
    PleaseSelect: "Por favor, selecione",
    SelectStartTime: "Selecionar Hora de Início",
    SelectEndTime: "Selecionar Hora de Término",
    DebugStart: "Depuração (Iniciar)",
    DebugClose: "Depuração (Fechar)",
    ClearPacketNumber: "Limpar Número do Pacote",
    Upload: "Carregar",
    TotalQuery: "Total de Consultas",
    property: "imagem(ns)",
    all: "Todos",
    UnloadingToDeliveryScanning: "Escaneamento de Descarga e Entrega",
    BuildingPackageScanning: "Escaneamento de Empacotamento",


    SelectUser: "Selecione o usuário",
    InvokeTargetMethod: "Método de chamada de destino",
    PleaseSelectTaskGroup: "Por favor, selecione o grupo de tarefas",
    CronExpression1: "expressão cron",
    ExceptionInfo: "Informações de exceção",

    Edit: "Editar",
    ScopeOfAuthority: "Escopo de Autorização",
    DataPermission: "Permissão de Dados",
    Confirm: "Confirmar",
    StartDate: "Data de Início",
    EndDate: "Data de Término",
    Weight: "peso",
    length: "longo",
    width: "Largura",
    heigth: "alto",
    CarNumber: "número do carrinho",
    RequestedGate: "Compartimento solicitado",
    PhysicalGrid: "Compartimento físico retornado pelo PLC",
    taskNo: "Número da tarefa",
    TerminalDispatchCode: "código de três segmentos",
    FallingGridTime: "Tempo necessário para entrar no saco",
    PackageGrade: "Número do Lote",
    ChipNumber: "Número do chip",
    BindingTime: "Tempo de vinculação",
    BindingPersonnel: "Operador de vinculação",
    ScanType: "Tipo de Escaneamento",
    NextStationNumber: "Número da próxima parada",
    NextStopNumber: "Número da próxima parada",
    Rfid: "Etiqueta eletrônica de lote",
    TimeInterval: "intervalo de tempo",
    SortingQuantity: "Quantidade de Triagem",
    TotalSortingWeight: "Peso total de triagem (KG)",
    NumberOfScannedBarcodeRecords:
      "Digitalize a quantidade de registros de código de barras",
    RecordTheNumberOfPackagesLoadedOntoTheVehicle:
      "Número de registros de pacotes carregados no carrinho",
    NumberOfDropFeedbackRecords: "Número de registros de feedback imediato",
    scanQuantity: "Quantidade de Escaneamento",
    arrivalQuantity: "Quantidade de chegada",
    passBackQuantity: "Quantidade de retorno",
    TotalNumberOfPackages: "Quantidade total do pacote",
    Packagenumb: "Quantidade de lote (número de peças dentro deste período)",
    Type: "tipo",
    Count: "quantidade",
    SystemType: "Tipo de sistema",
    EmployeeID: "Insire ID de Trabalho",
    FullName: "Insire o nome",
    Password: "Insire a senha",
    ConfirmPassword: "Insire a senha novamente",
    SelectGender: "Por favor selecione o sexo",
    Active: "normal",
    Inactive: "desativar",
    Male: "Masculino",
    Female: "feminio",
    Unknown: "desconhecido",
    RoleOrder: "Ordem de função",
    Show: "mostrar",
    Hide: "esconder",
    Default: "padrão",
    System: "sistema",
    Success: "sucesso",
    Failure: "Falha",
    AddMenu: "Adicionar menu",
    EditMenu: "Modificar menu",
    ParentMenu: "Menu anterior",
    MenuType: "Tipo de menu",
    Directory: "Índice",
    Menu: "menu",
    Button: "botão",
    MenuIcon: "ícone do menu",
    SelectIcon: "Selecione o ícone",
    RouteAddress: "endereço de roteamento",
    DisplayOrder: "Mostrar a ordem",
    ExternalLink: "Link externo",
    DisplayStatus: "Mostrar status",
    MenuStatus: "Status oe menu",
    RouteParameters: "parâmetros de roteamento",
    Cache: " Haverá cache? ",
    ComponentPath: "Trajeto do Componente",
    AddRole: "Adicionar função",
    EditRole: "Modificar função",
    AddPlan: "Adicionar novo plano",
    Cancel: "Cancelar",
    FirstSegmentCode: " Código de 1º segmento",
    SecondSegmentCode: "Código de 2º segmento",
    ThirdSegmentCode: "Código de 3º segmento",
    NextStopCode: "Código da próxima parada",
    NextStopName: "Nome da próxima parada",
    ModificationTime: "hora da modificação",
    BulkDelete: "Exclusão em lote",
    AddDetails: "Adicionar detalhes",
    ByServicePackage: "De acordo com cabine",
    ByDwsNo: "De acordo com o número de série do DWS",
    Granularity: "granularidade",
    LogicCode: "código lógico",
    PacketType: "tipo de pacote",
    IsUpload: "FoiEnviado",
    AddSuccess: "Adicionado com sucesso",
    EditSuccess: "Modificação bem-sucedida",
    DelSuccess: "Excluir com sucesso",
    ImportSuccessful: "Importação bem-sucedida",
    BeginExport: "Iniciar exportação",
    ModificationFailed: "Falha na modificação",
    AddFailed: "Falha ao adicionar",
    OperationSuccessful: "Operação bem-sucedida",
    OperationFailed: "Falha na operação",
    OperationCancellation: "Operação cancelada",
    ExportFailed: "Falha na exportação",
    LoginOut:
      "Você foi desconectado. Você pode cancelar e permanecer nesta página ou fazer login novamente.",
    ConfirmLogout: "Confirmar saída",
    LogAgain: "Faça login novamente",
    Remark: "Observação",
    DwsNo: "Número de série DWS",
    Notempty:
      "A senha da conta ou o código de verificação não podem ficar vazios!",
    Notpassword: "A senha da conta não pode ficar vazia!",
    Id: "Chave primária do parâmetro",
    Parameter: "Parâmetro",
    PlParameter: "Por favor insira o nome do parâmetro",
    ParameterKey: "Nome da chave do parâmetro",
    PlParameterKey: "Insira o nome da chave do parâmetro",
    ParameterValue: "Valor do parâmetro",
    PlParameterValue: "Insira o valor da chave do parâmetro",
    Group: "Nome do grupo",
    PlGroup: "Selecione um nome de grupo",
  },
  scada: {
    DeviceRunningStatus: "Status operacional do equipamento",
    DeviceStopped: "Equipamento parado",
    DeviceRunning: "Equipamento em funcionando",
    StartTime: "Start Time",
    RunningSpeed: "Velocidade de Execução",
    CartOccupancyRate: "Taxa de ocupação de carro",
    TotalDistanceTraveledByDevice:
      "Total de quilômetros operacionais do equipamento",
    DistanceTraveledInCurrentRun: "Número de quilômetros percorridos desta vez",
    TotalDistanceTraveled: "Total de quilômetros percorridos",
    Scans: "Número de Escaneamento",
    PendingStart: "Esperando para começar ",
    FullScreen: "Exibição em tela cheia",
    ExitFull: "Sair da tela inteira",
    UpperLevelRunningSpeed: "Velocidade de corrida da camada superior",
    UpperLevelDeviceRunningStatus: "Status operacional do dispositivo superior",
    UpperLevelStartTime: "Tempo de execução da camada superior",
    UpperLevelCartOccupancyRate: "Taxa de ocupação de carros superior",
    UpperLevelByDevice:
      "Total de quilômetros operacionais de equipamentos de nível superior",
    LowerLevelDeviceRunningStatus: "Status operacional do equipamento inferior",
    UpperLayerPLCDisconnect:
      "O PLC superior está desconectado de forma anormal",
    LowerLevelPLCDisconnect:
      "O PLC inferior está desconectado de forma anormal",
    UpperLayerPLCConnectionStatus: "Status da conexão PLC da camada superior",
    LowerLevelPLCConnectionStatus: "Status de conexão PLC inferior",
    LowerLevelStartTime: "Tempo de execução de início da camada inferior",
    LowerLevelRunningSpeed: "Velocidade de execução da camada inferior",
    LowerLevelCartOccupancyRate: "Taxa de ocupação de carrinhos inferiores",
    LowerLevelTotalDistanceTraveledByDevice:
      "Total de quilômetros operacionais de equipamentos de nível inferior",
    UpperLevelScans: "Número de escaneamento da camada superior",
    LowerLevelScans: "Número de bipagem da camada inferior",
    AbnormalQuantity: "Quantidade anormal",
    NumberOfSlotsOccupied: "Número de Slots Ocupados",
    FailedRepushQuantity: "Número de pushbacks com falha",
    InterceptedQuantity: "Pacote Interceptado",
    ExcessiveCirclesQuantity: "número de super voltas",
    UnconfiguredThreeSegmentCodeSlots:
      "O compartimento do código de três segmentos não está configurada",
    ComprehensiveExceptionSlots: "compartimento anormal abrangente",
    CancelledItems: "Encomenda cancelada",
    UnobtainedThreeSegmentCodeInformation:
      "Informações de código de três segmentos não obtidas",
    WebSocketStatus: "Status do WebSocket",
    WCSCommunicationStatus: "Status de comunicação WCS",
    Connected: "Conectado",
    NotConnected: "Não conectado",
    SortingStatus: "Status de Triagem",
    Idle: "inativo",
    Loaded: "Carregada",
    CartStatus: "Status do carrinho",
    LittleThingsAreQuick:'Peças pequenas rápido',
    Headscratcher: 'Chapéu de esaú',
    OnLine:'online',
    Offline:'desligado',
    Locked: "bloqueio",
    FullPackage: "Saco completo",
    SlotStatus: "Status do compartimento",
    InterceptedItem: "Interceptor",
    ExceptionSlot: "Porta anormal",
    PendingCommunication: "Aguardando comunicação",
    Max: "ciclo máximo",
    Cancellation: "Cancelamento",
    UnProgramme: "Sem plano",
    UnThree: "Não equipado com código de três segmentos",
    CartOperation: "Operação do carrinho",
    OneKeyUnlock: "Destravar com Uma Chave",
    OneKeyLockSlot: "Trava com Uma Chave",
    CartNumber: "número do carrinho",
    FloorNumber: "Número de camadas",
    UpperLevel: "camada superior",
    LowerLevel: "nível inferior",
    Lock: "Bloqueiar",
    Unlock: "Desbloquear",
    ManualSwitchStart: "Comutação manual (ativação)",
    ManualSwitchClose: "Interruptor manual (desligado)",
    Run: "funcionar",
    Close: "encerramento",
    DisabledList: "Lista de carrinhos desativados",
    AbnormalAlarm: "Alarme anormal",
    DisableCart: "desabilitar carrinho",
    UpperLevelCart: "Carrinho superior",
    LowerLevelCart: "Carrinho inferior",
    VerificationPassword: "Por favor insira a senha de verificação",
    Confirm: "confirmar",
    ClearTheKilometers: "Limpar quilômetros",
    PleaseSelectClearKilometers: "Selecione quilômetros livres",
    FaultLevel: "Nível de falha",
    StartTime: "hora de início",
    EndTime: "hora de término",
    CriticalAlarm: "Alerta Crítico",
    GeneralAlarm: "Alarme geral",
    MinorAlarm: "Alarme menor",
    SearchCriteria: "Critérios de pesquisa",
    Time: "tempo",
    Select: "Selecionar",
    PleaseEnterContent: "Por favor insira o conteúdo",
    SerialNumber: "número de série",
    Operation: "Operação",
    AlarmHelpLink: "Documentação de ajuda",
    AlarmType: "Tipo de alarme",
    AlarmSource: "Fonte de alarme",
    Content: "contente",
  },
};
export default pt;
