<template>
  <div style="width: 100%; display: flex; justify-content: center;">
    <div
      ref="chart"
      style="width: 100%; height: 450px; display: flex; justify-content: center; align-items: center;"
    ></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'LineChartComponent',
  props: {
    lineXAxis: {
      type: Array,
      default: () => [],
    },
    lineTitle: {
      type: String,
      default: '线图标题',
    },
    lineData: {
      type: Array,
      default: () => [],
    },
    legendDate: {
      type: Array,
      default: () => [],
    },
    titlePosition: {
      type: String,
      default: 'left',
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart);

      const options = {
        color: ['rgba(64, 158, 255, 1)', 'rgba(254,47,79,1)', 'rgba(220,198,196,1)'],// 设置柱子的颜色
        title: {
          text: this.lineTitle,
          left: this.titlePosition,
          textStyle: {
            color: 'rgba(254,47,79,1)'  // 设置标题颜色为红色
          }
        },
        tooltip: {
          trigger: 'item',
        },
        legend: {
          left: 'left',
          data: this.legendDate,
        },
        grid: {
          // top: '2%',
          left: '3%',
          right: '4%',
          // bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: this.lineXAxis,
          axisLabel: {
            rotate: 45,
            textStyle: {
              fontSize: 15,
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            interval: 0,
            textStyle: {
              fontSize: 15,
            },
          },
        },
        series: [
          {
            data: this.lineData,
            type: 'bar',
            barWidth: 30,
            
            label: {
              show: true,
              position: 'top',
              textStyle: {
                fontSize: 16,
              },
            },
          },
        ],
      };

      this.chart.setOption(options);
    },
  },
  watch:{
    lineXAxis:{
      immediate: true,
      handler(newVal) {
        if (newVal) {
          console.log("line---------------------", this.lineXAxis)
        }
      }
    },
    lineData:{
      handler(newVal) {
        if (newVal) {
          console.log("lineData---------------------", this.lineData)
        }
      }
    }
  }
};
</script>

<style scoped>
</style>
