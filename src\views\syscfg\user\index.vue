<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--用户数据-->
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" >
          <el-form-item :label="$t('page.usermanagement.UserName')" prop="account">
            <el-input v-model="queryParams.account" :placeholder="$t('login.plsuaername')" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item :label="$t('page.usermanagement.PhoneNumber')" prop="phoneNum">
            <el-input v-model="queryParams.phoneNum" :placeholder="$t('page.usermanagement.PleasePhoneNumber')" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item :label="$t('page.usermanagement.Status')" prop="status">
            <el-select v-model="queryParams.status" :placeholder="$t('page.usermanagement.PleaseStatus')" clearable style="width: 240px">
              <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('page.usermanagement.CreationTime')">
            <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
              range-separator="-" :start-placeholder="$t('page.usermanagement.StartDate')" :end-placeholder="$t('page.usermanagement.EndDate')"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('page.usermanagement.Search') }}</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('page.usermanagement.Reset') }}</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
              >{{ $t('page.usermanagement.Add') }}</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              >修改</el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              >{{ $t('page.usermanagement.Delete') }}</el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
              >{{ $t('common.Export') }}</el-button>
          </el-col> -->
          <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar> -->
        </el-row>

        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column :label="$t('page.usermanagement.Delete')" align="center" key="userId" prop="userId" v-if="columns[0].visible" />
          <el-table-column :label="$t('page.usermanagement.UserAccount')" align="center" key="account" prop="account" v-if="columns[1].visible"
            :show-overflow-tooltip="true" />
          <el-table-column :label="$t('page.usermanagement.UserName')" align="center" key="username" prop="username" v-if="columns[2].visible"
            :show-overflow-tooltip="true" />
          <el-table-column :label="$t('page.usermanagement.PhoneNumber')" align="center" key="phoneNum" prop="phoneNum" v-if="columns[3].visible"
            width="120" />
          <el-table-column :label="$t('page.usermanagement.Status')" align="center" key="status" v-if="columns[4].visible">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                @change="handleStatusChange(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column :label="$t('page.usermanagement.CreationTime')" align="center" prop="createTime" v-if="columns[5].visible" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('page.usermanagement.Operation')" align="center" width="180" class-name="small-padding fixed-width">
            <template slot-scope="scope" v-if="scope.row.userId !== 1">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                >{{ $t('page.usermanagement.Update') }}</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                >{{ $t('page.usermanagement.Delete') }}</el-button>
              <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)"
                >
                <el-button size="mini" type="text" icon="el-icon-d-arrow-right">{{ $t('common.More') }}</el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="handleResetPwd" icon="el-icon-key"
                    > {{ $t('page.usermanagement.ResetPassword') }}</el-dropdown-item>
                  <!-- <el-dropdown-item command="handleAuthRole" icon="el-icon-circle-check"
                    >分配角色</el-dropdown-item> -->
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.curPage" :limit.sync="queryParams.pageSize"
          @pagination="getList" />
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('page.Serviceusermanagement.UserName')" prop="username">  
              <el-input v-model="form.username" :placeholder="$t('common.PleaseInputnickname')" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('page.usermanagement.PhoneNumber')" prop="phoneNum">
              <el-input v-model="form.phoneNum" :placeholder="$t('common.PleaseInputPhoneNumber')" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('page.usermanagement.Email')" prop="emailAddress">
              <el-input v-model="form.emailAddress" :placeholder="$t('common.PleaseInputEmail')" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" :label="$t('page.usermanagement.UserName')" prop="account">
              <el-input v-model="form.account" :placeholder="$t('common.PleaseInputUserName')" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" :label="$t('page.usermanagement.UserPassword')" prop="password">
              <el-input v-model="form.password" :placeholder="$t('common.PleaseInputPassWord')" type="password" maxlength="20" show-password />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('page.usermanagement.UserGender')">
              <el-select v-model="form.sex" :placeholder="$t('common.SelectGender')">
                <el-option
                  v-for="dict in dict.type.sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('common.Status')">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{ dict.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="12">
            <el-form-item label="岗位">
              <el-select v-model="form.postIds" multiple placeholder="请选择岗位">
                <el-option v-for="item in postOptions" :key="item.postId" :label="item.postName" :value="item.postId"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>  
          </el-col> -->
          <el-col :span="12">
            <el-form-item :title="$t('page.usermanagement.Role')">
              <el-select v-model="form.roleIds" multiple :placeholder="$t('common.PleaseSelectARole')">
                <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName" :value="item.roleId"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('common.Memo')">
              <el-input v-model="form.remark" type="textarea" :placeholder="$t('common.PleaseEnterContent')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.Determine') }}</el-button>
        <el-button @click="cancel">{{ $t('common.Cancellation') }}</el-button>
      </div>
    </el-dialog>

  </div>
</template>
  
<script>
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus,getRoleSelect } from "@/api/system/userNew";

export default {
  name: "User",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        curPage: 1,
        pageSize: 10,
        account: undefined,
        phoneNum: undefined,
        status: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `手机号码`, visible: true },
        { key: 4, label: `状态`, visible: true },
        { key: 5, label: `创建时间`, visible: true }
      ],
      // 表单校验
      rules: {
        account: [
          { required: true, message: "用户名称不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        username: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" }
        ],
        password: [
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
        ],
        emailAddress: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        phoneNum: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      },
      dict: {
        type: {
          //dicts: ['sys_normal_disable', 'sys_user_sex'],
          sys_normal_disable: [
            { value: "0", label: this.$t('common.Active') },
            { value: "1", label: this.$t('common.Inactive') },
          ],
          sys_user_sex: [
            { value: "0", label: this.$t('common.Male') },
            { value: "1", label: this.$t('common.Female') },
            { value: "2", label: this.$t('common.Unknown') },
          ]
        }
      }
    };
  },
  watch: {
  },
  created() {
    this.getList();
    // this.getConfigKey("sys.user.initPassword").then(response => {
    //   this.initPassword = response.msg;
    // });
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.userList = response.data.result.records;
        this.total = response.data.result.total;
        this.loading = false;
      }
      );
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.account + '"用户吗？').then(function () {
        return changeUserStatus(row.userId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        account: undefined,
        username: undefined,
        password: undefined,
        phoneNum: undefined,
        emailAddress: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        roleIds: []
      };
      this.resetForm("form");
      this.getRoleSelect();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.curPage = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleResetPwd":
          this.handleResetPwd(row);
          break;
        case "handleAuthRole":
          this.handleAuthRole(row);
          break;
        default:
          break;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加用户";
        this.form.password = this.initPassword;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids;
      getUser({userId:userId}).then(response => {
        this.form = response.data.user;
        this.$set(this.form, "roleIds", response.data.roleIds);
        this.open = true;
        this.title = "修改用户";
        this.form.password = "";
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.account + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: "用户密码长度必须介于 5 和 20 之间"
      }).then(({ value }) => {
        resetUserPwd(row.userId, value).then(response => {
          this.$modal.msgSuccess(this.$t('common.EditSuccess') + value);
        });
      }).catch(error => {
              this.loading = false
              this.$message({
                message: error,
                type: 'error',
                duration: 2 * 1000
              })
            });
    },
    /** 分配角色操作 */
    handleAuthRole: function (row) {
      const userId = row.userId;
      this.$router.push("/system/user-auth/role/" + userId);
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.userId != undefined) {
            updateUser(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.EditSuccess'));
              this.open = false;
              this.getList();
            }).catch(error => {
              this.loading = false
              this.$message({
                message: error,
                type: 'error',
                duration: 2 * 1000
              })
            });
          } else {
            addUser(this.form).then(response => {
              this.$modal.msgSuccess(this.$t('common.AddSuccess'));
              this.open = false;
              this.getList();
            }).catch(error => {
              this.loading = false
              this.$message({
                message: error,
                type: 'error',
                duration: 2 * 1000
              })
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.userId ? [row.userId] : this.ids;
      this.$modal.confirm('是否确认删除用户编号为"' + userIds + '"的数据项？').then(function () {
        return delUser({userIds:userIds});
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('common.DelSuccess'));
      }).catch(error => {
              this.loading = false
              this.$message({
                message: error,
                type: 'error',
                duration: 2 * 1000
              })
            });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    getRoleSelect(){
      getRoleSelect().then(response => {
        this.roleOptions = response.data.result;
      })
    }
  }
};
</script>