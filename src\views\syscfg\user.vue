<template>
  <div class="app-container">
    <!--用户数据-->
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="68px">
      <el-form-item v-if="$store.state.user.userInfo.userType === '1'" :label="$t('page.Serviceusermanagement.UserID')" prop="userName">
        <el-input
          v-model="queryParams.userName"
          :placeholder="$t('page.usermanagement.PlaseUserAccount')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button v-if="$store.state.user.userInfo.userType === '1'" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('common.Search') }}</el-button>
        <el-button v-if="$store.state.user.userInfo.userType === '1'" icon="el-icon-refresh" size="mini" @click="resetQuery" type="warning">{{ $t('common.Reset') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" style="margin-right:10px" @click="()=>getList()" type="primary">{{ $t('page.Serviceusermanagement.Refresh') }}</el-button>
        <el-button
          v-if="$store.state.user.userInfo.userType === '1'"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          type="success"
        >{{ $t('common.Add') }}</el-button>
      </el-form-item>
    </el-form>
    <!-- @selection-change="handleSelectionChange" -->
    <el-table v-loading="loading" :data="userList" border :header-cell-style="{background:'#f8f8f9'}">
      <el-table-column type="index" width="55" align="center" :index="indexMethod" :title="$t('common.SerialNumber')" />
      <!-- <el-table-column type="selection" width="50" align="center" /> -->
      <el-table-column key="account" :label="$t('page.usermanagement.UserAccount')" prop="account" />
      <el-table-column key="userName" :label="$t('page.usermanagement.UserName')" prop="userName" :show-overflow-tooltip="true" />
      <!-- <el-table-column key="nickName" label="用户昵称" prop="nickName" :show-overflow-tooltip="true" /> -->
      <el-table-column key="phoneNum" :label="$t('page.usermanagement.PhoneNumber')" prop="phoneNum" />
      <el-table-column key="emailAddress" :label="$t('page.usermanagement.Email')" prop="emailAddress" />
      <el-table-column :label="$t('common.CreationTime')" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.CreatorID')" prop="createBy" />
      <el-table-column
        :label="$t('common.Operation')"
        class-name="small-padding fixed-width"
        width="280"
      >
        <template v-if="scope.row.userType !== '1'" slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >{{ $t('common.Update') }}</el-button>
          <el-button
            v-if="$store.state.user.userInfo.userType === '1'"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >{{ $t('common.Delete') }}</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-key"
            @click="handleResetPwd(scope.row)"
          > {{ $t('page.usermanagement.ResetPassword') }}</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleLook(scope.row)"
          >{{ $t('common.View') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('page.usermanagement.PhoneNumber')" prop="phoneNum">
              <el-input v-model="form.phoneNum" :disabled="title === '查看用户'" :placeholder="$t('common.PleaseInputPhoneNumber')" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('page.usermanagement.Email')" prop="emailAddress">
              <el-input v-model="form.emailAddress" :disabled="title === '查看用户'" :placeholder="$t('common.PleaseInputEmail')" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="title === '添加用户'" :label="$t('page.usermanagement.UserAccount')" prop="account">
              <el-input v-model="form.account" :disabled="title === '查看用户'" :placeholder="$t('page.usermanagement.PleaseInputUserAccount')" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="title === '添加用户'" :label="$t('page.usermanagement.UserPassword')" prop="password">
              <el-input v-model="form.password" :disabled="title === '查看用户'" :placeholder="$t('common.PleaseInputPassWord')" type="password" maxlength="20" show-password />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <!-- v-if="form.userId == undefined" -->
            <!-- :label="$t('page.usermanagement.UserName')" -->
            <el-form-item :label="$t('page.usermanagement.UserName')" prop="userName">
              <el-input v-model="form.userName" :disabled="title === '查看用户'" :placeholder="$t('common.PleaseInputUserName')" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户类型" prop="userType">
              <el-select v-model="form.userType" :disabled="title === '查看用户'" :placeholder="$t('common.PleaseSelectUserType')" style="width:100%">
                <el-option
                  v-for="item in userTypeOptions"
                  v-show="item.show"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :title="$t('page.usermanagement.Role')">
              <el-select v-model="form.formRoles" :disabled="title === '查看用户'" multiple :placeholder="$t('common.PleaseSelectARole')" clearable filterable collapse-tags style="width:100%">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item :label="$t('common.Memo')">
              <el-input v-model="form.remark" type="textarea" :placeholder="$t('common.PleaseEnterContent')" />
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.Determine') }}</el-button>
        <el-button @click="cancel">{{ $t('common.Cancellation') }}</el-button>
      </div>
    </el-dialog>
    <el-pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus } from '@/api/system/user'
import { listRole } from '@/api/system/role'

export default {
  name: 'User',
  components: { },
  data() {
    return {
      storeUserInfo: {},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 默认密码
      initPassword: '123456',
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 用户类型
      userTypeOptions: [
        {
          id: '0',
          name: '非超级管理员',
          show: true
        },
        {
          id: '1',
          name: '超级管理员',
          show: this.$store.state.user.userInfo.userType === '1'
        }
      ],
      // 表单参数
      form: {
        userId: '',
        userName: '',
        account: '',
        password: '',
        phoneNum: '',
        emailAddress: '',
        remark: '',
        formRoles: [],
        userRoleBindInfos: [],
        userType: '0',
        createBy: '',
        createTime: ''
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phoneNum: undefined,
        status: undefined,
        deptId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `用户名称`, visible: true },
        { key: 2, label: `用户昵称`, visible: true },
        { key: 3, label: `部门`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true }
      ],
      // 表单校验
      rules: {
        userName: [
          { required: true, message: '用户昵称不能为空', trigger: 'blur' }
        ],
        account: [
          { required: true, message: '用户账号不能为空', trigger: 'blur' },
          { min: 2, max: 20, message: '用户账号长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        userType: [
          { required: true, message: '用户类型不能为空', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '用户密码不能为空', trigger: 'blur' },
          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
        ],
        emailAddress: [
          {
            type: 'email',
            message: '请输入正确的邮箱地址',
            trigger: ['blur', 'change']
          }
        ],
        phoneNum: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.storeUserInfo = this.$store.state.user.userInfo
    this.getList()
  },
  methods: {
    indexMethod(index) {
      return index + 1
    },
    /** 初始查询用户列表 */
    getList() {
      const { account, userType } = this.storeUserInfo
      // console.log('$store.state', this.$store.state)
      this.loading = true
      listUser(account, userType).then(response => {
        this.userList = response.data
        // this.total = response.total
        this.loading = false
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.handleQuery(this.queryParams.userName)
    },
    // 用户状态修改
    handleStatusChange(row) {
      const text = row.status === '0' ? 'Enable' : 'Deactivate'
      this.$confirm('Confirm to"' + text + '""' + row.userName + '"Users?', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(function() {
        return changeUserStatus(row.userId, row.status)
      }).then(() => {
        this.$message.success(text + 'Success')
      }).catch(function() {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      setTimeout(() => {
        this.reset()
      }, 500)
    },
    // 表单重置
    reset() {
      this.form = {
        id: '',
        userId: '',
        userName: '',
        password: '',
        phoneNum: '',
        emailAddress: '',
        remark: '',
        formRoles: [],
        userRoleBindInfos: [],
        roleInfos: [],
        userType: '0',
        createBy: '',
        createTime: ''
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.loading = true
      listUser(this.queryParams.userName, '0').then(response => {
        this.userList = response.data
        this.loading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      listRole().then(response => {
        this.roleOptions = response.data._menuInfo
        this.open = true
        this.title = '添加用户'
        this.form.password = this.initPassword
      })
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      // console.log('修改按钮操作', row)
      // await this.reset()
      this.form.phoneNum = row.phoneNum
      this.form.emailAddress = row.emailAddress
      this.form.id = row.id
      this.form.userName = row.userName
      this.form.account = row.account
      this.form.userType = row.userType + '' // 值为字符串
      const userId = row.id
      // 获取当前系统的所有角色
      listRole().then(response => {
        this.roleOptions = response.data._menuInfo
      })
      // 获取当前行用户拥有的角色
      getUser(userId).then(response => {
        this.form.formRoles = response.data._roleInfo.map(item => item.roleId)
        this.open = true
        this.title = '修改用户'
      })
    },
    /** 查看按钮操作 */
    async handleLook(row) {
      // console.log('修改按钮操作', row)
      // await this.reset()
      this.form.phoneNum = row.phoneNum
      this.form.emailAddress = row.emailAddress
      this.form.id = row.id
      this.form.userName = row.userName
      this.form.account = row.account
      this.form.userType = row.userType + '' // 值为字符串
      const userId = row.id
      // 获取当前系统的所有角色
      listRole().then(response => {
        this.roleOptions = response.data._menuInfo
      })
      // 获取当前行用户拥有的角色
      getUser(userId).then(response => {
        this.form.formRoles = response.data._roleInfo.map(item => item.roleId)
        this.open = true
        this.title = '查看用户'
      })
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      // console.log('重置密码按钮操作', row)
      this.$prompt('请输入"' + row.userName + '"的新密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: '用户密码长度必须介于 5 和 20 之间'
      }).then(({ value }) => {
        const params = {
          account: row.account,
          // oldPassword:row.account,
          newPassword: value,
          userType: row.userType
        }
        resetUserPwd(params).then(response => {
          this.$message.success(this.$t('common.EditSuccess') + value)
        })
      }).catch(() => {})
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const userId = this.storeUserInfo.userId
          this.form.createBy = this.storeUserInfo.account
          this.form.createTime = this.parseTime(Date.now())
          this.form.formRoles.forEach(item => {
            this.form.userRoleBindInfos.push({ userId: this.title === '添加用户' ? userId : this.form.id, roleId: item })
          })
          if (this.title !== '添加用户') {
            updateUser(this.form).then(response => {
              this.form.formRoles.length = 0
              this.form.userRoleBindInfos.length = 0
              this.$message.success(this.$t('common.EditSuccess'))
              this.open = false
              this.getList()
            }).catch(error => {
              this.form.formRoles.length = 0
              this.form.userRoleBindInfos.length = 0
              console.error('searchFun::error', error)
              // this.open = false
              this.$message.error(this.$t('common.ModificationFailed'))
            })
          } else {
            addUser(this.form).then(response => {
              this.form.formRoles.length = 0
              this.form.userRoleBindInfos.length = 0
              this.$message.success(this.$t('common.AddSuccess'))
              this.open = false
              this.getList()
            }).catch(error => {
              this.form.formRoles.length = 0
              this.form.userRoleBindInfos.length = 0
              console.error('searchFun::error', error)
              // this.open = false
              this.$message.error(this.$t('common.AddFailed'))
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.id
      this.$confirm('Are you sure to delete the username called"' + row.userName + '"Data？', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(function() {
        return delUser({ id: userIds, account: row.account })
      }).then(() => {
        this.getList()
        this.$message.success(this.$t('common.DelSuccess'))
      }).catch(() => {})
    }
  }
}
</script>
