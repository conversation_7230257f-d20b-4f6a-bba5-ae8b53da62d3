<template>
  <div style="padding:5px">

    <el-form
      :model="form"
      style="margin:15px 0 15px 0"
    >
      <el-form-item>
        <span>
          {{ $t('common.Date') }}:
          <el-date-picker
            v-model="form.start"
            :clearable="false"
            type="datetime"
            :placeholder="$t('page.usermanagement.StartDate')"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:185px"
          />
          -
          <el-date-picker
            v-model="form.end"
            :clearable="false"
            type="datetime"
            :placeholder="$t('page.usermanagement.EndDate')"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:185px"
          />
        </span>
        <span style="margin:0 0 0 20px">
          {{ $t('common.Chute') }}:
          <el-input v-model="form.chuteNo" style="width:136px" />
        </span>

        <span style="margin:0 0 0 20px">
          {{ $t('common.PackageGrade') }}:
          <el-input v-model="form.packageNumber" style="width:136px" />
        </span>

        <span style="margin:0 0 0 20px">
          {{ $t('common.ChipNumber') }}:
          <el-input v-model="form.rfid" style="width:136px" />
        </span>
        <el-button-group style="margin:15px 0 0 0">
          <el-button style="margin-right:15px" icon="el-icon-search" @click="loadData">{{ $t('common.Select') }}</el-button>
          <el-button style="margin-right:15px" icon="el-icon-refresh-left" @click="resetForm">{{ $t('common.Reset') }}</el-button>
          <el-button style="margin-right:15px" icon="el-icon-delete" @click="clearData">{{ $t('common.ClearPacketNumber') }}</el-button>
          <el-button icon="el-icon-download" @click="exportDataEvent">{{ $t('common.Export') }}</el-button>
        </el-button-group>
      </el-form-item>
    </el-form>

    <vxe-table
      ref="xTable"
      resizable
      show-overflow
      :height="tableHeight"
      row-id="id"
      :loading="loading"
      :data="tableData"
    >
      <vxe-table-column :resizable="true" type="seq" :title="$t('common.SerialNumber')" width="120" />
      <vxe-table-column :resizable="true" field="businessDate" :title="$t('common.BindingTime')" />
      <vxe-table-column :resizable="true" field="chuteNo" :title="$t('common.Chute')" />
      <vxe-table-column :resizable="true" field="packageNumber" :title="$t('common.PacketNumber')" />
      <vxe-table-column :resizable="true" field="rfid" :title="$t('common.ChipNumber')" />
      <vxe-table-column :resizable="true" field="userCode" :title="$t('common.BagBindingBfficer')" />
    </vxe-table>
    <vxe-pager
      background
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.totalResult"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    />
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'Packagenumber',
  data() {
    return {
      form: {
        start: this.getDate(0),
        end: this.getDate(1),
        step: ''
      },
      tableHeight: window.innerHeight - 130,
      loading: false,
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      }
    }
  },
  methods: {
    getDate(format) {
      var today = new Date()
      var year = today.getFullYear()
      var month = today.getMonth() + 1
      var date = today.getDate()

      if (format === 0) {
        return year + '-' + month + '-' + date + ' 00:00:00'
      } else if (format === 1) {
        return year + '-' + month + '-' + date + ' 23:59:59'
      }
    },
    loadData() {
      this.loading = true
      return request({
        url: '/packagenumber/loaddata',
        method: 'post',
        data: { queryParam: this.form, page: this.tablePage }
      }).then(resp => {
        this.tableData = resp.data.result
        this.tablePage = resp.data.page
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || '加载失败！',
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    exportDataEvent() {
      this.loading = true
      return request({
        url: '/packagenumberlog/exportdata',
        method: 'post',
        data: { startTime:this.form.start,endTime: this.form.end}
      }).then(resp => {
        this.$refs.xTable.exportData({
          filename: '历史包号',
          type: 'csv',
          data: resp.data.result
        })
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || this.$t('common.ExportFailed'),
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.loadData()
    },
    resetForm() {
      // this.form = {}
      this.form = {
        start: this.getDate(0),
        end: this.getDate(1)
      }
      this.tableData = []
      this.tablePage = {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      }
    },
    clearData() {
      this.$confirm('“Clearing the package number will immediately clear all the bound package numbers for the grid. Are you sure you want to continue?', 'prompt', {
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        type: 'warning'
      }).then(() => {
        this.loading = true
        return request({
          url: '/packagenumber/cleardata',
          method: 'post',
          data: this.tableData
        }).then(response => {
          this.loading = false
          this.loadData()
          this.$message({
            message: 'success',
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.loading = false
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      })
    }
  }
}
</script>
