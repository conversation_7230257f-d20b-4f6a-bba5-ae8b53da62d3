<template>
  <div class="navbar">
    <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb class="breadcrumb-container" />

    <div class="right-menu">
      <screenfull id="screenfull" class="right-menu-item hover-effect" />
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <img src="../../assets/user_images/userPic.png" class="user-avatar" />
          <span>{{ name }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu>
          <router-link to="/">
            <el-dropdown-item style="width: 70px; text-align: center">
              {{ $t("sidebar.Home") }}
            </el-dropdown-item>
          </router-link>
          <el-dropdown-item
            divided
            style="width: 70px; text-align: center"
            @click.native="logout"
          >
            {{ $t("headers.loginout") }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";

export default {
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull,
  },
  data() {
    return {
      imgUrl: "/assets/online.png",
    };
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "name"]),
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      this.$router.push(`/login?redirect=${this.$route.fullPath}`);
    },
  },
};
</script>


<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  // background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  background: linear-gradient(
    to right,
    #0755c9 20%,
    #2774e4 50%,
    /* 20% + 30% */ #1766da 80%,
    /* 50% + 30% */ #0154cc 100% /* 80% + 20% */
  );

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    color: #ffffff !important;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu-item {
    display: inline-block;
    // padding: 15px 10px;
    height: 100%;
    line-height: 50px;
    font-size: 18px;
    color: #ffffff;
    vertical-align: text-bottom;

    &.hover-effect {
      cursor: pointer;
      transition: background 0.3s;

      &:hover {
        background: rgba(0, 0, 0, 0.025);
      }
    }
  }

  .right-menu {
    // float: right;
    // height: 100%;
    line-height: 50px;
    display: flex;
    float: right;

    &:focus {
      outline: none;
    }

    .avatar-container {
      margin-right: 30px;
      line-height: 50px;
      color: #fff;
      .avatar-wrapper {
        cursor: pointer;
        font-size: 15px;
        margin: 0 10px;
        .user-avatar {
          width: 20px;
          height: 20px;
          margin-bottom: -4px;
          border-radius: 2px;
        }

        .el-icon-caret-bottom {
          position: absolute;
          color: #fff;
          right: -15px;
          top: 17px;
        }
      }
    }
  }
}
</style>
