const zh = {
  login: {
    changelanguage: "切换语言",
    index: "I'm six",
    title: "奥索斯分拣查询系统",
    Success: "登录成功",
    plsuaername: "请输入用户名",
    plspassword: "请输入密码",
    plscode: "请输入验证码密码",
    login: "登录",
    code: "验证码",
    chinese: "中文",
    english: "english",
    thai: "ภาษาไทย",
    ms: "Bahasa Melayu",
    vi: "Tiếng Việt",
    es: "Español",
    pt: "Bahasa Portugis",
    id: "Bahasa Indonesia",
    ph: "Filipino",
    SwipeRight: "向右滑动",
  },
  welcome: {
    title: "欢迎使用分拣日志查询系统",
  },
  headers: {
    title: "分拣数据查询系统",
    loginout: "退出系统",
  },
  sidebar: {
    Home: "首页",
    Syscfg: "系统配置",
    Userinfosup: "供包用户管理",
    User: "用户管理",
    Role: "角色管理",
    Menu: "菜单管理",
    Sysparam: "系统参数",
    Errchute: "异常格口",
    Sysurl: "日志保存天数",
    Sortingplan: "分拣方案",
    Datamgr: "数据管理",
    Runninglog: "运行日志",
    Locklog: "锁定日志",
    Sortinglog: "分拣日志",
    Packagenumberlog: "历史包号",
    Sortingrepush: "补推数据",
    Sortingreport: "报表统计",
    Chutereport: "格口分拣量",
    Sortingcountsup: "数量统计(供包台)",
    Sortingcountuser: "数量统计(工号)",
    Packagenumbercount: "总包统计",
    Sortingefficiency: "效率统计",
    Errorreport: "类型统计",
    Monitor: "系统监控",
    Scada: "SCADA监控",
    Job: "定时任务",
    Config: "参数设置",
    Dict: "字典管理",
  },
  page: {
    homepage: {
      boardingcount: "小车数量",
      scancount: "扫描数量",
      cellcount: "落格数量",
      cancelleditems: "取消件",
      rabbitinterceptions: "极兔拦截总数",
      unmatchedcodes: "三段码未匹配格口",
      overbounds: "超圈总数",
      anomalies: "异常总数",
      sortingvolume: "分拣量",
      monday: "周一",
      tuesday: "周二",
      wednesday: "周三",
      thursday: "周四",
      friday: "周五",
      saturday: "周六",
      sunday: "周日",
    },
    Serviceusermanagement: {
      Refresh: "刷新",
      AddUser: "新增用户",
      BulkDelete: "批量删除",
      FullName: "姓名",
      EmployeeID: "工号",
      Query: "查询",
      Import: "导入",
      OnlyExcel: "(只能导入excel.xlsx格式文件)",
      SerialNumber: "序号",
      UserEmployeeID: "用户工号",
      UserName: "用户姓名",
      UserID: "用户ID",
      Creator: "创建人",
      CreationTime: "创建时间",
      Operation: "操作",
      UserPassword: "用户密码",
      ConfirmPassword: "确认密码",
      NewPassword: "新密码",
      Save: "保存",
      Cancel: "取消",
      UpdateUser: "修改用户",
    },
    usermanagement: {
      UserID: "用户编号",
      UserName: "用户姓名",
      UserAccount: "用户账号",
      PlaseUserAccount: "请输入用户账号",
      PhoneNumber: "手机号码",
      PleasePhoneNumber: "请输入手机号码",
      Status: "状态",
      PleaseStatus: "请输入状态",
      StartDate: "开始日期",
      EndDate: "结束日期",
      CreationTime: "创建时间",
      Operation: "操作",
      UserGender: "用户性别",
      UserNickname: "用户昵称",
      Email: "邮箱",
      UserPassword: "用户密码",
      Active: "正常",
      Inactive: "停用",
      Role: "角色",
      Note: "备注",
      Search: "搜索",
      Reset: "重置",
      Add: "新增",
      Delete: "删除",
      Update: "修改",
      More: "更多",
      ResetPassword: "重置密码",
    },
  },
  common: {
    DerivedData: "导出数据",
    ViewDWSPeakEffect: "查看DWS峰值效率",
    peakEffect: "峰值效率",
    SearchData: "搜索数据",
    NoData: "暂无数据",
    Arrival: "进港",
    Departure: "出港",
    ArrivalAndDeparture: "进出港",
    CloseOther: "关闭其他",
    CloseAll: "关闭所有",
    RoleName: "角色名称",
    PlaseRoleName: "请输入角色名称",
    PermissionCharacters: "权限字符",
    PlasePermissionCharacters: "请输入权限字符",
    Order: "顺序",
    Status: "状态",
    Search: "搜索",
    Reset: "重置",
    Delete: "删除",
    Update: "修改",
    More: "更多",
    CreationTime: "创建时间",
    Operation: "操作",
    Update: "修改",
    Add: "新增",
    UpdateCache: "更新缓存",
    Select: "查询",
    Import: "导入",
    Export: "导出",
    SerialNumber: "序号",
    RoleNumber: "角色编号",
    Memo: "备注",
    UpdateDate: "更新时间",
    ParameterName: "参数名",
    ParameterValue: "参数值",
    PleaseSelectARole: "请选择角色",
    PleaseInputnickname: "请输入用户昵称",
    PleaseInputPhoneNumber: "请输入手机号码",
    PleaseInputEmail: "请输入电子邮件",
    PleaseInputUserName: "请输入用户名称",
    PleaseInputPassWord: "请输入密码",
    RoleStatus: "角色状态",
    RoleCode: "用户编号",
    PlaseInputRoleCode: "请输入用户编号",
    CreatorID: "创建人编号",
    Determine: "确 定",
    Cancellation: "取 消",
    Task: "任务名称",
    PlaseTask: "请输入任务名称",
    TaskCode: "任务编号",
    PlaseTaskCode: "请输入任务编号",
    MenuName: "菜单名称",
    PlaseMenuName: "请输入菜单名称",
    MenuStatus: "菜单状态",
    ExpandAndCollapse: "展开/折叠",
    SelectAllDonteSelectAll: "全选/全不选",
    ParentChildLinkage: "父子联动",
    MenuPermissions: "菜单权限",
    PleaseEnterContent: "请输入内容",
    Icon: "图标",
    ComponentPath: "Component Path",
    SchemeID: "方案ID",
    ModeType: "分拣类型",
    PlanName: "方案名称",
    PlanCode: "方案编号",
    PlanDesc: "方案描述",
    PlanFlag: "方案类型",
    IsSelected: "是否启用",
    UpdateDate: "修改时间",
    Detail: "明细",
    Enable: "启用",
    Date: "时间",
    Grade: "等级",
    Source: "来源",
    Message: "消息",
    ExtraData: "额外数据",
    plcType: "层",
    Number: "编号",
    Component: "部件",
    Chute: "格口",
    Supply: "供包台",
    dwsNo: "DWS序号",
    BigBarRate: "大包占比",
    Quantity: "数量",
    SmallBarRate: "小包占比",
    ExceptionCode: "异常码",
    Code: "单号",
    ScanTime: "扫描时间",
    BoardingTime: "上车时间",
    DropTime: "落格时间",
    NextPieceTime: "建包时间",
    passBackTime: "回传时间",
    arrivalTime: "到件时间",
    PacketNumber: "包号",
    TimeType: "时间类型",
    turns: "圈数",
    Image: "图片",
    UpperLevel: "上层",
    LowerLevel: "下层",
    LayerNumber: "层数",
    PleaseEnterTheDictionaryLabel: "请输入字典标签",
    DictionaryName: "字典名称",
    DictionaryId: "字典编号",
    DictionaryType: "字典类型",
    PleaseEnterTheDictionaryType: "请输入字典类型",
    PleaseEnterTheDictionaryName: "请输入字典名称",
    BagBindingBfficer: "绑包人员",
    DictionaryEncoding: "字典编码",
    DictionaryTag: "字典标签",
    DictionaryValue: "字典键值",
    DictionarySort: "字典排序",
    DictionaryRemark: "备注",
    DictionaryCreateTime: "创建时间",
    DataTag: "数据标签",
    PleaseEnterTheDataLabel: "请输入数据标签",
    DataKey: "数据键值",
    StyleAttribute: "样式属性",
    DisplayOrder: "显示排序",
    EchoStyle: "回显样式",
    ListClass: "列表样式",
    PleaseEnterTheDataKey: "请输入数据键值",
    PleaseEnterTheStyleAttribute: "请输入样式属性",
    PleaseEnterTheDisplayOrder: "请输入显示排序",
    PleaseEnterTheEchoStyle: "请输入回显样式",
    PleaseEnterTheListClass: "请输入列表样式",
    PleaseEnterTheRemark: "请输入备注",
    PleaseEnterTheContent: "请输入内容",
    TheAddressNeedsToBehttp: "选择是外链则路由地址需要以`http(s)://`开头",
    TheAddressNeedsToBehttpUser:
      "访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",
    TheAddressNeedsToBehttpCatalogue:
      "访问的组件路径，如：`system/user/index`，默认在`views`目录下",
    TheDefaultPassingParametersForTheRoute:
      "访问路由的默认传递参数，如：`{'id': 1, 'name': 'ry'}`",
    TheComponentWillBeCachedByKeepAlive:
      "选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",
    SelectHiddenThenTheRoute:
      "选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",
    SelectDisableThenTheRouteSidebar:
      "选择停用则路由将不会出现在侧边栏，也不能被访问",
    PleaseEnterTheRouteParameters: "请输入路由参数",
    Yes: "是",
    No: "否",
    PermissionCharactersString: "控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)",
    Cache1: "缓存",
    NoCache: "不缓存",
    AddUser: "新增用户",
    BatchCancelAuthorization: "批量取消授权",
    Close: "关闭",
    CancelAuthorization: "取消授权",
    View: "查看",
    UserType: "用户类型",
    PleaseSelectUserType: "请选择用户类型",
    Forward: "正 转",
    Reverse: "反 转",
    Lock: "锁 定",
    Unlock: "解 锁",
    SendMessage: "发消息",
    TaskGroup: "任务组名",
    PleaseSelectTaskGroup: "请选择任务组名",
    TaskStatus: "任务状态",
    PleaseSelectTaskStatus: "请选择任务状态",
    Log: "日志",
    InvokeTarget: "调用目标字符串",
    CronExpression: "cron执行表达式",
    ExecuteOnce: "执行一次",
    DispatchLog: "调度日志",
    InvokeTarget: "调用方法",
    BeanCallExample: "Bean调用示例：ryTask.ryParams('ry')",
    ClassCallExample:
      "Class类调用示例：com.ruoyi.quartz.task.RyTask.ryParams('ry')",
    ParameterDescription:
      "参数说明：支持字符串，布尔类型，长整型，浮点型，整型",
    PleaseInputInvokeTarget: "请输入调用目标字符串",
    PleaseInputCronExpression: "请输入cron执行表达式",
    GenerateExpression: "生成表达式",
    ExecuteStrategy: "执行策略",
    MisfirePolicy: "执行策略",
    ImmediateExecution: "立即执行",
    DelayExecution: "延迟执行",
    AbandonExecution: "放弃执行",
    PleaseSelectExecuteStrategy: "请选择执行策略",
    Concurrent: "是否并发",
    Allow: "允许",
    Prohibit: "禁止",
    PleaseSelectConcurrent: "请选择是否并发",
    CronExpressionGenerator: "Cron表达式生成器",
    NextExecutionTime: "下次执行时间",
    TaskDetails: "任务详情",
    TaskGroup1: "任务分组",
    DefaultStrategy: "默认策略",
    ExecuteStatus: "执行状态",
    PleaseSelectExecuteStatus: "请选择执行状态",
    ExecutionTime: "执行时间",
    PleaseSelectExecutionTime: "请选择执行时间",
    Clear: "清空",
    JobLogId: "日志编号",
    JobMessage: "日志信息",
    Detail1: "详细",
    DispatchLogDetails: "调度日志详细",
    PleaseSelect: "请选择",
    SelectStartTime: "选择开始时间",
    SelectEndTime: "选择结束时间",
    DebugStart: "调试(启动)",
    DebugClose: "调试(关闭)",
    ClearPacketNumber: "清除包号",
    Upload: "上传",
    TotalQuery: "共查询到",
    property: "张图片",
    all: "全部",
    UnloadingToDeliveryScanning: "卸车到件扫描",
    BuildingPackageScanning: "建包扫描",
    SelectUser: "选择用户",
    InvokeTargetMethod: "调用目标方法",
    PleaseSelectTaskGroup: "请选择任务分组",
    CronExpression1: "cron表达式",
    ExceptionInfo: "异常信息",
    Edit: "修改",
    ScopeOfAuthority: "权限范围",
    DataPermission: "数据权限",
    Confirm: "确认",
    StartDate: "开始日期",
    EndDate: "结束日期",
    Weight: "重量",
    length: "长",
    width: "宽",
    heigth: "高",
    CarNumber: "小车号",
    RequestedGate: "请求到的格口",
    PhysicalGrid: "PLC返回的物理格口",
    taskNo: "任务编号",
    TerminalDispatchCode: "三段码",
    FallingGridTime: "落格用时",
    PackageGrade: "包牌号",
    ChipNumber: "芯片号",
    BindingTime: "绑包时间",
    BindingPersonnel: "绑包人员",
    ScanType: "扫描类型",
    NextStationNumber: "下一站编号",
    Rfid: "电子包牌",
    TimeInterval: "时间区间",
    SortingQuantity: "分拣数量",
    TotalSortingWeight: "分拣总重(KG)",
    NumberOfScannedBarcodeRecords: "扫描条码记录数量",
    RecordTheNumberOfPackagesLoadedOntoTheVehicle: "包裹上车记录数量",
    NumberOfDropFeedbackRecords: "落格反馈记录数量",
    scanQuantity: "扫描数量",
    arrivalQuantity: "到件数量",
    passBackQuantity: "回传数量",
    TotalNumberOfPackages: "总包数量",
    Packagenumb: "包牌号(该时段内件数量)",
    Type: "类型",
    Count: "数量",
    SystemType: "系统类型",
    EmployeeID: "请输入工号",
    FullName: "请输入姓名",
    Password: "请输入密码",
    ConfirmPassword: "请再次输入密码",
    SelectGender: "请选择性别",
    Active: "正常",
    Inactive: "停用",
    Male: "男",
    Female: "女",
    Unknown: "未知",
    RoleOrder: "角色顺序",
    Show: "显示",
    Hide: "隐藏",
    Default: "默认",
    System: "系统",
    Success: "成功",
    Failure: "失败",
    AddMenu: "添加菜单",
    EditMenu: "修改菜单",
    ParentMenu: "上级菜单",
    MenuType: "菜单类型",
    Directory: "目录",
    Menu: "菜单",
    Button: "按钮",
    MenuIcon: "菜单图标",
    SelectIcon: "选择图标",
    RouteAddress: "路由地址",
    DisplayOrder: "显示排序",
    ExternalLink: "是否外链",
    DisplayStatus: "显示状态",
    MenuStatus: "菜单状态",
    RouteParameters: "路由参数",
    Cache: "是否缓存",
    ComponentPath: "组件路径",
    AddRole: "添加角色",
    EditRole: "修改角色",
    AddPlan: "新增方案",
    Cancel: "取消",
    FirstSegmentCode: "第一段码",
    SecondSegmentCode: "第二段码",
    ThirdSegmentCode: "第三段码",
    NextStopCode: "下一站编号",
    NextStopName: "下一站名称",
    ModificationTime: "修改时间",
    BulkDelete: "批量删除",
    AddDetails: "新增明细",
    ByServicePackage: "按供包台",
    ByDwsNo: "按DWS序号",
    Granularity: "粒度",
    LogicCode: "逻辑码",
    PacketType: "包类型",
    IsUpload: "是否上传",
    AddSuccess: "添加成功",
    EditSuccess: "修改成功",
    DelSuccess: "删除成功",
    ImportSuccessful: "导入成功",
    BeginExport: "开始导出",
    ModificationFailed: "修改失败",
    AddFailed: "新增失败",
    OperationSuccessful: "操作成功",
    OperationFailed: "操作失败",
    OperationCancellation: "操作取消",
    ExportFailed: "导出失败",
    LoginOut: "你已被登出，可以取消继续留在该页面，或者重新登录",
    ConfirmLogout: "确定登出",
    LogAgain: "重新登录",
    Remark: "备注",
    DwsNo: "DWS编号",
    Notempty: "账号密码或验证码不能为空！",
    Notpassword: "账号密码不能为空！",
    Id: "参数主键",
    Parameter: "参数名称",
    PlParameter: "请输入参数名称",
    ParameterKey: "参数键名",
    PlParameterKey: "请输入参数键名",
    ParameterValue: "参数键值",
    PlParameterValue: "请输入参数键值",
    Group: "组名",
    PlGroup: "请选择组名",
  },
  scada: {
    DeviceRunningStatus: "设备运行状态",
    DeviceStopped: "设备停止运行",
    DeviceRunning: "设备运行中",
    StartTime: "开始运行时间",
    RunningSpeed: "运行速度",
    CartOccupancyRate: "小车占有率",
    TotalDistanceTraveledByDevice: "设备运行总公里数",
    DistanceTraveledInCurrentRun: "本次运行公里数",
    TotalDistanceTraveled: "运行总公里数",
    Scans: "扫描数",
    PendingStart: "待开始运行",
    FullScreen: "全屏显示",
    ExitFull: "退出全屏",
    UpperLevelRunningSpeed: "上层运行速度",
    UpperLevelDeviceRunningStatus: "上层设备运行状态",
    UpperLevelStartTime: "上层开始运行时间",
    UpperLevelCartOccupancyRate: "上层小车占有率",
    UpperLevelByDevice: "上层设备运行总公里数",
    LowerLevelDeviceRunningStatus: "下层设备运行状态",
    UpperLayerPLCDisconnect: "上层PLC异常断开连接",
    LowerLevelPLCDisconnect: "下层PLC异常断开连接",
    PlCConnectionStatus: "PLC连接状态",
    UpperLayerPLCConnectionStatus: "上层PLC连接状态",
    LowerLevelPLCConnectionStatus: "下层PLC连接状态",
    LowerLevelStartTime: "下层开始运行时间",
    LowerLevelRunningSpeed: "下层运行速度",
    LowerLevelCartOccupancyRate: "下层小车占有率",
    LowerLevelTotalDistanceTraveledByDevice: "下层设备运行总公里数",
    UpperLevelScans: "上层扫描数",
    LowerLevelScans: "下层扫描数",
    AbnormalQuantity: "异常量",
    NumberOfSlotsOccupied: "落格数",
    FailedRepushQuantity: "失败补推数",
    InterceptedQuantity: "拦截数",
    ExcessiveCirclesQuantity: "超圈数",
    UnconfiguredThreeSegmentCodeSlots: "未配置三段码格口",
    ComprehensiveExceptionSlots: "综合异常格口",
    CancelledItems: "取消件",
    UnobtainedThreeSegmentCodeInformation: "未获取三段码信息",
    WebSocketStatus: "WebSocket状态",
    WCSCommunicationStatus: "WCS通讯状态",
    Connected: "已连接",
    NotConnected: "未连接",
    SortingStatus: "分拣状态",
    Idle: "空闲",
    Loaded: "载货",
    CartStatus: "小车状态",
    LittleThingsAreQuick:'小件快手',
    Headscratcher: '顶扫',
    OnLine:'在线',
    Offline:'离线',
    Locked: "锁定",
    FullPackage: "满包",
    SlotStatus: "格口状态",
    InterceptedItem: "拦截件",
    ExceptionSlot: "异常口",
    PendingCommunication: "待通信",
    Max: "最大循环",
    Cancellation: "取消件",
    UnProgramme: "未配方案",
    UnThree: "未配三段码",
    CartOperation: "小车操作",
    OneKeyUnlock: "一键解锁",
    OneKeyLockSlot: "一键锁格",
    CartNumber: "小车号",
    FloorNumber: "层数",
    UpperLevel: "上层",
    LowerLevel: "下层",
    Lock: "锁定",
    Unlock: "解锁",
    ManualSwitchStart: "手动切换（启动）",
    ManualSwitchClose: "手动切换（关闭）",
    Run: "运行",
    Close: "关闭",
    DisabledList: "禁用列表",
    AbnormalAlarm: "异常报警",
    DisableCart: "禁用小车",
    UpperLevelCart: "上层小车",
    LowerLevelCart: "下层小车",
    VerificationPassword: "请输入验证密码",
    Confirm: "确认",
    ClearTheKilometers: "清除公里数",
    AbnormalInfo: "报警信息",
    PleaseSelectClearKilometers: "选择清除公里数",
    FaultLevel: "故障级别",
    StartTime: "起始时间",
    EndTime: "结束时间",
    CriticalAlarm: "严重报警",
    GeneralAlarm: "一般报警",
    MinorAlarm: "轻微报警",
    SearchCriteria: "搜索条件",
    Time: "时间",
    Select: "查询",
    PleaseEnterContent: "请输入内容",
    PleaseEnterContent: "请输入内容",
    SerialNumber: "序号",
    Operation: "操作",
    AlarmHelpLink: "帮助文档",
    AlarmType: "报警类型",
    AlarmSource: "报警来源",
    Content: "内容",
  },
};
export default zh;
