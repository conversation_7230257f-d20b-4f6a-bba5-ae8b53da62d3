import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

import '@/styles/index.scss' // global css

import '@/icons/index'  // svg文件加载

import 'xe-utils'
import VXETable from 'vxe-table'
import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
import 'vxe-table/lib/index.css'
VXETable.use(VXETablePluginExportXLSX)
import App from './App'
import store from './store'
import router from './router'

import '@/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, handleTree } from '@/utils/ruoyi'
// 分页组件
import Pagination from "@/components/Pagination"
import plugins from './plugins' // plugins
// 字典标签组件
import DictTag from '@/components/DictTag'
// 字典数据组件
import DictData from '@/components/DictData'


import i18n from "./lang";
// import th from './lang/th'
// import zh from './lang/zh'
// import en from './lang/en'
import SlideVerify from 'vue-monoplasty-slide-verify'


// 全局方法挂载
Vue.prototype.getDicts = getDicts
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.handleTree = handleTree

// 全局组件挂载
Vue.component('Pagination', Pagination)
Vue.component('DictTag', DictTag)

Vue.use(ElementUI, { size: 'mini' })
Vue.use(VXETable, { size: 'mini' })
Vue.use(plugins)
Vue.use(SlideVerify)
DictData.install()

Vue.config.productionTip = false
// const i18n = new VueI18n({
//   locale: 'zh', // 设置默认语言为泰文
//   messages: {
//     th,
//     en,
//     zh, // 假设已经有中文翻译
//     // 其他语言
//   }
// })


new Vue({
  el: '#app',
  router,
  store,
  i18n,   // 注入，不能缺少
  render: h => h(App)
})
