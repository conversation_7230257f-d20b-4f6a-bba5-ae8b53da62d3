<!-- 常熟 1下2上 -->
<template>
  <screen-adaptive ref="screenAdaptive">
    <div class="page">
      <div class="content-wrapper">
        <!-- 左侧SVG区域 -->
        <div class="left-container">
          <!-- 状态信息显示区域 -->
          <div class="status-info-container">
            <!-- 单层模式 -->
            <div v-if="isOne" class="status-row-single">
              <div class="status-item">
                <span class="status-label"
                  >{{ $t("scada.DeviceRunningStatus") }}：</span
                >
                <div class="status-indicator">
                  <span
                    class="status-dot"
                    :class="{
                      running: speedDialDown > 0,
                      stopped: speedDialDown == 0,
                    }"
                  ></span>
                  <span class="status-value">{{ runningTextDown }}</span>
                </div>
              </div>

              <div class="status-item">
                <span class="status-label">{{ $t("scada.StartTime") }}：</span>
                <span class="status-value">{{ nowStartDateDown }}</span>
              </div>

              <div class="status-item">
                <span class="status-label"
                  >{{ $t("scada.TotalDistanceTraveledByDevice") }}：</span
                >
                <span class="status-value">{{ distanceDialDown }} km</span>
              </div>

              <div class="status-item">
                <span class="status-label"
                  >{{ $t("scada.RunningSpeed") }}：</span
                >
                <span class="status-value">{{ speedDialDown }} m/s</span>
              </div>

              <div class="status-item">
                <span class="status-label"
                  >{{ $t("scada.CartOccupancyRate") }}：</span
                >
                <span class="status-value"
                  >{{ sortingMinutesCarShareDown }}%</span
                >
              </div>
            </div>

            <!-- 双层模式 -->
            <div v-if="isTwo" class="status-container-dual">
              <div class="status-header">
                <div class="status-column">
                  <span class="status-label"
                    >{{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                    }}{{ $t("scada.DeviceRunningStatus") }}：</span
                  >
                </div>
                <div class="status-column">
                  <span class="status-label"
                    >{{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                    }}{{ $t("scada.StartTime") }}：</span
                  >
                </div>
                <div class="status-column">
                  <span class="status-label"
                    >{{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                    }}{{ $t("scada.UpperLevelRunningSpeed") }}：</span
                  >
                </div>
                <div class="status-column">
                  <span class="status-label"
                    >{{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                    }}{{ $t("scada.TotalDistanceTraveledByDevice") }}：</span
                  >
                </div>
                <div class="status-column">
                  <span class="status-label"
                    >{{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                    }}{{ $t("scada.CartOccupancyRate") }}：</span
                  >
                </div>
                <div class="status-column">
                  <span class="status-label"
                    >{{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                    }}{{ $t("scada.PlCConnectionStatus") }}：</span
                  >
                </div>
              </div>

              <div class="status-values">
                <!-- 上层数据 -->
                <div class="status-row upper-level">
                  <div class="status-column">
                    <div class="status-indicator">
                      <span
                        class="status-dot"
                        :class="{
                          running: runningText === $t('scada.DeviceRunning'),
                          stopped: runningText === $t('scada.DeviceStopped'),
                        }"
                      ></span>
                      <span class="status-value">{{ runningTextDown }}</span>
                    </div>
                  </div>
                  <div class="status-column">
                    <span class="status-value">{{ nowStartDateDown }}</span>
                  </div>
                  <div class="status-column">
                    <span class="status-value">{{ speedDialDown }} m/s</span>
                  </div>
                  <div class="status-column">
                    <span class="status-value">{{ distanceDial }}%</span>
                  </div>
                  <div class="status-column">
                    <span class="status-value"
                      >{{ sortingMinutesCarShareDown }}%</span
                    >
                  </div>
                  <div class="status-column">
                    <div class="connection-status">
                      <span
                        class="status-dot"
                        :class="{
                          connected:
                            LowerLevelConnectionStatus ===
                            $t('scada.Connected'),
                          disconnected:
                            LowerLevelConnectionStatus ===
                            $t('scada.NotConnected'),
                        }"
                      ></span>
                      <span class="status-value">{{
                        LowerLevelConnectionStatus
                      }}</span>
                    </div>
                  </div>
                </div>

                <!-- 下层数据 -->
                <div class="status-row lower-level">
                  <div class="status-column">
                    <div class="status-indicator">
                      <span
                        class="status-dot"
                        :class="{
                          running:
                            runningTextDown === $t('scada.DeviceRunning'),
                          stopped:
                            runningTextDown === $t('scada.DeviceStopped'),
                        }"
                      ></span>
                      <span class="status-value">{{ runningTextDown }}</span>
                    </div>
                  </div>
                  <div class="status-column">
                    <span class="status-value">{{ nowStartDateDown }}</span>
                  </div>
                  <div class="status-column">
                    <span class="status-value">{{ speedDialDown }} m/s</span>
                  </div>
                  <div class="status-column">
                    <span class="status-value">{{ distanceDialDown }}%</span>
                  </div>
                  <div class="status-column">
                    <span class="status-value"
                      >{{ sortingMinutesCarShareDown }}%</span
                    >
                  </div>
                  <div class="status-column">
                    <div class="connection-status">
                      <span
                        class="status-dot"
                        :class="{
                          connected:
                            LowerLevelConnectionStatus ===
                            $t('scada.Connected'),
                          disconnected:
                            LowerLevelConnectionStatus ===
                            $t('scada.NotConnected'),
                        }"
                      ></span>
                      <span class="status-value">{{
                        LowerLevelConnectionStatus
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 控制按钮容器 - 与white-box同级 -->
          <div class="control-buttons-container">
            <div class="control-buttons-wrapper">
              <!-- 异常报警按钮 -->
              <el-badge
                v-if="AbnormalAlarmInfo.messageNum >= 0"
                :value="
                  AbnormalAlarmInfo.messageNum
                    ? AbnormalAlarmInfo.messageNum
                    : 0
                "
                class="badge"
              >
                <button
                  type="button"
                  @click="abnormalAlarm"
                  class="control-btn primary-btn"
                >
                  <img
                    src="../../assets/images/scada/forewarning.png"
                    class="btn-icon"
                    :alt="`${$t('scada.AbnormalAlarm')}`"
                  />
                  <span>{{ $t("scada.AbnormalAlarm") }}</span>
                </button>
              </el-badge>

              <!-- 操作按钮组（隐藏状态） -->
              <button
                id="operate-unlock"
                type="button"
                @click="chuteUnLockAllBtn"
                class="control-btn success-btn"
                style="display: none"
              >
                <img
                  src="../../assets/images/scada/unlock.png"
                  class="btn-icon"
                  :alt="`${$t('scada.Unlock')}`"
                />
                <span>{{ $t("scada.OneKeyUnlock") }}</span>
              </button>
              <button
                id="operate-lock"
                type="button"
                @click="chuteLockAllBtn"
                class="control-btn success-btn"
                style="display: none"
              >
                <img
                  src="../../assets/images/scada/lock.png"
                  class="btn-icon"
                  :alt="`${$t('scada.OneKeyLockSlot')}`"
                />
                <span>{{ $t("scada.OneKeyLockSlot") }}</span>
              </button>

              <!-- 其他功能按钮 -->
              <button
                type="button"
                @click="clearTheKilometers"
                class="control-btn info-btn"
              >
                <img
                  src="../../assets/images/scada/kilometres.png"
                  class="btn-icon"
                  :alt="`${$t('scada.ClearTheKilometers')}`"
                />
                <span>{{ $t("scada.ClearTheKilometers") }}</span>
              </button>

              <button
                type="button"
                @click="requestFullScreen"
                class="control-btn primary-btn"
              >
                <img
                  src="../../assets/images/scada/BlowUp.png"
                  class="btn-icon"
                  alt="全屏"
                />
                <span>{{ $t("scada.FullScreen") }}</span>
              </button>
            </div>
          </div>

          <div class="white-box">
            <div id="svgDiv">
              <div>
                <div style="display: flex; flex-direction: column">
                  <!-- <div
                  style="
                    position: relative;
                    background: #fff !important;
                    z-index: 1000;
                  "
                >
                  <svg width="130%" height="100%">
                    <g v-if="isOne">
                      <text
                        x="20"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.DeviceRunningStatus") }}：
                      </text>
                      <circle
                        cx="125"
                        cy="20"
                        r="5"
                        fill="red"
                        id="run1"
                      ></circle>
                      <text
                        x="140"
                        y="24"
                        style="font-size: 14px; font-weight: bold"
                        id="text1"
                      >
                        {{ this.runningText }}
                      </text>

                      <text
                        x="260"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.StartTime") }}：
                      </text>
                      <text
                        x="350"
                        y="24"
                        style="font-size: 14px; font-weight: bold"
                        id="text2"
                      >
                        {{ this.nowStartDate }}
                      </text>

                      <text
                        x="540"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.TotalDistanceTraveledByDevice") }}：
                      </text>
                      <text
                        x="670"
                        y="24"
                        style="font-size: 14px; font-weight: bold"
                        id="text91"
                      >
                        {{ this.distanceDial }} km
                      </text>

                      <text
                        x="810"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.RunningSpeed") }}：
                      </text>
                      <text
                        x="880"
                        y="24"
                        style="font-size: 14px; font-weight: bold"
                        id="text3"
                      >
                        {{ this.speedDial }} m/s
                      </text>

                      <text
                        x="980"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.CartOccupancyRate") }}：
                      </text>
                      <text
                        x="1070"
                        y="24"
                        style="font-size: 14px; font-weight: bold"
                        id="text4"
                      >
                        {{ this.sortingMinutesCarShare }}%
                      </text>
                    </g>
                    <g v-if="isTwo">
                      <text
                        x="20"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                        }}{{ $t("scada.DeviceRunningStatus") }}：
                      </text>
                      <text
                        x="20"
                        y="50"
                        style="font-size: 14px; font-weight: bold"
                        id="text1"
                      >
                        {{ this.runningText }}
                      </text>
                      <text
                        x="20"
                        y="74"
                        style="font-size: 14px; font-weight: bold"
                        id="text1"
                      >
                        {{ this.runningTextDown }}
                      </text>

                      <text
                        x="220"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                        }}{{ $t("scada.StartTime") }}：
                      </text>
                      <text
                        x="220"
                        y="50"
                        style="font-size: 14px; font-weight: bold"
                        id="text2"
                      >
                        {{ this.nowStartDate }}
                      </text>

                      <text
                        x="220"
                        y="74"
                        style="font-size: 14px; font-weight: bold"
                        id="text1"
                      >
                        {{ this.nowStartDateDown }}
                      </text>

                      <text
                        x="380"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                        }}{{ $t("scada.UpperLevelRunningSpeed") }}：
                      </text>
                      <text
                        x="380"
                        y="50"
                        style="font-size: 14px; font-weight: bold"
                        id="text3"
                      >
                        {{ this.speedDial }} m/s
                      </text>

                      <text
                        x="380"
                        y="74"
                        style="font-size: 14px; font-weight: bold"
                        id="text3"
                      >
                        {{ this.speedDialDown }} m/s
                      </text>

                      <text
                        x="620"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                        }}{{ $t("scada.TotalDistanceTraveledByDevice") }}：
                      </text>
                      <text
                        x="620"
                        y="50"
                        style="font-size: 14px; font-weight: bold"
                        id="text4"
                      >
                        {{ this.distanceDial }}%
                      </text>

                      <text
                        x="620"
                        y="74"
                        style="font-size: 14px; font-weight: bold"
                        id="text4"
                      >
                        {{ this.distanceDialDown }}%
                      </text>

                      <text
                        x="850"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                        }}{{ $t("scada.CartOccupancyRate") }}：
                      </text>
                      <text
                        x="850"
                        y="50"
                        style="font-size: 14px; font-weight: bold"
                        id="text4_2"
                      >
                        {{ this.sortingMinutesCarShare }}%
                      </text>
                      <text
                        x="850"
                        y="74"
                        style="font-size: 14px; font-weight: bold"
                        id="text4_2"
                      >
                        {{ this.sortingMinutesCarShareDown }}%
                      </text>
                      <text
                        x="1030"
                        y="24"
                        style="font-size: 14px"
                        fill="#7e7e7e"
                      >
                        {{ $t("scada.UpperLevel") }}/{{ $t("scada.LowerLevel")
                        }}{{ $t("scada.PlCConnectionStatus") }}：
                      </text>
                      <text
                        x="1030"
                        y="50"
                        style="font-size: 14px; font-weight: bold"
                        id="text91"
                      >
                        {{ this.UpperLevelConnectionStatus }}
                      </text>
                      <text
                        x="1030"
                        y="74"
                        style="font-size: 14px; font-weight: bold"
                        id="text91"
                      >
                        {{ this.LowerLevelConnectionStatus }}
                      </text>
                    </g>
                  </svg>
                </div> -->
                </div>

                <div
                  class="svgPic"
                  @mousedown="handleMouseDown"
                  @mousemove="handleMouseMove"
                  @mouseup="handleMouseUp"
                  @mouseleave="handleMouseUp"
                  @wheel="handleWheel"
                  @contextmenu.prevent
                >
                  <svg
                    width="100%"
                    height="100%"
                    class="svg-container"
                    :viewBox="viewBoxValue"
                    preserveAspectRatio="xMidYMid meet"
                    :style="{
                      transform: `scale(${scale}) translate(${translateX}px, ${translateY}px)`,
                    }"
                  >
                    <!-- 供包台 1-->
                    <g
                      v-for="supply in generateSupplies1()"
                      :key="`supplys-${supply.number}`"
                      :style="{
                        transform: `translate(${supply.x}px, ${supply.y}px) rotate(-40deg)`,
                      }"
                    >
                      <g transform="rotate(45)">
                        <circle
                          :id="`supply${supply.number}`"
                          :cx="testPoint + 196"
                          :cy="testPoint + 9"
                          r="7"
                          fill="#979797"
                        />
                        <text
                          :x="testPoint + 196"
                          :y="testPoint + 8"
                          font-size="10"
                          font-weight="700"
                          fill="white"
                          text-anchor="middle"
                          alignment-baseline="middle"
                        >
                          {{ supply.number }}
                        </text>
                      </g>

                      <line
                        :x1="testPoint - 1"
                        :y2="testPoint + 190"
                        :x2="testPoint - 1"
                        :y1="testPoint + 248"
                        stroke="#979797"
                        stroke-width="2"
                      />
                      <line
                        :x1="testPoint + cellSize + 4"
                        :y1="testPoint + 190"
                        :x2="testPoint + cellSize + 4"
                        :y2="testPoint + 260"
                        stroke="#979797"
                        stroke-width="2"
                      />
                      <rect
                        v-for="i in packagesNum"
                        :key="`supply${supply.number}-${i}`"
                        :id="`supply${supply.number}-${i}`"
                        :x="testPoint + 1.3"
                        :y="testPoint - i * (cellSize + 2) + 248"
                        rx="2"
                        ry="2"
                        :width="cellSize"
                        :height="cellSize"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                    </g>
                    <!-- 供包台 2-->
                    <g
                      v-for="supply in generateSupplies2()"
                      :key="`supplys-${supply.number}`"
                      :style="{
                        transform: `translate(${supply.x}px, ${
                          supply.y - 68
                        }px) rotate(45deg)`,
                      }"
                    >
                      <g transform="rotate(0)">
                        <circle
                          :id="`supply${supply.number}`"
                          :cx="testPoint + 12"
                          :cy="testPoint + 8"
                          :r="cellSize - 10"
                          fill="#979797"
                        />
                        <text
                          transform="rotate(-40)"
                          :x="testPoint - 106"
                          :y="testPoint + 68"
                          :font-size="cellSize - 5"
                          font-weight="700"
                          fill="white"
                          text-anchor="middle"
                          alignment-baseline="middle"
                        >
                          {{ supply.number }}
                        </text>
                      </g>
                      <line
                        :x1="testPoint - 1"
                        :y1="testPoint - 4 * 18"
                        :x2="testPoint - 1"
                        :y2="testPoint + 18"
                        stroke="#979797"
                        stroke-width="2"
                      />
                      <line
                        :x1="testPoint + cellSize + 4"
                        :y1="testPoint - 4 * 22"
                        :x2="testPoint + cellSize + 4"
                        :y2="testPoint + 20"
                        stroke="#979797"
                        stroke-width="2"
                      />
                      <rect
                        v-for="i in packagesNum"
                        :key="`supply${supply.number}-${i}`"
                        :id="`supply${supply.number}-${i}`"
                        :x="testPoint + 2"
                        :y="testPoint - i * (cellSize + 2)"
                        rx="2"
                        ry="2"
                        :width="cellSize"
                        :height="cellSize"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                    </g>
                    <!-- 供包台 3-->
                    <g
                      v-for="supply in generateSupplies3()"
                      :key="`supplys-${supply.number}`"
                      :style="{
                        transform: `translate(${supply.x}px, ${supply.y}px) rotate(45deg)`,
                      }"
                    >
                      <g transform="rotate(-45)">
                        <circle
                          :id="`supply${supply.number}`"
                          :cx="testPoint - 100"
                          :cy="testPoint + 38"
                          r="7"
                          fill="#979797"
                        />
                        <text
                          :x="testPoint - 100"
                          :y="testPoint + 38"
                          font-size="10"
                          font-weight="700"
                          fill="white"
                          text-anchor="middle"
                          alignment-baseline="middle"
                        >
                          {{ supply.number }}
                        </text>
                      </g>
                      <line
                        :x1="testPoint - 1"
                        :y1="testPoint - 3 * 11"
                        :x2="testPoint - 1"
                        :y2="testPoint + 2 * 20"
                        stroke="#979797"
                        stroke-width="2"
                      />
                      <line
                        :x1="testPoint + cellSize + 4"
                        :y1="testPoint - 3 * 11"
                        :x2="testPoint + cellSize + 4"
                        :y2="testPoint + 1 * 25"
                        stroke="#979797"
                        stroke-width="2"
                      />
                      <rect
                        v-for="i in packagesNum"
                        :key="`supply${supply.number}-${i}`"
                        :id="`supply${supply.number}-${i}`"
                        :x="testPoint + 1.5"
                        :y="testPoint - (i - 1.7) * 14"
                        rx="2"
                        ry="2"
                        :width="cellSize"
                        :height="cellSize"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                    </g>
                    <!-- 供包台 4-->
                    <g
                      v-for="supply in generateSupplies4()"
                      :key="`supplys-${supply.number}`"
                      :style="{
                        transform: `translate(${supply.x}px, ${supply.y}px) rotate(-45deg)`,
                      }"
                    >
                      <g transform="rotate(0)">
                        <circle
                          :id="`supply${supply.number}`"
                          :cx="testPoint + 6"
                          :cy="testPoint - 4"
                          r="8"
                          fill="#979797"
                        />
                        <text
                          transform="rotate(45)"
                          :x="testPoint + 53"
                          :y="testPoint - 132"
                          font-size="10"
                          font-weight="700"
                          fill="white"
                          text-anchor="middle"
                          alignment-baseline="middle"
                        >
                          {{ supply.number }}
                        </text>
                      </g>
                      <line
                        :x1="testPoint - 3"
                        :y1="testPoint - 3 * 25"
                        :x2="testPoint - 3"
                        :y2="testPoint + 1 * 3"
                        stroke="#979797"
                        stroke-width="2"
                      />
                      <line
                        :x1="testPoint + cellSize + 3"
                        :y1="testPoint - 3 * 20"
                        :x2="testPoint + cellSize + 3"
                        :y2="testPoint + 1 * 3"
                        stroke="#979797"
                        stroke-width="2"
                      />
                      <rect
                        v-for="i in packagesNum"
                        :key="`supply${supply.number}-${i}`"
                        :id="`supply${supply.number}-${i}`"
                        :x="testPoint"
                        :y="testPoint - (i - 1) * 15 - 26"
                        rx="2"
                        ry="2"
                        :width="cellSize"
                        :height="cellSize"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                    </g>
                    <!-- 格口 -->
                    <g>
                      <!-- 在P8点上方添加横向格子  第一排上方 -->
                      <rect
                        v-for="cell in chuteNum8Cells"
                        :key="cell.text"
                        :id="`chute${cell.text}`"
                        :x="cell.x - 100"
                        :y="-20"
                        :width="cellSize + 4"
                        :height="cellSize"
                        @click="chuteClick(cell.text)"
                        rx="2"
                        ry="2"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                      <text
                        v-for="cell in chuteNum8Cells"
                        :key="`text-${cell.text}`"
                        :x="cell.x - 90"
                        :y="-10"
                        :font-size="cellSize - 7"
                        font-weight="bold"
                        fill="white"
                        text-anchor="middle"
                        alignment-baseline="middle"
                      >
                        {{ cell.text }}
                      </text>
                      <!-- 在P7点上方添加横向格子  第二三排中间右方 -->
                      <rect
                        v-for="cell in chuteNum7Cells"
                        :key="cell.text"
                        @click="chuteClick(cell.text)"
                        :id="`chute${cell.text}`"
                        :x="cell.x + 1414"
                        rx="2"
                        ry="2"
                        :y="cell.y + 275"
                        :width="cellSize * 1.1 + 4"
                        :height="cellSize"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                      <text
                        v-for="cell in chuteNum7Cells"
                        :key="`text-${cell.text}`"
                        :x="cell.x + 1423"
                        rx="2"
                        ry="2"
                        :y="cell.y + 282"
                        font-weight="bold"
                        font-size="8"
                        fill="white"
                        text-anchor="middle"
                        alignment-baseline="middle"
                      >
                        {{ cell.text }}
                      </text>
                      <!-- 在P6点上方添加横格子  第二排上方 -->
                      <rect
                        v-for="cell in chuteNum6Cells"
                        :key="cell.text"
                        :id="`chute${cell.text}`"
                        :x="cell.x - 308"
                        :y="636"
                        @click="chuteClick(cell.text)"
                        :width="cellSize + 4"
                        :height="cellSize"
                        rx="2"
                        ry="2"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                      <text
                        v-for="cell in chuteNum6Cells"
                        :key="`text-${cell.text}`"
                        :x="cell.x - 300"
                        :y="643"
                        font-weight="bold"
                        font-size="8"
                        fill="white"
                        text-anchor="middle"
                        alignment-baseline="middle"
                      >
                        {{ cell.text }}
                      </text>
                      <!-- 在P5点上方添加横向格子  右方右竖排 -->
                      <rect
                        v-for="cell in chuteNum5Cells"
                        :key="cell.text"
                        :id="`chute${cell.text}`"
                        :x="cell.x - 372"
                        :y="cell.y + 276"
                        :width="cellSize + 4"
                        :height="cellSize"
                        @click="chuteClick(cell.text)"
                        rx="2"
                        ry="2"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                      <text
                        v-for="cell in chuteNum5Cells"
                        :key="`text-${cell.text}`"
                        :x="cell.x - 364"
                        :y="cell.y + 283"
                        font-size="8"
                        font-weight="bold"
                        fill="white"
                        text-anchor="middle"
                        alignment-baseline="middle"
                      >
                        {{ cell.text }}
                      </text>
                      <!-- 在P4点上方添加横向格子  第三排下方 -->
                      <rect
                        v-for="cell in chuteNum4Cells"
                        :key="cell.text"
                        :id="`chute${cell.text}`"
                        :x="cell.x - 100"
                        :y="isOne ? 154 : 127"
                        :width="cellSize + 4"
                        @click="chuteClick(cell.text)"
                        :height="cellSize"
                        rx="2"
                        ry="2"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                      <text
                        v-for="cell in chuteNum4Cells"
                        :key="`text-${cell.text}`"
                        :x="cell.x - 92"
                        :y="isOne ? 161 : 135"
                        font-size="8"
                        font-weight="bold"
                        fill="white"
                        text-anchor="middle"
                        alignment-baseline="middle"
                      >
                        {{ cell.text }}
                      </text>
                      <!-- 在P3点上方添加横向格子  第二三排中间右方 -->
                      <rect
                        v-for="cell in chuteNum3Cells"
                        :key="cell.text"
                        :id="`chute${cell.text}`"
                        @click="chuteClick(cell.text)"
                        :x="cell.x + 1368"
                        rx="2"
                        ry="2"
                        :y="cell.y + 276"
                        :width="cellSize * 1.1 + 4"
                        :height="cellSize"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                      <text
                        v-for="cell in chuteNum3Cells"
                        :key="`text-${cell.text}`"
                        :x="cell.x + 1376"
                        rx="2"
                        ry="2"
                        :y="cell.y + 282"
                        font-weight="bold"
                        font-size="8"
                        fill="white"
                        text-anchor="middle"
                        alignment-baseline="middle"
                      >
                        {{ cell.text }}
                      </text>
                      <!-- 在P2点上方添加横格子  第二排上方 -->
                      <rect
                        v-for="cell in chuteNum2Cells"
                        @click="chuteClick(cell.text)"
                        :key="cell.text"
                        :id="`chute${cell.text}`"
                        :x="cell.x - 308"
                        :y="isOne ? 614 : 596"
                        :width="cellSize + 4"
                        :height="cellSize"
                        rx="2"
                        ry="2"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                      <text
                        v-for="cell in chuteNum2Cells"
                        :key="`text-${cell.text}`"
                        :x="cell.x - 300"
                        :y="isOne ? 621 : 603"
                        font-weight="bold"
                        font-size="8"
                        fill="white"
                        text-anchor="middle"
                        alignment-baseline="middle"
                      >
                        {{ cell.text }}
                      </text>
                      <!-- P1点上方添加横向格子  最左上方右排 -->
                      <rect
                        v-for="cell in chuteNum1Cells"
                        @click="chuteClick(cell.text)"
                        :key="cell.text"
                        :id="`chute${cell.text}`"
                        rx="2"
                        ry="2"
                        :x="cell.x - 328"
                        :y="cell.y + 276"
                        :width="cellSize + 4"
                        :height="cellSize"
                        fill="#979797"
                        stroke="white"
                        stroke-width="1"
                      />
                      <text
                        v-for="cell in chuteNum1Cells"
                        :key="`text-${cell.text}`"
                        :x="cell.x - 320"
                        :y="cell.y + 283"
                        font-weight="bold"
                        font-size="8"
                        fill="white"
                        text-anchor="middle"
                        alignment-baseline="middle"
                      >
                        {{ cell.text }}
                      </text>
                    </g>
                    <!-- 定义外部路径动画 -->
                    <path
                      id="outerPath"
                      fill="none"
                      :d="pathData"
                      stroke="rgba(0,0,255,0)"
                      stroke-width="2"
                      opacity="0.5"
                    />
                    <path
                      id="innerPath"
                      fill="none"
                      stroke="rgba(0,0,255,0)"
                      stroke-width="2"
                      opacity="0.5"
                      :d="innerPathData"
                    />
                    <defs>
                      <path
                        id="外部动画路径"
                        fill="none"
                        :d="pathData"
                        stroke="#000000"
                      />
                      <path id="内部动画路径" :d="innerPathData" />
                    </defs>
                    <g
                      stroke="none"
                      stroke-width="1"
                      fill="none"
                      fill-rule="evenodd"
                    >
                      <g>
                        <g id="outer-rects"></g>
                        <g id="inner-rects"></g>
                      </g>
                    </g>
                    <!-- 小方块组 -->
                    <g id="outerGroup" ref="outerGroup"></g>
                    <g id="innerGroup" ref="innerGroup"></g>
                    <!-- 添加圆角点位和标签 -->
                    <g v-for="point in cornerPoints" :key="point.id">
                      <!-- 第一个点 -->
                      <circle
                        :cx="point.x1"
                        :cy="point.y1"
                        r="4"
                        fill="red"
                        stroke="none"
                      />
                      <text
                        :x="point.x1 + 10"
                        :y="point.y1"
                        font-size="24"
                        fill="red"
                        text-anchor="start"
                        alignment-baseline="middle"
                      >
                        {{ point.label1 }}
                      </text>

                      <!-- 第二个点 -->
                      <circle
                        :cx="point.x2"
                        :cy="point.y2"
                        r="4"
                        fill="red"
                        stroke="none"
                      />

                      <g>
                        <rect
                          x="124.9;"
                          y="124.9;"
                          width="20"
                          height="20"
                          fill="#979797"
                          stroke="red"
                          stroke-width="1"
                        ></rect>
                        <text
                          x="129.9;"
                          y="124.9;"
                          font-size="10"
                          fill="yellow#979797"
                        >
                          P1
                        </text>
                      </g>
                    </g>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer区域 - 底部状态信息 -->
          <div
            class="status-bottom"
            style="background-color: rgba(0, 0, 0, 0.5)"
          >
            <div class="status-bar">
              <div class="status-row">
                <div class="status-item">
                  <span style="color: #7e7e7e"
                    >{{ $t("scada.WebSocketStatus") }}:</span
                  >
                  <span
                    class="status-dot"
                    id="websocket-status-dot"
                    :style="{ backgroundColor: getWebSocketStatusColor() }"
                  ></span>
                  <span> {{ this.webSocketRunText }}</span>
                </div>
                <div class="status-item">
                  <span style="color: #7e7e7e"
                    >{{ $t("scada.WCSCommunicationStatus") }}:</span
                  >
                  <span
                    class="status-dot"
                    id="wcs-status-dot"
                    :style="{ backgroundColor: getWCSStatusColor() }"
                  ></span>
                  <span>{{ this.wcsRunText }}</span>
                </div>
                <div class="status-item">
                  <span style="color: #7e7e7e"
                    >{{ $t("scada.CartStatus") }}:</span
                  >
                  <span>{{ $t("scada.Idle") }}:</span>
                  <span
                    class="status-dot connected"
                    style="background-color: #5ecb80"
                  ></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("scada.Loaded") }}:</span>
                  <span class="status-dot warning"></span>
                </div>
              </div>
              <div class="status-row">
                <div class="status-item">
                  <span style="color: #7e7e7e"
                    >{{ $t("scada.SlotStatus") }}:</span
                  >
                  <span>{{ $t("scada.Locked") }}:</span>
                  <span class="status-dot disconnected"></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("common.Active") }}:</span>
                  <span
                    class="status-dot"
                    style="background-color: #5ecb80"
                  ></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("scada.FullPackage") }}:</span>
                  <span
                    class="status-dot pending"
                    style="background-color: #8c008b"
                  ></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("scada.InterceptedItem") }}:</span>
                  <span
                    class="status-dot active"
                    style="background-color: #0101b5"
                  ></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("scada.ExceptionSlot") }}:</span>
                  <span
                    class="status-dot black"
                    style="background-color: #262626"
                  ></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("scada.PendingCommunication") }}:</span>
                  <span
                    class="status-dot neutral"
                    style="background-color: #979797"
                  ></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("scada.Max") }}:</span>
                  <span
                    class="status-dot disconnected"
                    style="background-color: #ffd222"
                  ></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("common.Cancel") }}:</span>
                  <span
                    class="status-dot active"
                    style="background-color: #4a428f"
                  ></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("scada.UnProgramme") }}:</span>
                  <span
                    class="status-dot"
                    style="background-color: #82cfff"
                  ></span>
                </div>
                <div class="status-item">
                  <span>{{ $t("scada.UnThree") }}:</span>
                  <span
                    class="status-dot"
                    style="background-color: #695592"
                  ></span>
                </div>
              </div>
            </div>
          </div>

          <el-dialog
            :title="$t('scada.DisabledList')"
            :visible.sync="forbiddenVisible"
            width="25%"
            append-to-body
          >
            <el-form ref="form" :model="form" label-width="80px">
              <el-form-item :label="$t('scada.DisableCart')" v-if="isOne">
                <el-input
                  v-model="forbidden.downCarStr"
                  type="textarea"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item
                :label="$t('scada.UpperLevelCart') + ':'"
                v-if="isTwo"
              >
                <el-input
                  v-model="forbidden.upCarStr"
                  type="textarea"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item
                :label="$t('scada.LowerLevelCart') + ':'"
                v-if="isTwo"
              >
                <el-input
                  v-model="forbidden.downCarStr"
                  type="textarea"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button @click="forbiddenVisible = false">{{
                $t("scada.Close")
              }}</el-button>
            </span>
          </el-dialog>

          <el-dialog
            :title="$t('scada.OneKeyUnlock')"
            :visible.sync="chuteUnLockVisible"
            width="25%"
            append-to-body
          >
            <div
              v-if="this.cellNum"
              style="
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                margin: -10px 0 10px 0;
              "
            >
              {{ $t("scada.Unlock")
              }}<span style="color: rgb(121, 190, 124)">{{ this.cellNum }}</span
              >{{ $t("common.Chute") }}
            </div>
            <el-form ref="form" :model="form" label-width="120px">
              <el-form-item :label="$t('scada.VerificationPassword') + ':'">
                <el-input
                  v-model="form.chuteUnLockPwd"
                  type="password"
                ></el-input>
              </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button @click="chuteUnLockAll()" type="danger">{{
                $t("common.Confirm")
              }}</el-button>
              <el-button @click="closeChuteUnLock()">{{
                $t("common.Close")
              }}</el-button>
            </span>
          </el-dialog>

          <el-dialog
            :title="$t('scada.OneKeyLockSlot')"
            :visible.sync="chuteLockVisible"
            width="25%"
            append-to-body
          >
            <el-form ref="form" :model="form" label-width="120px">
              <el-form-item :label="$t('scada.VerificationPassword') + ':'">
                <el-input
                  v-model="form.chuteLockPwd"
                  type="password"
                ></el-input>
              </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button @click="chuteLockAll()" type="danger">{{
                $t("common.Confirm")
              }}</el-button>
              <el-button @click="chuteLockVisible = false">{{
                $t("common.Close")
              }}</el-button>
            </span>
          </el-dialog>

          <el-dialog
            :title="$t('scada.CartOperation')"
            :visible.sync="carVisible"
            width="30%"
            append-to-body
          >
            <el-form ref="form" :model="form" label-width="80px">
              <el-form-item :label="$t('scada.CartNumber')">
                <el-input v-model="form.name"></el-input>
              </el-form-item>
              <el-form-item :label="$t('scada.FloorNumber')" v-if="isTwo">
                <el-radio-group v-model="form.number" @change="layChange">
                  <el-radio label="1">{{ $t("scada.UpperLevel") }}</el-radio>
                  <el-radio label="2">{{ $t("scada.LowerLevel") }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button @click="carLock()" type="danger">{{
                $t("scada.Lock")
              }}</el-button>
              <el-button @click="carUnLock()" type="success">{{
                $t("scada.Unlock")
              }}</el-button>
              <el-button
                @click="plcHand('1')"
                type="primary"
                v-if="isScadaStop"
                >{{ $t("scada.ManualSwitchStart") }}</el-button
              >
              <el-button
                @click="plcHand('0')"
                type="warning"
                v-if="!isScadaStop"
                >{{ $t("scada.ManualSwitchClose") }}</el-button
              >
              <el-button @click="plcCarNo()" type="primary">{{
                $t("scada.CartNumber")
              }}</el-button>
              <el-button @click="carRun()" type="warning">{{
                $t("scada.Run")
              }}</el-button>
              <el-button @click="carVisible = false">{{
                $t("common.Close")
              }}</el-button>
            </span>
          </el-dialog>

          <el-dialog
            :title="$t('scada.AbnormalAlarm')"
            :visible.sync="abnormalAlarmVisible"
            width="60%"
            append-to-body
            @closed="abnormalClose()"
          >
            <div class="AbnormalStyle fontWidth">
              {{ $t("scada.StartTime") }}:<el-date-picker
                v-model="AbnormalAlarmTime.startTime"
                type="datetime"
                :placeholder="$t('common.SelectStartTime')"
                :clearable="false"
                @change="(e) => changeAbnormal(e, 'startTime')"
              >
              </el-date-picker>
              <span style="margin-left: 20px">{{ $t("scada.EndTime") }}</span
              >:<el-date-picker
                v-model="AbnormalAlarmTime.endTime"
                type="datetime"
                :clearable="false"
                :placeholder="$t('common.SelectEndTime')"
                @change="(e) => changeAbnormal(e, 'endTime')"
              >
              </el-date-picker>
            </div>
            <div class="FaultLevelSty AbnormalStyle">
              <div
                style="display: flex; align-items: center"
                class="FaultLevelSty fontWidth"
              >
                <span class="fontWidth">
                  {{ $t("common.Source") }}:
                  <el-select
                    v-model="AbnormalAlarmInfo.logSource"
                    style="width: 95px"
                  >
                    <el-option
                      v-for="dict in dict.type.runLogSource"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </span>
                <div style="margin-left: 20px">
                  {{ $t("scada.SearchCriteria") }}:
                  <el-input
                    :placeholder="$t('common.PleaseEnterContent')"
                    v-model="AbnormalAlarmInfo.SearchCriteria"
                    style="width: 120px"
                    @input="(e) => changeAbnormal(e, 'SearchCriteria')"
                  />
                </div>
                <!-- 故障级别 -->
                <!-- 故障级别: 
                <el-checkbox 
                v-model="AbnormalAlarmInfo.FaultLevelValueList[index]" 
                text-color="rgb(0,0,0)"
                v-for="(el, index) in AbnormalAlarmInfo.FaultLevelList" 
                :key="index"
                @change="(val) => changeFaultLevelValueList(val,index)"
                >
                   <span class="fontWidth" style="color: #666">{{ el }}</span>
                </el-checkbox> -->
                <!-- <el-radio-group v-model="AbnormalAlarmInfo.clickGroup">
             <el-radio-button
                class="fontWidth"
                :key="item.id"
                v-for="item in AbnormalAlarmInfo.AbnormalAlarmList"
                :label="item.name" >{{ item.name }}
             </el-radio-button>
          </el-radio-group> -->
              </div>
              <div>
                <el-button @click="abnormalAlarm()" type="primary">{{
                  $t("scada.Select")
                }}</el-button>
                <!-- <el-button @click="abnormalExport()" type="primary">导出</el-button> -->
              </div>
            </div>
            <SACADAABNORMALLIST
              :abnormalList="abnormalList"
              :AbnormalAlarmInfo="AbnormalAlarmInfo"
              @getParentFn="childParent"
              @postSuccess="postSuccess"
              @abnormalAlarm="abnormalAlarm"
            />

            <!-- :loading="AbnormalAlarmInfo.loading" -->
            <vxe-pager
              background
              :current-page="AbnormalAlarmInfo.currentPage"
              :page-size="AbnormalAlarmInfo.pageSize"
              :total="AbnormalAlarmInfo.totalResult"
              :layouts="[
                'PrevPage',
                'JumpNumber',
                'NextPage',
                'FullJump',
                'Sizes',
                'Total',
              ]"
              @page-change="handlePageChange"
            />
            <span slot="footer" class="dialog-footer">
              <el-button @click="debugBtn()" type="primary" v-if="isLog">{{
                $t("scada.DebugStart")
              }}</el-button>
              <el-button @click="debugBtn()" type="warning" v-if="!isLog">{{
                $t("common.DebugClose")
              }}</el-button>
              <el-button @click="abnormalClose()">{{
                $t("common.Close")
              }}</el-button>
            </span>
          </el-dialog>

          <el-dialog
            :title="$t('scada.ClearTheKilometers')"
            :visible.sync="clearTheKilometersVisible"
            width="25%"
            append-to-body
          >
            <el-form ref="form" :model="form" label-width="120px">
              <!-- <el-form-item label="选择清除公里数">
                <el-select v-model="form.selectUpOrDownVal" placeholder="请选择" @change="changeSelectVal" style="width: 100%">
                <el-option
                style=""
                   v-for="item in [ {value:'2',label: `上层`}, {value: '1',label: `下层`}]"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value">
                </el-option>
             </el-select>
             </el-form-item> -->
              <el-form-item :label="$t('scada.VerificationPassword')">
                <el-input
                  v-model="form.chuteLockPwd"
                  type="password"
                ></el-input>
              </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button @click="clearTheKilometersDialog()" type="danger">{{
                $t("common.Confirm")
              }}</el-button>
              <el-button @click="clearTheKilometersVisible = false">{{
                $t("common.Close")
              }}</el-button>
            </span>
          </el-dialog>
        </div>

        <!-- 右侧区域 -->
        <div class="right-container">
          <div class="upper-box" style="margin-bottom: 1rem; display: block">
            <GridItem
              :dataNum="{
                scansNum: this.scansNumDown,
                scansNumDown: isOne
                  ? this.scansNumDown
                  : this.sortingMinutesDistance,
                distanceDial: this.distanceDialDown,
                sortingErrorNum: this.sortingErrorNum,
                fallingNum: this.fallingNum,
                exceedsMaxNum: this.exceedsMaxNum,
                interceptNum: this.interceptNum,
                hypercycleNum: this.hypercycleNum,
                notConfiguredGrid: this.notConfiguredGrid,
                abnormalNum: this.abnormalNum,
                cancelNum: this.cancelNum,
                notObtainedNum: this.notObtainedNum,
              }"
              :runSpeedAndRate="{
                speedDial: this.speedDialDown,
                speedDialDown: this.speedDial,
                sortingMinutesCarShare: this.sortingMinutesCarShareDown,
                sortingMinutesCarShareDown: this.sortingMinutesCarShare,
              }"
              :isTwo="isTwo"
              :ShowTheNumberOfBottomScans="!isOne"
            />
            <!-- ShowTheNumberOfBottomScans // 是否显示下层扫描数 -->
            <AndInfo :isTwo="isTwo" />
          </div>
        </div>
      </div>
      <div v-if="showOverlay" class="overlay" append-to-body>
        <div class="overlay-content">
          <p>Loading...</p>
        </div>
      </div>
    </div>
  </screen-adaptive>
</template>
 
<script>
import ScreenAdaptive from "@/components/ScreenAdaptive.vue";
import Footer from "@/views/sysMonitor/Footer.vue";
import FooterTwo from "@/views/sysMonitor/FooterTwo.vue";
import SACADAABNORMALLIST from "@/views/sysMonitor/scadaAbnormalList.vue";
import GridItem from "@/views/sysMonitor/components/GridItem.vue";
import AndInfo from "@/views/sysMonitor/components/AndInfo.vue";
import request from "@/utils/request";
//import getMAC, { isMAC } from 'getmac'
import { v4 as uuidv4 } from "uuid";
//import path from 'path'
import { Message, MessageBox } from "element-ui";
import Cookies from "js-cookie";
import moment from "moment";
export default {
  components: {
    Footer,
    FooterTwo,
    SACADAABNORMALLIST,
    GridItem,
    AndInfo,
    ScreenAdaptive,
  },
  dicts: ["runLogSource"],
  data() {
    return {
      Distinguish: "0", //0为一个PLC、1为两个PLC
      //LayerNum: 1,//层数
      carCountState: 1, //1 无锡小车号特殊处理,
      isOne: false,
      isTwo: true,
      carVisible: false,
      form: {
        name: "",
        number: "2",
        chuteLockPwd: "",
        chuteUnLockPwd: "",
      },
      cellNum: "", // 解锁格口
      speedDial: "0.000", //运行速度
      speedDialDown: "0.000", //下层运行速度
      sortingMinutesCarShare: "0.000", //小车占有率
      sortingMinutesCarShareDown: "0.000", //下层小车占有率
      distanceDial: "0.000", //设备运行总公里数
      distanceDialDown: "0.000", //下层设备运行总公里数
      UpperLevelConnectionStatus: this.$t("scada.NotConnected"), // 上层PLC连接状态文案
      LowerLevelConnectionStatus: this.$t("scada.NotConnected"), // 下层PLC连接状态文案
      clearTheKilometersVisible: false, // 清除公里数弹框
      AbnormalAlarmTime: {
        startTime: "",
        endTime: "",
      },
      AbnormalAlarmInfo: {
        logSource: " ",
        loading: false,
        SearchCriteria: "",
        AbnormalAlarmList: [
          { name: "设备报警", id: 1 },
          { name: "业务报警", id: 2 },
        ],
        clickGroup: "设备报警",
        FaultLevelValueList: [false, false, true],
        FaultLevelList: [
          this.$t("scada.CriticalAlarm"),
          this.$t("scada.GeneralAlarm"),
          this.$t("scada.MinorAlarm"),
        ],
        abnormalListColumn: [
          {
            prop: "SerialNumber",
            label: this.$t("scada.SerialNumber"),
            align: "center",
            width: 80,
          },
          {
            prop: "createTime",
            label: this.$t("scada.Time"),
            align: "center",
            width: 135,
          },
          {
            prop: "alarmSource",
            label: this.$t("scada.AlarmSource"),
            align: "center",
            width: 135,
          },
          {
            prop: "message",
            label: this.$t("scada.Content"),
            align: "center",
            width: 420,
          },
          {
            prop: "alarmHelpUrl",
            label: this.$t("scada.AlarmHelpLink"),
            align: "center",
            width: 135,
          },
          {
            prop: "Operation",
            label: this.$t("scada.Operation"),
            align: "center",
            width: 200,
            fixed: "right",
          },
        ],
        totalResult: 0,
        currentPage: 1,
        pageSize: 10,
        messageNum: 0,
      },
      alertInstance: null, // 弹窗实例
      isPopupContent: "", // 弹窗内容是否和上次一样,值
      forbiddenVisible: false,
      chuteUnLockVisible: false,
      chuteLockVisible: false,
      abnormalAlarmVisible: false,
      abnormalList: [],
      forbidden: {
        upCarStr: "",
        downCarStr: "",
      },
      forbiddenDis: "false",
      scalesNum: 1, // 缩放比例
      // WsUrl: "ws://192.168.20.160:9004/ws", //"ws://82.157.123.54:9010/ajaxchattest",
      WsUrl: "ws://192.168.150.198:9004/ws", //"ws://82.157.123.54:9010/ajaxchattest",
      // 
      websock: null,
      ws_heart: null,
      connectTimer: null, // 重连对象
      socket: null,
      isFullFlag: false, //是否全屏
      CarNum: 670,
      ChuteNum: 352,
      SupplysNum: 24,
      width: "1700px",
      height: "845px",
      heightDown: 805,
      // 上层格口1 2，下层格口3 4
      ChuteNum1: "",
      ChuteNum2: "",
      ChuteNum3: "",
      ChuteNum4: "",
      ChuteNum5: "",
      ChuteNum6: "",
      ChuteNum7: "",
      ChuteNum8: "",
      //异常格口
      ChuteErro: "160",
      alertInstance: null,
      //上层供包台34，下层供包台12
      SupplysNum1: "",
      SupplysNum2: "",
      SupplysNum3: "",
      SupplysNum4: "",
      isCreateCar: false,
      isCreateChute: false,
      isCreateSupplys: false,
      oneTime: 62.2, //小车一圈时间/S
      time1: 0,
      time2: 0,
      runPath1: "",
      runPath2: "",
      scansNum: "0", //上层扫描数
      scansNumDown: "0", //下层扫描数
      sortingMinutesDistance: "0", //本次运行公里数
      sortingErrorNum: "0", //异常量
      fallingNum: "0", //落格数
      exceedsMaxNum: "0", //失败补推数
      interceptNum: "0", //拦截数
      hypercycleNum: "0", //超圈数
      notConfiguredGrid: "0", //未配置三段码格口
      abnormalNum: "0", //综合异常口
      cancelNum: "0", //取消件
      notObtainedNum: "0", //未获取三段码信息
      runningText: this.$t("scada.DeviceRunning"), //设备运行状态
      runningTextDown: this.$t("scada.DeviceRunning"), //设备运行状态
      nowStartDate: this.$t("scada.PendingStart"), //开始运行时间
      nowStartDateDown: this.$t("scada.PendingStart"), //开始运行时间
      speedDial: "0.000", //运行速度
      speedDialDown: "0.000", //下层运行速度
      sortingMinutesCarShare: "0.000", //小车占有率
      sortingMinutesCarShareDown: "0.000", //下层小车占有率
      webSocketRunText: this.$t("scada.NotConnected"),
      wcsRunText: this.$t("scada.NotConnected"),
      isMainIp: false, // 白名单
      fullText: this.$t("scada.FullScreen"),
      testPoint: 30,
      isDragging: false,
      startX: 0,
      startY: 0,
      translateX: 0,
      translateY: 0,
      scale: 0.8, // 调整初始缩放，适应固定容器
      minScale: 0.2,
      maxScale: 2.0,
      isMobile: false,
      cellSize: 20,
      cornerPoints: [],
      number: 21,
      isCtrlPressed: false,
      isOuterPaused: true,
      isInnerPaused: true,
      isOuterClockwise: false,
      isInnerClockwise: false,
      outerAnimationDuration: 60,
      innerAnimationDuration: 60,
      packagesNum: 3,
      //上层供包台1 2，下层供包台3 4
      innerPathData:
        "M-90,10 h1426 a10,10 0 0 1 10,10 v444 a10,10 0 0 1 -10,10 h-1426 a10,10 0 0 1 -10,-10 v-444 a10,10 0 0 1 10,-10 z",
      pathData:
        "M-177,123 h1700 a10,10 0 0 1 10,10 v468 a10,10 0 0 1 -10,10 h-1700 a10,10 0 0 1 -10,-10 v-468 a10,10 0 0 1 10,-10 z",
      baseHeightNum: 937,
      upNo: "2",
      downNo: "1",
      isScadaStop: true,
      isLog: false,
      systemOptions: this.getBoxValue(),
      systemTypeCode: "1",
      isWcs: true,
      isFlb: false,
      showOverlay: false,
      account: "",
      refreshTimer: null,
      isScadaRoute: false,
    };
  },
  created() {
    // 尝试从存储中恢复状态
    this.checkRoute(this.$route);
    this.$nextTick(() => {
      // this.getSvg();
    });
    if (window.location.href.includes("scadajcd")) {
      setInterval(() => {
        // 显式拼接完整的 URL
        window.location.reload(true);
      }, 3600 * 1000);
    }
  },
  computed: {
    viewBoxValue() {
      // 计算包含所有路径的viewBox
      // 内层路径: x从-190到1536 (1726宽度)，y从110到624 (494高度)
      // 外层路径: x从-177到1523 (1700宽度)，y从123到601 (468高度)
      // 需要包含从x=-190到x=1536的范围，总宽度约1726
      // 需要包含从y=110到y=624的范围，总高度约514

      // 调整viewBox以包含完整的动画轨道，并添加一些边距
      return "-110 80 1600 580";
    },
    ...Array.from({ length: 16 }, (_, i) => i + 1).reduce((acc, num) => {
      acc[`chuteNum${num}Cells`] = function () {
        return this.chuteNumsCells(`ChuteNum${num}`);
      };
      return acc;
    }, {}),
  },
  mounted() {
    this.checkDevice();
    this.getAccount();
    window.addEventListener("resize", this.checkDevice);
    this.changeAbnormal(
      moment().format("YYYY-MM-DD") + " 00:00:00",
      "startTime"
    );
    this.changeAbnormal(new Date(), "endTime");

    // 添加全屏状态变化监听
    document.addEventListener("fullscreenchange", this.handleFullscreenChange);
    document.addEventListener(
      "webkitfullscreenchange",
      this.handleFullscreenChange
    );
    document.addEventListener(
      "mozfullscreenchange",
      this.handleFullscreenChange
    );
    document.addEventListener(
      "msfullscreenchange",
      this.handleFullscreenChange
    );

    // 监听页面全屏
    window.addEventListener("click", () => {
      this.$forceUpdate();
    });
    window.addEventListener("keydown", (event) => {
      if (event.key === "F12") {
        this.$forceUpdate();
      }
    });
    window.addEventListener("keydown", this.handleKeyDown);
    window.addEventListener("keyup", this.handleKeyUp);
    window.addEventListener("wheel", this.handleWheel, { passive: true });

    document.getElementById("svgDiv").style.userSelect = "none";
    var browerWidth = window.innerWidth; //window.getComputedStyle(document.getElementsByClassName('app-main')[0]).width.replace("px", "")//浏览器可视宽度
    var baseWidth = 1920; //设计稿宽度
    var zoomValue = browerWidth / baseWidth; //缩放比例计算

    var browerHeight = window.innerHeight; //window.getComputedStyle(document.getElementsByClassName('app-main')[0]).height.replace("px", "")//浏览器可视高度
    var baseHeight = 937; //设计稿宽度
    var zoomValue1 = browerHeight / baseHeight; //缩放比例计算
    var dom = document.getElementsByClassName("app-main")[0];
    document.getElementById("svgDiv").style.transform =
      "scale(" + zoomValue + "," + zoomValue1 + ")"; //mainContainer为主容器id
    // document.getElementById("svgDiv").style.transformOrigin = "left top";
    window.onresize = () => {
      //窗口尺寸变化时，重新计算和缩放
      var browerWidth = window.innerWidth;
      var browerHeight = window.innerHeight;
      var flag = this.isFullFlag;
      if (document.fullscreenElement) {
        zoomValue = browerWidth / 1640;
        zoomValue1 = browerHeight / 880; //缩放比例计算
        this.fullText = this.$t("scada.ExitFull");
      } else {
        zoomValue = browerWidth / baseWidth;
        zoomValue1 = browerHeight / baseHeight; //缩放比例计算
        this.fullText = this.$t("scada.FullScreen");
      }
      document.getElementById("svgDiv").style.transform =
        "scale(" + zoomValue + "," + zoomValue1 + ")";
      document.getElementById("svgDiv").style.userSelect = "none";
    };
    document.addEventListener("keydown", this.onKeyDown);
  },
  beforeDestroy() {
    document.removeEventListener("keydown", this.onKeyDown);
    window.removeEventListener("resize", this.checkDevice);
    window.removeEventListener("keydown", this.handleKeyDown);
    window.removeEventListener("keyup", this.handleKeyUp);
    // 组件销毁前清除定时器
    this.clearRefreshTimer();
    // 移除全屏状态监听器
    document.removeEventListener(
      "fullscreenchange",
      this.handleFullscreenChange
    );
    document.removeEventListener(
      "webkitfullscreenchange",
      this.handleFullscreenChange
    );
    document.removeEventListener(
      "mozfullscreenchange",
      this.handleFullscreenChange
    );
    document.removeEventListener(
      "msfullscreenchange",
      this.handleFullscreenChange
    );
  },
  // beforeCreate() {
  //   console.log("beforeCreate: data 和 methods 尚未初始化");
  //   console.log("this.$router.query", this.$route.query);
  //   this.port = this.$route.query.port;
  //   console.log("this.prot", this.port);
  //   // 此时访问 t"his.data 会返回 undefined
  // },
  methods: {
    reload() {
      location.href = location.href.split("?")[0] + "?ts=" + Date.now();
    },
    // 获取WebSocket状态颜色
    getWebSocketStatusColor() {
      return this.webSocketRunText === this.$t("scada.Connected")
        ? "#5ECB80"
        : "#FF5161";
    },
    // 获取WCS通讯状态颜色
    getWCSStatusColor() {
      return this.wcsRunText === this.$t("scada.Connected")
        ? "#5ECB80"
        : "#FF5161";
    },
    getBottomPosition() {
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;
      // console.log("screenWidth", screenWidth);
      // console.log("screenHeight", screenHeight);
      // 检查是否是1024*768分辨率
      if (screenHeight <= 657 && screenWidth <= 1024) {
        return this.isFullFlag ? "-14.5rem" : "-16.5rem";
      } else if (screenWidth <= 1550 && screenHeight <= 965) {
        return this.isFullFlag ? "-17.5rem" : "-6.2rem";
      } else if (screenWidth <= 1745 && screenHeight >= 850) {
        return this.isFullFlag ? "2.5rem" : "-4.5rem";
      } else if (screenWidth >= 10366 && screenHeight <= 768) {
        return this.isFullFlag ? "-18.5rem" : "-17.5rem";
      } else {
        // 其他分辨率保持原样
        return this.isFullFlag ? "6.5rem" : "3.8rem";
      }
    },
    getAccount() {
      const account = Cookies.get("account");
      this.account = account;
    },
    scaleAll() {
      //窗口尺寸变化时，重新计算和缩放
      var browerWidth = window.innerWidth;
      var browerHeight = window.innerHeight;
      let zoomValue = 0;
      let zoomValue1 = 0;
      var flag = this.isFullFlag;
      if (document.fullscreenElement) {
        zoomValue = browerWidth / 1640;
        zoomValue1 = browerHeight / 880; //缩放比例计算
        this.fullText = this.$t("scada.ExitFull");
      } else {
        zoomValue = browerWidth / this.baseWidth;
        zoomValue1 = browerHeight / this.baseHeight; //缩放比例计算
        this.fullText = this.$t("scada.FullScreen");
      }
      document.getElementById("svgDiv").style.transform =
        "scale(" + zoomValue + "," + zoomValue1 + ")";
    },
    checkRoute(currentPath) {
      // console.log("currentPath", currentPath);
      const isTargetRoute = currentPath.path.includes("/scadajcd");
      // this.WsUrl = `http://${window.location.origin}:${currentPath.query.port} `;
      this.WsUrl = `ws://192.168.150.198:${currentPath.query.port} `;
      if (isTargetRoute && !this.isScadaRoute) {
        this.startAutoRefresh();
        this.isScadaRoute = true;
      } else if (!isTargetRoute && this.isScadaRoute) {
        this.stopAutoRefresh();
        this.isScadaRoute = false;
      }
    },
    startAutoRefresh() {
      this.clearRefreshTimer();
      // 保存当前路由信息到sessionStorage
      this.refreshTimer = setTimeout(() => {
        // 强制刷新
        window.location.reload(true);
      }, 3600 * 1000);

      console.log("自动刷新已启动，将在1小时后刷新页面");
    },
    stopAutoRefresh() {
      this.clearRefreshTimer();
    },
    clearRefreshTimer() {
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }
    },

    childParent() {
      this.changeAbnormal(
        moment().format("YYYY-MM-DD") + " 00:00:00",
        "startTime"
      );
      this.changeAbnormal(new Date(), "endTime");
      this.changeAbnormal("", "SearchCriteria");
      this.AbnormalAlarmInfo.clickGroup === "设备报警";
      // this.abnormalAlarm()
    },
    exitFullScreen() {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitCancelFullScreen) {
        document.webkitCancelFullScreen();
      }
      this.fullText = this.$t("scada.FullScreen");
      this.isFullFlag = false; // 退出全屏时设置为 false
    },
    getSvg() {
      //页面初始化
      var dom = document.getElementsByClassName("app-main")[0];
      //var w1 = dom.style.width	//此api只能获取到内联样式的属性值
      //var w2 = dom.currentStyle.width  //此api虽然支持全部三种设置样式的方式，但是只支持IE
      let mac = uuidv4();
      //this.WsUrl = this.WsUrl + mac.replace(/-/g, "")
      this.initWebSocket();
      // 开启心跳监测
    },
    // 初始化函数
    async createInnerRectangles() {
      const innerGroup = document.getElementById("innerGroup");
      if (!innerGroup) {
        console.error("找不到 innerGroup 元素");
        return;
      }

      // 清空现有内容
      innerGroup.innerHTML = "";
      const startNumber = this.isOne ? 0 : Math.floor(this.CarNum / 2) + 1;
      const endNumber = this.CarNum;
      const totalRects = endNumber - startNumber + 1;
      const totalDuration = this.innerAnimationDuration;
      const gapBetweenRects = totalDuration / totalRects;

      const promises = [];
      for (let i = startNumber; i <= endNumber; i++) {
        const rect = this.addRectCar(i, "2");
        const animateMotion = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "animateMotion"
        );
        animateMotion.setAttribute("dur", `${totalDuration}s`);
        animateMotion.setAttribute("repeatCount", "indefinite");
        animateMotion.setAttribute("rotate", "auto");
        animateMotion.setAttribute("fill", "freeze");
        // 添加 begin="indefinite" 使动画不自动开始
        animateMotion.setAttribute("begin", "indefinite");
        const delay = i * gapBetweenRects;
        // 注释掉延迟开始时间
        // animateMotion.setAttribute("begin", `-${delay}s`);

        const mpath = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "mpath"
        );
        mpath.setAttributeNS(
          "http://www.w3.org/1999/xlink",
          "href",
          "#innerPath"
        );

        animateMotion.appendChild(mpath);
        rect.appendChild(animateMotion);
        innerGroup.appendChild(rect);
        promises.push(Promise.resolve());
      }
      // await Promise.all(promises);
    },

    async createOuterRectangles() {
      const outerGroup = document.getElementById("outerGroup");
      if (!outerGroup) {
        console.error("找不到 outerGroup 元素");
        return;
      }
      // 清空现有内容
      outerGroup.innerHTML = "";
      const startNumber = 1;
      const endNumber = this.isOne ? this.CarNum : Math.floor(this.CarNum / 2); // this.CarNum / 2  = 311 内层格子数 311

      const totalRects = endNumber - startNumber + 1;
      const totalDuration = this.outerAnimationDuration;
      const gapBetweenRects = totalDuration / totalRects;

      const promises = [];
      for (let i = startNumber; i <= endNumber; i++) {
        // console.log("变色--外层-i", i);
        const rect = this.addRectCar(i, "1");
        const animateMotion = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "animateMotion"
        );
        animateMotion.setAttribute("dur", `${totalDuration}s`);
        animateMotion.setAttribute("repeatCount", "indefinite");
        animateMotion.setAttribute("rotate", "auto");
        animateMotion.setAttribute("fill", "freeze");
        // 添加 begin="indefinite" 使动画不自动开始
        animateMotion.setAttribute("begin", "indefinite");

        const delay = i * gapBetweenRects;
        // 注释掉延迟开始时间
        // animateMotion.setAttribute("begin", `-${delay}s`);

        const mpath = document.createElementNS(
          "http://www.w3.org/2000/svg",
          "mpath"
        );
        mpath.setAttributeNS(
          "http://www.w3.org/1999/xlink",
          "href",
          "#outerPath"
        );

        animateMotion.appendChild(mpath);
        rect.appendChild(animateMotion);
        outerGroup.appendChild(rect);
        promises.push(Promise.resolve());
      }
      await Promise.all(promises);
    },
    addRectCar(id, type) {
      // 创建一个组合元素
      var g = document.createElementNS("http://www.w3.org/2000/svg", "g");
      // 创建矩形
      var rect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
      let idStr = "car" + this.checkCar(id, type);
      const isLastInnerGridCell = id === this.CarNum && type === "2";
      const isFirstInnerGridCell =
        id === (this.isOne ? this.CarNum : Math.floor(this.CarNum / 2) + 1) &&
        type === "2";
      rect.setAttribute("id", idStr);
      rect.setAttribute("class", "dot-red");
      rect.setAttribute("x", "-2");
      rect.setAttribute("y", "-10");
      rect.setAttribute("width", this.cellSize + 13);
      rect.setAttribute("height", "12");
      rect.setAttribute("rx", "1");
      if (isLastInnerGridCell) {
        // rect.setAttribute("x", "-6");
        rect.setAttribute("width", this.cellSize + 13);
      } else if (isFirstInnerGridCell) {
        rect.setAttribute("x", "+20");
        rect.setAttribute("width", this.cellSize + 13);
      }
      rect.setAttribute("fill", "#979797");
      rect.setAttribute("stroke", "#fff");
      rect.setAttribute("stroke-width", "1");

      // 创建文本元素
      // var text = document.createElementNS("http://www.w3.org/2000/svg", "text");
      // text.setAttribute("x", "0");
      // text.setAttribute("y", "0");
      // text.setAttribute("fill", "#ffffff");
      // text.setAttribute("font-size", "8");
      // text.setAttribute("text-anchor", "middle");
      // text.setAttribute("alignment-baseline", "middle");
      // text.textContent = id;

      // 将矩形和文本添加到组合元素中
      g.appendChild(rect);
      // g.appendChild(text);

      return g;
    },
    handleRotationChange(animations, isClockwise, color) {
      // 1下2上
      const totalRects =
        color === "inner"
          ? this.isOne
            ? this.CarNum
            : Math.floor(this.CarNum / 2) + 1
          : this.isOne
          ? this.CarNum
          : Math.floor(this.CarNum / 2); // 根据内外层选择正确的格子数
      const totalDuration = 60;
      const gapBetweenRects = totalDuration / totalRects;

      animations.forEach((animation, index) => {
        const currentTime = animation.getCurrentTime() || 0;
        const delay = index * gapBetweenRects;
        const newAnimation = animation.cloneNode(true);
        const rect = animation.parentElement;

        // 获取原始ID并转换
        const originalId = rect.getAttribute("id");
        if (originalId) {
          const carNumber = parseInt(originalId.replace("car", ""));
          rect.setAttribute("id", `car${carNumber}`);
        }

        newAnimation.setAttribute("dur", `${totalDuration}s`);
        newAnimation.setAttribute("repeatCount", "indefinite");
        newAnimation.setAttribute("begin", `-${currentTime + delay}s`);

        // 添加keyPoints来控制动画方向
        if (isClockwise) {
          newAnimation.setAttribute("keyPoints", "0;1");
        } else {
          newAnimation.setAttribute("keyPoints", "1;0");
        }
        newAnimation.setAttribute("keyTimes", "0;1");
        newAnimation.setAttribute("calcMode", "linear");

        const mpath = newAnimation.querySelector("mpath");
        mpath.setAttributeNS(
          "http://www.w3.org/1999/xlink",
          "href",
          color === "inner" ? "#innerPath" : "#outerPath"
        );

        rect.replaceChild(newAnimation, animation);
      });
    },

    // 暂停和继续动画
    handlePauseResume(animations, isPaused) {
      if (isPaused) {
        animations.forEach((animation) => {
          const currentTime = animation.getCurrentTime() || 0;
          animation.setAttribute("fill", "freeze");
          animation.endElement();
          animation.dataset.pauseTime = currentTime;
        });
      } else {
        const totalRects = this.isOne
          ? this.CarNum
          : Math.floor(this.CarNum / 2); // 外层总格子数
        const totalDuration = 60;
        const gapBetweenRects = totalDuration / totalRects;

        animations.forEach((animation, index) => {
          const delay = index * gapBetweenRects;
          const newAnimation = animation.cloneNode(true);
          const rect = animation.parentElement;

          newAnimation.setAttribute("dur", `${totalDuration}s`);
          newAnimation.setAttribute("repeatCount", "indefinite");
          newAnimation.setAttribute("begin", `-${delay}s`);

          rect.replaceChild(newAnimation, animation);
        });
      }
    },
    // 切换外层暂停
    toggleOuterPause() {
      this.isOuterPaused = !this.isOuterPaused;
      const outerGroup = document.getElementById("outerGroup");
      const animations = outerGroup.querySelectorAll("animateMotion");
      this.handlePauseResume(animations, this.isOuterPaused);
    },
    // 切换内层旋转方向
    toggleInnerRotation() {
      // this.isInnerClockwise = !this.isInnerClockwise;
      const innerGroup = document.getElementById("innerGroup");
      const animations = innerGroup.querySelectorAll("animateMotion");
      this.handleRotationChange(animations, this.isInnerClockwise, "inner");
    },
    // 切换外层旋转方向
    toggleOuterRotation() {
      // this.isOuterClockwise = !this.isOuterClockwise;
      const outerGroup = document.getElementById("outerGroup");
      const animations = outerGroup.querySelectorAll("animateMotion");
      this.handleRotationChange(animations, this.isOuterClockwise, "outer");
    },
    // 切换内层暂停
    toggleInnerPause() {
      this.isInnerPaused = !this.isInnerPaused;
      const innerGroup = document.getElementById("innerGroup");
      const animations = innerGroup.querySelectorAll("animateMotion");
      this.handlePauseResume(animations, this.isInnerPaused);
    },
    // 判断奇偶
    isEven(type) {
      let number = parseInt(type.replace("ChuteNum", ""));
      if (!isNaN(number)) {
        return number % 2 === 0;
      }
      return false;
    },
    // 获取格子坐标
    chuteNumsCells(type) {
      if (Array.isArray(this[type])) {
        return this[type];
      }
      const startX = 124.9;
      this.testPoint = startX;
      const config = {
        even: {
          // 偶数
          getPosition: (index) => ({
            x: startX + index * (this.cellSize + 5),
            y: index * this.cellSize,
          }),
        },
        odd: {
          // 奇数
          getPosition: (index) => ({
            x: startX,
            y: index * this.cellSize,
          }),
        },
      };
      const layout = this.isEven(type) ? config.even : config.odd;
      let result = [];
      if (this[type].includes("-")) {
        const [num1, num2] = this[type]
          .split("-")
          .map((num) => parseInt(num.replace(";", "")));

        const isAscending = num1 < num2;
        const start = isAscending ? num1 : num2;
        const end = isAscending ? num2 : num1;
        const length = Math.abs(end - start) + 1;

        result = Array.from({ length }, (_, index) => ({
          ...layout.getPosition(index),
          text: isAscending ? start + index : start - index,
        }));
      } else {
        const numbers = this.processArray(this[type]);
        result = numbers.map((num, index) => ({
          ...layout.getPosition(index),
          text: num,
        }));
      }
      this[type] = result;
      return result;
    },
    // 处理数组
    processArray(arr, type) {
      let result = [];
      if (arr.length <= 0) return [];
      let elementNum = Array.from(arr);
      // 计算分号的数量
      const semicolonCount = elementNum.filter((char) => char === ";").length;
      if (semicolonCount >= 2) {
        // 如果有两个或以上分号，使用新的处理逻辑
        const numbers = arr.split(";").filter(Boolean).map(Number);
        return numbers;
      } else {
        if (elementNum[0].includes("-")) {
          // 处理-的情况
          const [start, end] = elementNum[0].split("-").map(Number);
          const length = Math.abs(end - start) + 1;
          if (start > end) {
            result = Array.from({ length }, (_, index) => start - index);
          } else {
            result = Array.from({ length }, (_, index) => start + index);
          }
        } else {
          // 原有的处理逻辑
          elementNum.forEach((element) => {
            let splitElements = element.split(";").map(Number);
            result = result.concat(splitElements);
          });
        }
      }
      return result;
    },
    // 生成供应点
    generateSupplies(startX, startY, gap, supplyNums, reverse = false) {
      if (!supplyNums) return [];
      if (supplyNums.includes("1") && this.isOne) {
        startY = 422;
      } else if (supplyNums.includes("4") && this.isOne) {
        startY = 200;
      }
      const supplies = [];
      const arr = this.processArray([supplyNums], "supply");
      if (reverse) arr.reverse();
      Array.from(arr).forEach((item, i) => {
        const x = reverse
          ? startX + (arr.length - 1 - i) * (34 + gap)
          : startX + i * (34 + gap);
        supplies.push({
          x: x,
          y: startY,
          number: item,
          centerX: x + 17,
          centerY: startY + 17,
        });
      });
      return supplies;
    },
    // 生成供应点1
    generateSupplies1() {
      return this.generateSupplies(1000, 396, 0, this.SupplysNum1);
    },
    // 生成供应点2
    generateSupplies2() {
      return this.generateSupplies(1200, 0, 0, this.SupplysNum2);
    },
    generateSupplies3() {
      return this.generateSupplies(-160, -104, 0, this.SupplysNum3, true);
    },
    generateSupplies4() {
      return this.generateSupplies(-316, 176, 0, this.SupplysNum4, true);
    },
    // 切换播放暂停
    // 按下ctrl
    handleKeyDown(event) {
      if (event.key === "Control") {
        this.isCtrlPressed = true;
      }
    },
    // 松开ctrl
    handleKeyUp(event) {
      if (event.key === "Control") {
        this.isCtrlPressed = false;
      }
    },
    // 鼠标按下
    handleMouseDown(event) {
      if (event.button === 0) {
        // 只处理左键点击
        this.isDragging = true;
        this.startX = event.clientX - this.translateX;
        this.startY = event.clientY - this.translateY;
      }
    },
    // 鼠标移动拖拽
    handleMouseMove(event) {
      if (this.isDragging) {
        const newTranslateX = event.clientX - this.startX;
        const newTranslateY = event.clientY - this.startY;

        // 限制拖拽范围在容器内
        const containerWidth = 1236;
        const containerHeight = 658;
        const maxTranslateX = containerWidth * 0.5;
        const maxTranslateY = containerHeight * 0.5;
        const minTranslateX = -containerWidth * 0.5;
        const minTranslateY = -containerHeight * 0.5;

        this.translateX = Math.max(
          minTranslateX,
          Math.min(maxTranslateX, newTranslateX)
        );
        this.translateY = Math.max(
          minTranslateY,
          Math.min(maxTranslateY, newTranslateY)
        );

        const svgPic = document.querySelector(".svgPic");
        if (svgPic) {
          svgPic.style.transform = `scale(${this.scale}) translate(${this.translateX}px, ${this.translateY}px)`;
        }
      }
    },
    // 鼠标松开
    handleMouseUp() {
      this.isDragging = false;
    },
    // 滚轮缩放
    handleWheel(event) {
      event.preventDefault();

      const delta = event.deltaY > 0 ? -0.1 : 0.1;
      const newScale = Math.max(
        this.minScale,
        Math.min(this.maxScale, this.scale + delta)
      );

      if (newScale !== this.scale) {
        // 获取容器的固定尺寸和中心点
        const containerWidth = 1236;
        const containerHeight = 658;
        const centerX = containerWidth / 2;
        const centerY = containerHeight / 2;

        // 获取鼠标相对于容器的位置
        const rect = event.currentTarget.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        // 计算缩放比例
        const scaleRatio = newScale / this.scale;

        // 以鼠标位置为中心进行缩放
        this.translateX = mouseX - (mouseX - this.translateX) * scaleRatio;
        this.translateY = mouseY - (mouseY - this.translateY) * scaleRatio;

        // 限制平移范围，确保内容不会完全移出容器
        const maxTranslateX = containerWidth * 0.5;
        const maxTranslateY = containerHeight * 0.5;
        const minTranslateX = -containerWidth * 0.5;
        const minTranslateY = -containerHeight * 0.5;

        this.translateX = Math.max(
          minTranslateX,
          Math.min(maxTranslateX, this.translateX)
        );
        this.translateY = Math.max(
          minTranslateY,
          Math.min(maxTranslateY, this.translateY)
        );

        this.scale = newScale;
      }
    },
    checkDevice() {
      this.isMobile = window.innerWidth <= 768;
    },
    carClick(ss) {},
    //格口点击
    chuteClick(id) {
      this.cellNum = id.toString().replace("chute", "");
      this.chuteUnLockVisible = true;
    },
    //小车操作
    carOperate() {
      if (document.fullscreenElement) {
        this.requestFullScreen();
      }
      this.carVisible = true;
    },
    //禁用列表
    forbiddenList() {
      if (document.fullscreenElement) {
        this.requestFullScreen();
      }
      this.forbiddenVisible = true;
    },
    //异常报警
    abnormalAlarm() {
      this.abnormalAlarmVisible = true;
      // this.AbnormalAlarmInfo.loading = true
      if (document.fullscreenElement) {
        this.requestFullScreen();
      }
      // let alarmType =  this.AbnormalAlarmInfo.clickGroup === '业务报警' ? 2 : 1
      return request({
        url: "/log/alarm/list",
        method: "post",
        data: {
          // alarmType: alarmType,
          dbCode: this.$route.query.dbCode,

          alarmSource:
            this.AbnormalAlarmInfo.logSource == " "
              ? "null"
              : this.AbnormalAlarmInfo.logSource,
          message: this.AbnormalAlarmInfo.SearchCriteria
            ? this.AbnormalAlarmInfo.SearchCriteria
            : "",
          ...this.AbnormalAlarmTime,
          curPage: this.AbnormalAlarmInfo.currentPage,
          pageSize: this.AbnormalAlarmInfo.pageSize,
        },
      })
        .then((response) => {
          // this.AbnormalAlarmInfo.loading = false

          let str = `201#0#${this.account}\r\n`; // 报警已读开启
          this.websocketsend(str);
          this.abnormalList = response.data.result.records;
          this.AbnormalAlarmInfo.totalResult = response.data.result.total || 0;
        })
        .catch((error) => {
          this.$message({
            message: error,
            type: "error",
            duration: 2 * 1000,
          });
        });
    },
    //一键锁格
    chuteLockAllBtn() {
      if (document.fullscreenElement) {
        this.requestFullScreen();
      }
      this.form.chuteLockPwd = "";
      this.chuteLockVisible = true;
    },
    //一键解锁
    chuteUnLockAllBtn() {
      if (document.fullscreenElement) {
        this.requestFullScreen();
      }
      this.form.chuteUnLockPwd = "";
      this.chuteUnLockVisible = true;
    },
    // 关闭解锁格口
    closeChuteUnLock() {
      this.chuteUnLockVisible = false;
      this.cellNum = "";
    },
    // 异常报警关闭
    abnormalClose() {
      this.childParent();
      this.abnormalAlarmVisible = false;
      let str = `201#1#${this.account}\r\n`; // 报警已读关闭
      this.websocketsend(str);
      // this.AbnormalAlarmInfo.messageNum = 0
    },
    handlePageChange({ currentPage, pageSize }) {
      this.AbnormalAlarmInfo.currentPage = currentPage;
      this.AbnormalAlarmInfo.pageSize = pageSize;
      this.abnormalAlarm();
    },
    //清除公里数dialog框
    clearTheKilometersDialog() {
      if (this.form.chuteLockPwd == "000000") {
        let str = `363#${this.form.selectUpOrDownVal}\r\n`;
        // console.log("str-----", str);
        this.websocketsend(str);
        this.clearTheKilometersVisible = false;
      } else {
        Message({
          message: "请输入正确密码！",
          type: "error",
          duration: 2 * 1000,
        });
      }
    },
    // 子调父
    childParent() {
      this.changeAbnormal(
        moment().format("YYYY-MM-DD") + " 00:00:00",
        "startTime"
      );
      this.changeAbnormal(new Date(), "endTime");
      this.changeAbnormal("", "SearchCriteria");
      this.AbnormalAlarmInfo.clickGroup === "设备报警";
      // this.abnormalAlarm()
    },
    // 故障级别选项
    changeFaultLevelValueList(val, i) {
      this.AbnormalAlarmInfo.FaultLevelValueList[i] = val;
    },
    // 预警按钮选择时间
    changeAbnormal(e, type) {
      const time = moment(e).format("YYYY-MM-DD HH:mm:ss");
      this.AbnormalAlarmTime[type] = time.trim();
    },
    changeSelectVal(e) {
      this.form.selectUpOrDownVal = e;
    },
    // 子组件调父组件
    postSuccess(val) {
      if (val) {
        this.abnormalAlarm();
      }
    },
    // 清除公里数
    clearTheKilometers() {
      if (document.fullscreenElement) {
        this.requestFullScreen();
      }
      this.form.chuteLockPwd = "";
      this.clearTheKilometersVisible = true;
    },
    //调试显示日志
    debugBtn() {
      this.isLog = !this.isLog;
    },
    //小车锁定
    carLock() {
      var carNo = this.form.name;
      let str = "";
      var carStr = carNo;
      if (carStr.includes("*")) {
        carNo = carStr.replace("*", "");
        carNo = carNo - 0;
        if (carNo && carNo > 0 && carNo <= this.CarNum) {
          //carNo = this.checkCar(carNo)
          if (this.form.number == "1") {
            str = "352#" + carStr + "#1\r\n";
          } else {
            str = "353#" + carStr + "#1\r\n";
          }
          this.websocketsend(str);
        } else {
          Message({
            message: "请输入正确小车号！",
            type: "warning",
            duration: 2 * 1000,
          });
        }
      } else {
        carNo = carNo - 0;
        if (carNo && carNo > 0 && carNo <= this.CarNum / 2 && this.isTwo) {
          //carNo = this.checkCar(carNo)
          if (this.form.number == "1") {
            str = "352#" + carStr + "#1\r\n";
          } else {
            str = "353#" + carStr + "#1\r\n";
          }
          this.websocketsend(str);
        } else if (carNo && carNo > 0 && carNo <= this.CarNum && this.isOne) {
          //carNo = this.checkCar(carNo)
          if (this.form.number == "1") {
            str = "352#" + carStr + "#1\r\n";
          } else {
            str = "353#" + carStr + "#1\r\n";
          }
          this.websocketsend(str);
        } else {
          Message({
            message: "请输入正确小车号！",
            type: "warning",
            duration: 2 * 1000,
          });
        }
      }
    },
    //小车解锁
    carUnLock() {
      var carNo = this.form.name;
      var carStr = carNo;
      if (carStr.includes("*")) {
        carNo = carStr.replace("*", "");
        carNo = carNo - 0;
        if (carNo && carNo > 0 && carNo <= this.CarNum) {
          //carNo = this.checkCar(carNo)
          let str = "";
          if (this.form.number == "1") {
            str = "352#" + carStr + "#0\r\n";
          } else {
            str = "353#" + carStr + "#0\r\n";
          }
          this.websocketsend(str);
        } else {
          Message({
            message: "请输入正确小车号！",
            type: "warning",
            duration: 2 * 1000,
          });
        }
      } else {
        carNo = carNo - 0;
        if (carNo && carNo > 0 && carNo <= this.CarNum / 2 && this.isTwo) {
          //carNo = this.checkCar(carNo)
          let str = "";
          if (this.form.number == "1") {
            str = "352#" + carStr + "#0\r\n";
          } else {
            str = "353#" + carStr + "#0\r\n";
          }
          this.websocketsend(str);
        } else if (carNo && carNo > 0 && carNo <= this.CarNum && this.isOne) {
          //carNo = this.checkCar(carNo)
          let str = "";
          if (this.form.number == "1") {
            str = "352#" + carStr + "#0\r\n";
          } else {
            str = "353#" + carStr + "#0\r\n";
          }
          this.websocketsend(str);
        } else {
          Message({
            message: "请输入正确小车号！",
            type: "warning",
            duration: 2 * 1000,
          });
        }
      }
    },
    //小车正转
    carForeward() {
      var carNo = this.form.name;
      var carStr = carNo;
      if (carStr.includes("*")) {
        carNo = carStr.replace("*", "");
      }
      if (carNo && carNo > 0 && carNo <= this.CarNum) {
        //carNo = this.checkCar(carNo)
        let str = "";
        if (this.form.number == "1") {
          str = "355#" + carStr + "#2#1\r\n";
        } else {
          str = "355#" + carStr + "#1#1\r\n";
        }
        this.websocketsend(str);
      } else {
        Message({
          message: "请输入正确小车号！",
          type: "warning",
          duration: 2 * 1000,
        });
      }
    },
    //小车反转
    carReversal() {
      var carNo = this.form.name;
      var carStr = carNo;
      if (carStr.includes("*")) {
        carNo = carStr.replace("*", "");
      }
      if (carNo && carNo > 0 && carNo <= this.CarNum) {
        //carNo = this.checkCar(carNo)
        let str = "";
        if (this.form.number == "1") {
          str = "355#" + carStr + "#2#2\r\n";
        } else {
          str = "355#" + carStr + "#1#2\r\n";
        }
        this.websocketsend(str);
      } else {
        Message({
          message: "请输入正确小车号！",
          type: "warning",
          duration: 2 * 1000,
        });
      }
    },
    //手动切换
    plcHand(type) {
      let str = "360#" + type + "\r\n";
      this.websocketsend(str);
    },
    //PLC小车号
    plcCarNo() {
      var carNo = this.form.name;
      var carStr = carNo;
      if (carStr.includes("*")) {
        carNo = carStr.replace("*", "");
      }
      carNo = carNo - 0;
      if (carNo && carNo - 0 > 0 && carNo - 0 <= this.CarNum) {
        let str = "";
        if (this.form.number == "1") {
          str = "361#" + carStr + "#" + this.upNo + "\r\n";
        } else {
          str = "361#" + carStr + "#" + this.downNo + "\r\n";
        }
        this.websocketsend(str);
      } else {
        Message({
          message: "请输入正确小车号！",
          type: "warning",
          duration: 2 * 1000,
        });
      }
    },
    plcRun() {
      let str = "362#1\r\n";
      this.websocketsend(str);
    },
    checkCar(carID, layer) {
      let retCarNum;
      switch (this.carCountState == 1) {
        case 1: //无锡
          carID = carID - 0;
          let carNo;
          if (layer == "1") {
            //上层
            if (carID > 300 && carID <= 311) {
              carNo = carID - 300 + 11 + 600;
            } else {
              carNo = carID + (parseInt((carID - 1) / 30) + 1) * 30;
            }
          } else {
            carID = carID - this.CarNum / 2;
            //下层
            if (carID > 300 && carID <= 311) {
              carNo = carID - 300 + 600;
            } else {
              carNo = carID + parseInt((carID - 1) / 30) * 30;
            }
          }
          retCarNum = carNo;
          break;
        default:
          retCarNum = carID;
          break;
      }
      return retCarNum;
    },
    reverseCheckCar(carNo) {
      carNo = carNo - 0;
      let carID, layer;
      if (carNo > 630 && carNo <= 660) {
        layer = "1";
        carID = carNo - (parseInt((carNo - 631) / 30) + 1) * 30;
      } else {
        layer = "2";
        carID = carNo - parseInt((carNo - 601) / 30) * 30;
      }
      if (carID >= 612 && carID <= 623) {
        carID = carID + 300 - 11 - 600;
      } else if (carID >= 601 && carID <= 611) {
        carID = carID + 300 - 600;
      }
      return { carID, layer };
    },
    layChange(val) {
      this.$nextTick(() => {
        this.form.number = val;
      });
    },
    //一键解锁
    chuteUnLockAll() {
      if (this.form.chuteUnLockPwd == "000000") {
        let str = "354#0\r\n";
        if (this.cellNum) {
          str =
            "351#" + this.cellNum.toString().replace("chute", "") + "#0\r\n";
        }
        this.websocketsend(str);
        this.cellNum = "";
        // 清空密码输入框
        this.form.chuteUnLockPwd = "";
        this.cellNum = "";

        this.chuteUnLockVisible = false;
      } else {
        Message({
          message: "请输入正确密码！",
          type: "error",
          duration: 2 * 1000,
        });
      }
    },
    //一键锁格
    chuteLockAll() {
      if (this.form.chuteLockPwd == "000000") {
        let str = "354#1\r\n";
        this.websocketsend(str);
        this.chuteLockVisible = false;
      } else {
        Message({
          message: "请输入正确密码！",
          type: "error",
          duration: 2 * 1000,
        });
      }
    },
    onKeyDown(event) {
      if (event.keyCode === 27 || event.keyCode === 122) {
        // // ESC键或F11键被按下
        // if (document.fullscreenElement) {
        //    // 如果当前处于全屏模式，退出全屏
        //    document.exitFullscreen();
        //    this.isFullFlag = false
        // } else {
        //    // 否则进入全屏模式
        //    const element = document.documentElement;
        //    if (element.requestFullscreen) {
        //       element.requestFullscreen();
        //    } else if (element.webkitRequestFullscreen) {
        //       element.webkitRequestFullscreen();
        //    } else if (element.mozRequestFullScreen) {
        //       element.mozRequestFullScreen();
        //    } else if (element.msRequestFullscreen) {
        //       element.msRequestFullscreen();
        //    }
        //    this.isFullFlag = true
        // }
      }
    },
    //全屏显示
    requestFullScreen() {
      // 简单的F11效果：切换整个文档的全屏状态
      if (!document.fullscreenElement) {
        // 进入全屏
        if (document.documentElement.requestFullscreen) {
          document.documentElement.requestFullscreen();
        } else if (document.documentElement.msRequestFullscreen) {
          document.documentElement.msRequestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) {
          document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.webkitRequestFullscreen) {
          document.documentElement.webkitRequestFullscreen();
        }
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        }
      }
    },
    //小车运行
    carRun(type, num) {
      // 设置旋转方向
      this.isOuterClockwise = false;
      this.isInnerClockwise = false;
      if (type == "1") {
        this.toggleInnerRotation();
        this.isInnerPaused = false;
      }
      if (type == "2") {
        this.toggleOuterRotation();
        this.isOuterPaused = false;
      }
      // 更新暂停状态
    },
    carStop(type, num) {
      if (type == "1") {
        const outerAnimations = document.querySelectorAll(
          "#innerGroup animateMotion"
        );
        // 暂停所有动画
        outerAnimations.forEach((animation) => {
          animation.setAttribute("begin", "indefinite");
          animation.endElement();
        });
        this.isOuterPaused = true;
      }
      if (type == "2") {
        const innerAnimations = document.querySelectorAll(
          "#outerGroup animateMotion"
        );
        innerAnimations.forEach((animation) => {
          animation.setAttribute("begin", "indefinite");
          animation.endElement();
        });
        this.isInnerPaused = true;
      }
    },
    //变色通用方法
    changeColor(id, type) {
      let dom = document.getElementById(id);
      if (dom) {
        switch (type) {
          case "0": //待通信
            dom.setAttribute("fill", "#979797");
            break;
          case "1": //解锁
            dom.setAttribute("fill", "#5ECB80");
            break;
          case "2": //锁定  "#008000"
            dom.setAttribute("fill", "#ff0000");
            break;
          case "3": //满包  #8b008b
            dom.setAttribute("fill", "#8C008B");
            break;
          case "4": //异常口 #000000
            dom.setAttribute("fill", "#262626");
            break;
          case "5": //拦截件 #0101B5
            dom.setAttribute("fill", "#0101B5");
            break;
          case "6": //载货中 #ff8c00
            dom.setAttribute("fill", "#FF9500");
            break;
          case "7": //超最大循环  #3c9293
            dom.setAttribute("fill", "#FFD222");
            break;
          case "8": //取消件  #4c4294
            dom.setAttribute("fill", "#4A428F");
            break;
          case "9": //未配置三段码格口 #5c0005
            dom.setAttribute("fill", "#82CFFF");
            break;
          case "10": //未获取三段码信息 #6c5596
            dom.setAttribute("fill", "#695592");
            break;
          default:
            break;
        }
      }
    },
    //socket链接
    initWebSocket() {
      if (typeof WebSocket === "undefined")
        return console.log("您的浏览器不支持websocket");
      //this.websock && this.websock.close()
      console.log(this.WsUrl, "url地址");
      this.websock = new WebSocket(this.WsUrl);
      this.websock.onmessage = this.websocketonmessage;
      this.websock.onopen = this.websocketonopen;
      this.websock.onerror = this.websocketonerror; //this.handleReconnectWs//
      this.websock.onclose = this.websocketclose;
    },
    websocketonopen() {
      console.log("建立链接" + this.WsUrl);
      this.webSocketRunText = this.$t("scada.Connected");
      this.recprdsSocketSendUserInfo(this.account);
      // this.startHeartbeat();
    },
    recprdsSocketSendUserInfo(Data) {
      let str = `209#${Data}\n\r`;
      this.websock.send(str);
    },

    startHeartbeat() {
      if (this.ws_heart) {
        clearInterval(this.ws_heart);
      }
      this.ws_heart = setInterval(() => {
        this.websock.send("ping");
      }, 30000);
    },
    websocketonerror() {
      console.log("断线重连");
      // this.changeColor("run2", "2");
      // this.webSocketRunText = this.$t("scada.NotConnected");
      // this.carStop('1')
      // this.carStop('2')
      this.reconnect();
    },
    websocketonmessage(e) {
      if (this.isLog) {
        console.log("接收消息：" + e.data);
      }
      this.ws_heart = true;
      if (e.data) {
        // let dateList = ""
        // if (e.data.includes("326#{") || e.data.includes("327#{")) {
        //    dateList = e.data
        // }else{
        //    dateList = e.data.split('|')
        // }
        let dateList = e.data.split("|");
        dateList.forEach((element) => {
          if (element) {
            //数据接收
            const strArr = element.split("#");
            // console.log("strArr-----------", strrr)
            const _socketTag = strArr[0];
            // console.log("_socketTag-----------", _socketTag)
            switch (_socketTag) {
              case "300":
                this.SocketMsg300(strArr);
                break;
              case "301":
                this.SocketMsg301(strArr);
                break;
              case "303":
                this.SocketMsg303(strArr);
                break;
              case "304":
                this.SocketMsg304(strArr);
                break;
              case "305":
                this.SocketMsg305(strArr);
                break;
              case "306":
                this.SocketMsg306(strArr);
                break;
              case "307":
                this.SocketMsg307(strArr);
                break;
              case "308":
                this.SocketMsg308(strArr);
                break;
              case "309":
                this.SocketMsg309(strArr);
                break;
              case "310":
                this.SocketMsg310(strArr);
                break;
              case "311":
                this.SocketMsg311(strArr);
                break;
              case "312":
                this.SocketMsg312(strArr);
                break;
              case "313":
                this.SocketMsg313(strArr);
                break;
              case "314":
                this.SocketMsg314(strArr);
                break;
              case "315":
                this.SocketMsg315(strArr);
                break;
              case "316":
                this.SocketMsg316(strArr);
                break;
              case "317":
                this.SocketMsg317(strArr);
                break;
              case "318":
                this.SocketMsg318(strArr);
                break;
              case "319":
                this.SocketMsg319(strArr);
                break;
              case "320":
                this.SocketMsg320(strArr);
                break;
              case "321":
                this.SocketMsg321(strArr);
                break;
              case "322":
                this.SocketMsg322(strArr);
                break;
              case "323":
                this.SocketMsg323(strArr);
                break;
              case "324":
                this.SocketMsg324(strArr);
                break;
              case "325":
                this.SocketMsg325(strArr);
                break;
              case "326":
                this.SocketMsg326(strArr);
                break;
              case "327":
                this.SocketMsg327(strArr);
                break;
              case "328":
                this.SocketMsg328(strArr);
                break;
              case "329":
                this.SocketMsg329(strArr);
                break;
              case "330":
                this.SocketMsg330(strArr);
                break;
              case "331":
                this.SocketMsg331(strArr);
                break;
              case "332":
                this.SocketMsg332(strArr);
                break;
              case "333":
                this.SocketMsg333(strArr);
                break;
              case "334":
                this.SocketMsg334(strArr);
                break;
              case "335":
                this.SocketMsg335(strArr);
                break;
              case "336":
                this.SocketMsg336(strArr);
                break;
              case "337":
                this.SocketMsg337(strArr);
                break;
              case "401":
                this.SocketMsg401(strArr);
                break;
              case "403":
                this.SocketMsg403(strArr);
                break;
              case "999":
                this.SocketMsg999(strArr);
                break;
              default:
                break;
            }
          }
        });
      }
    },
    websocketsend(Data) {
      console.log("data", Data);
      //数据发送
      if (this.isLog) {
        console.log("数据发送", Data);
      }
      console.log(
        "this.websock.readyState == WebSocket.OPEN",
        this.websock.readyState == WebSocket.OPEN
      );
      if (this.websock.readyState == WebSocket.OPEN) {
        if (this.isMainIp) {
          // 白名单
          if (this.wcsRunText == this.$t("scada.Connected")) {
            this.websock.send(Data);
            this.carVisible = false;
            this.forbiddenVisible = false;
            this.chuteUnLockVisible = false;
            this.chuteLockVisible = false;
            // this.abnormalAlarmVisible = false
            if (this.abnormalAlarmVisible || !this.abnormalAlarmVisible) {
              console.log(" this.showOverlay = true", this.showOverlay);
            } else {
              this.showOverlay = true;
            }
            setInterval(() => {
              /**
               * 自己定义的数据或者和后端约定一个固定的数据
               */
              if (this.showOverlay) {
                this.showOverlay = false;
                Message({
                  message: this.$t("common.OperationFailed"),
                  type: "error",
                  duration: 2 * 1000,
                });
              }
            }, 15000);
          } else {
            Message({
              message: "WCS 连接没有建立成功！",
              type: "error",
              duration: 2 * 1000,
            });
          }
        } else {
          Message({
            message: "ip不在白名单中！",
            type: "error",
            duration: 2 * 1000,
          });
        }
      } else {
        //alert("WebSocket 连接没有建立成功！")
        Message({
          message: "WebSocket 连接没有建立成功！",
          type: "error",
          duration: 2 * 1000,
        });
      }
      //this.websock.send(Data)
    },
    websocketclose(e) {
      //关闭
      console.log("断开链接", e);
      this.reconnect();
      // Message({
      //   message: "WebSocket 连接没有建立成功！",
      //   type: "error",
      //   duration: 2 * 1000,
      // });
      // this.changeColor("run2", "2");
      // this.webSocketRunText = this.$t("scada.NotConnected");
      // //链接建立失败重连
      // setTimeout(() => {
      //   this.initWebSocket();
      // }, 5000);
    },
    reconnect() {
      Message({
        message: "WebSocket 连接没有建立成功！",
        type: "error",
        duration: 2 * 1000,
      });
      this.webSocketRunText = this.$t("scada.NotConnected");
      this.carStop("1");
      this.carStop("2");
      if (this.connectTimer) {
        clearTimeout(this.connectTimer);
      }
      //链接建立失败重连
      this.connectTimer = setTimeout(() => {
        this.initWebSocket();
      }, 5000);
    },
    // 重连ws
    handleReconnectWs() {
      //console.log("断开链接", e)
      Message({
        message: "WebSocket 连接没有建立成功！",
        type: "error",
        duration: 2 * 1000,
      });
      this.webSocketRunText = this.$t("scada.NotConnected");

      this.connectTimer && clearTimeout(this.connectTimer);
      this.connectTimer = setTimeout(() => {
        console.log("重连", this.websock.readyState);
        // 接已经关闭，或者打开连接失败
        if (this.websock.readyState === 3) {
          this.initWebSocket();
        }
      }, 5000); // 需求是2s...我自测用的20s
    },
    // WebSocket的心跳监测，20s进行一次数据的发送，查看是否断开连接(需求是2s...我自测用的20s)
    handleHeartListen() {
      //clearInterval(this.ws_heart)
      // this.ws_heart = setInterval(() => {
      //    /**
      //    * 自己定义的数据或者和后端约定一个固定的数据
      //   */
      //    this.websock.send('999#0\r\n');
      //    console.log("心跳发送 999#0")
      // }, 20000)
      setInterval(() => {
        /**
         * 自己定义的数据或者和后端约定一个固定的数据
         */
        if (!this.ws_heart) {
          this.handleOptionChange();
        }
        this.ws_heart = false;
      }, 20000);
    },
    SocketMsg300(strArr) {},
    SocketMsg301(strArr) {
      let _socketNo = strArr[1];
      let _socketFlag = strArr[2];
      let _socketLayer = strArr[3];
      if (_socketLayer == this.downNo && this.isTwo) {
        if (this.carCountState != 1) {
          _socketNo = _socketNo - 0 + this.CarNum / 2;
        }
      }
      // 小车状态
      if (_socketFlag == 1) {
        this.changeColor("car" + _socketNo, "6");
      } else {
        this.changeColor("car" + _socketNo, "1");
      }
    },
    SocketMsg303(strArr) {
      let _socketNo = strArr[1];
      this.exceedsMaxNum = _socketNo;
    },
    SocketMsg304(strArr) {
      let _socketLayer = strArr[1];
      let _socketNo = strArr[2];
      if (_socketLayer == this.downNo) {
        this.scansNumDown = _socketNo;
      } else {
        this.scansNum = _socketNo;
      }
    },
    SocketMsg305(strArr) {
      let _socketNo = strArr[1];
      this.fallingNum = _socketNo;
    },
    SocketMsg306(strArr) {
      let _socketNo = strArr[1];
      this.interceptNum = _socketNo;
    },
    SocketMsg307(strArr) {
      let _socketNo = strArr[1];
      this.hypercycleNum = _socketNo;
    },
    SocketMsg308(strArr) {
      let _socketNo = strArr[1];
      this.notConfiguredGrid = _socketNo;
    },
    SocketMsg309(strArr) {
      let _socketNo = strArr[1];
      this.abnormalNum = _socketNo;
    },
    SocketMsg310(strArr) {
      let _socketNo = strArr[1];
      this.notObtainedNum = _socketNo;
    },
    SocketMsg311(strArr) {
      let _socketNo = strArr[1];
      this.cancelNum = _socketNo;
    },
    SocketMsg312(strArr) {
      let _socketNo = strArr[1];
      let _socketFlag = strArr[2];
      if (_socketFlag == "1") {
        this.changeColor("supply" + _socketNo, "1");
        [1, 2, 3].forEach((item) => {
          this.changeColor("supply" + _socketNo + "-" + item, "1");
        });
      } else {
        this.changeColor("supply" + _socketNo, "0");
        [1, 2, 3].forEach((item) => {
          this.changeColor("supply" + _socketNo + "-" + item, "0");
        });
      }
    },
    SocketMsg313(strArr) {
      let _socketNo = strArr[1];
      let _socketFlag = strArr[2];
      let _socketLayer = strArr[3];
      if (_socketLayer == this.upNo && this.isTwo) {
        if (this.carCountState != 1) {
          _socketNo = _socketNo - 0 + this.CarNum / 2;
        }
      }
      if (_socketFlag == "1") {
        this.changeColor("car" + _socketNo, "2");
      } else {
        this.changeColor("car" + _socketNo, "1");
      }
    },
    SocketMsg314(strArr) {
      let _socketNo = strArr[1];
      let _socketFlag = strArr[2];
      this.changeColor("chute" + _socketNo, _socketFlag);
    },
    SocketMsg315(strArr) {},
    SocketMsg316(strArr) {
      // let _socketNo = strArr[1]
      // let _socketFlag = strArr[2]
      // if(_socketFlag == '0')
      // {
      //    this.changeColor("supply" + _socketNo, '1')
      // }else{
      //    this.changeColor("supply" + _socketNo, '2')
      // }
    },
    SocketMsg317(strArr) {
      let _socketNo = strArr[1];
      let _socketFlag = strArr[2];
      let _socketLayer = strArr[3];
    },
    SocketMsg318(strArr) {
      let _socketNo = strArr[2] - 0;
      let _socketLayer = strArr[1];
      if (this.Distinguish == 0) {
        if (this.isOne) {
          if (_socketLayer == this.upNo) {
            this.speedDial = _socketNo;
            if (
              _socketNo > 0 &&
              this.runningText == this.$t("scada.DeviceStopped") &&
              this.isCreateCar
            ) {
              this.changeColor("role1", "1");
              this.runningText = this.$t("scada.DeviceRunning");
              this.carRun(_socketLayer);
            }
            if (
              _socketNo == 0 &&
              this.runningText == this.$t("scada.DeviceRunning")
            ) {
              this.changeColor("role1", "2");
              this.runningText = this.$t("scada.DeviceStopped");
              this.carStop(_socketLayer);
            }
          } else {
            this.speedDialDown = _socketNo;
            if (
              _socketNo > 0 &&
              this.runningTextDown == this.$t("scada.DeviceStopped") &&
              this.isCreateCar
            ) {
              this.changeColor("role2", "1");
              this.runningTextDown = this.$t("scada.DeviceRunning");
              this.carRun(_socketLayer);
            }
            if (
              _socketNo > 0 &&
              this.runningTextDown == this.$t("scada.DeviceRunning") &&
              this.isCreateCar
            ) {
              this.changeColor("role2", "1");
              this.runningTextDown = this.$t("scada.DeviceRunning");
              this.carRun(_socketLayer);
            }
            if (
              _socketNo == 0 &&
              this.runningTextDown == this.$t("scada.DeviceRunning")
            ) {
              this.changeColor("role2", "2");
              this.runningTextDown = this.$t("scada.DeviceStopped");
              this.carStop(_socketLayer);
            }
          }
        } else {
          if (this._socketLayer == this.upNo) {
            this.speedDial = _socketNo;
          } else {
            this.speedDialDown = _socketNo;
          }
          console.log("strArr", JSON.stringify(strArr));
          if (
            _socketNo > 0 &&
            this.runningText == this.$t("scada.DeviceStopped") &&
            this.isCreateCar
          ) {
            this.changeColor("role1", "1");
            this.runningText = this.$t("scada.DeviceRunning");
            this.carRun("1");
          }
          if (
            _socketNo == 0 &&
            this.runningText == this.$t("scada.DeviceRunning")
          ) {
            this.changeColor("role1", "2");
            this.runningText = this.$t("scada.DeviceStopped");
            this.carStop("1");
          }

          if (
            _socketNo > 0 &&
            this.runningTextDown == this.$t("scada.DeviceStopped") &&
            this.isCreateCar
          ) {
            this.changeColor("role2", "1");
            this.runningTextDown = this.$t("scada.DeviceRunning");
            this.carRun("2");
          }
          if (
            _socketNo == 0 &&
            this.runningTextDown == this.$t("scada.DeviceRunning")
          ) {
            this.changeColor("role2", "2");
            this.runningTextDown = this.$t("scada.DeviceStopped");
            this.carStop("2");
          }
        }
      } else {
        if (_socketLayer == this.upNo) {
          this.speedDial = _socketNo;
          if (
            _socketNo > 0 &&
            this.runningText == this.$t("scada.DeviceStopped") &&
            this.isCreateCar
          ) {
            this.changeColor("role1", "1");
            this.runningText = this.$t("scada.DeviceRunning");
            this.carRun(_socketLayer);
          }
          if (
            _socketNo == 0 &&
            this.runningText == this.$t("scada.DeviceRunning")
          ) {
            this.changeColor("role1", "2");
            this.runningText = this.$t("scada.DeviceStopped");
            this.carStop(_socketLayer);
          }
        } else {
          this.speedDialDown = _socketNo;
          if (
            _socketNo > 0 &&
            this.runningTextDown == this.$t("scada.DeviceStopped") &&
            this.isCreateCar
          ) {
            this.changeColor("role2", "1");
            this.runningTextDown = this.$t("scada.DeviceRunning");
            this.carRun(_socketLayer);
          }
          if (
            _socketNo == 0 &&
            this.runningTextDown == this.$t("scada.DeviceRunning")
          ) {
            this.changeColor("role2", "2");
            this.runningTextDown = this.$t("scada.DeviceStopped");
            this.carStop(_socketLayer);
          }
        }
      }
      // plc状态未连接，并且上下层速度>0,显示弹窗 //this.speedDial运行速度
      if (
        this.speedDial > 0 &&
        this.UpperLevelConnectionStatus == this.$t("scada.NotConnected")
      ) {
        this.getAlert(this.$t("scada.UpperLayerPLCDisconnect"));
      } else if (
        this.speedDialDown > 0 &&
        this.LowerLevelConnectionStatus == this.$t("scada.NotConnected")
      ) {
        this.getAlert(this.$t("scada.LowerLevelPLCDisconnect"));
      }
      this.scaleAll();
    },
    SocketMsg319(strArr) {
      let _socketNo = strArr[1];
      if (_socketNo == "1") {
        this.wcsRunText = this.$t("scada.Connected");
      } else {
        this.wcsRunText = this.$t("scada.NotConnected");
      }
    },
    SocketMsg320(strArr) {
      let _socketNo = strArr[2];
      let _socketLayer = strArr[1];
      if (_socketLayer == this.downNo) {
        this.sortingMinutesCarShareDown = _socketNo;
      } else {
        this.sortingMinutesCarShare = _socketNo;
      }
    },
    SocketMsg321(strArr) {
      let _socketNo = strArr[2];
      let _socketLayer = strArr[1];
      if (_socketLayer == this.downNo) {
        // this.distanceDial = _socketNo;
        this.distanceDialDown = _socketNo;
        if (this.isTwo && this.Distinguish == 0) {
          this.distanceDial = _socketNo;
        }
      } else {
        this.distanceDial = _socketNo;
      }
    },
    SocketMsg322(strArr) {
      let _socketNo = strArr[2];
      let _socketLayer = strArr[1];
      if (_socketLayer == this.downNo) {
        this.nowStartDateDown = _socketNo;
        if (this.isTwo && this.Distinguish == 0) {
          this.nowStartDate = _socketNo;
        }
      } else {
        this.nowStartDate = _socketNo;
      }
    },
    async SocketMsg323(strArr) {
      let _socketNo = strArr[1];
      this.CarNum = _socketNo;
      try {
        // 使用Promise.all同时执行两个创建过程
        await Promise.all([
          ,
          // 创建上层小车
          this.createInnerRectangles(), // 创建下层小车
          this.isTwo && this.createOuterRectangles(),
        ]);
      } catch (error) {
        console.error("初始化格子失败:", error);
      }
      this.carRun(2);
      this.carRun(1);
      if (!this.isCreateCar) {
        this.isCreateCar = true;
        if (
          this.speedDial == 0 &&
          this.runningText == this.$t("scada.DeviceRunning")
        ) {
          this.changeColor("role1", "1");
          this.runningText = this.$t("scada.DeviceStopped");
          this.carStop(this.upNo);
        }
        if (
          this.speedDialDown > 0 &&
          this.runningTextDown == this.$t("scada.DeviceRunning")
        ) {
          this.changeColor("role2", "1");
          this.runningTextDown = this.$t("scada.DeviceStopped");
          this.carStop(this.downNo);
        }
      }
      this.scaleAll();
    },
    SocketMsg324(strArr) {
      let _socketNo = strArr[1];
      this.ChuteNum = _socketNo;
    },
    SocketMsg325(strArr) {
      let _socketNo = strArr[1];
      this.SupplysNum = _socketNo;
    },
    SocketMsg326(strArr) {
      let _socketNo = strArr[1];
      let chuteJson = JSON.parse(_socketNo);
      this.ChuteNum1 = chuteJson.ChuteNum1;
      this.ChuteNum2 = chuteJson.ChuteNum2;
      this.ChuteNum3 = chuteJson.ChuteNum3;
      this.ChuteNum4 = chuteJson.ChuteNum4;
      this.ChuteNum5 = chuteJson.ChuteNum5;
      this.ChuteNum6 = chuteJson.ChuteNum6;
      this.ChuteNum7 = chuteJson.ChuteNum7;
      this.ChuteNum8 = chuteJson.ChuteNum8;
      if (!this.isCreateChute) {
        this.isCreateChute = true;
      }
      this.scaleAll();
    },
    SocketMsg327(strArr) {
      let _socketNo = strArr[1];
      let supplyJson = JSON.parse(_socketNo);
      this.SupplysNum1 = supplyJson.SupplysNum1;
      this.SupplysNum2 = supplyJson.SupplysNum2;
      this.SupplysNum3 = supplyJson.SupplysNum3;
      this.SupplysNum4 = supplyJson.SupplysNum4;
      // "SupplysNum1": ""
      if (!this.isCreateSupplys) {
        //绘制供包台
        this.isCreateSupplys = true;
      }
      this.scaleAll();
    },
    SocketMsg328(strArr) {
      let _socketNo = strArr[1];
      this.sortingErrorNum = _socketNo;
    },
    SocketMsg329(strArr) {
      let _socketNo = strArr[2];
      let _socketLayer = strArr[1];
      this.sortingMinutesDistance = _socketNo;
    },
    SocketMsg330(strArr) {
      let _socketNo = strArr[1];
      if (_socketNo == "0") {
        this.isMainIp = true;
        // 显示操作按钮
        let unlockBtn = document.getElementById("operate-unlock");
        let lockBtn = document.getElementById("operate-lock");
        if (unlockBtn) unlockBtn.style.display = "";
        if (lockBtn) lockBtn.style.display = "";
      }
    },
    SocketMsg331(strArr) {
      let _socketNo = strArr[2];
      let _socketLayer = strArr[1];
      if (_socketLayer == this.downNo) {
        this.forbidden.downCarStr = _socketNo;
      } else {
        this.forbidden.upCarStr = _socketNo;
      }
    },
    SocketMsg332(strArr) {
      let _socketNo = strArr[1];
      if (_socketNo == "1") {
        this.carCountState = 1; //1 无锡小车号特殊处理,
      } else {
        this.carCountState = 0;
      }
    },
    SocketMsg333(strArr) {
      let _socketNo = strArr[1];
      if (_socketNo == "0") {
        this.Distinguish = 0; //0为一个PLC、1为两个PLC
        this.isOne = true;
        this.isTwo = false;
      } else if (_socketNo == "1") {
        this.Distinguish = 0; //0为一个PLC、1为两个PLC
        this.isOne = false;
        this.isTwo = true;
      } else if (_socketNo == "2") {
        this.Distinguish = 1; //0为一个PLC、1为两个PLC
        this.isOne = false;
        this.isTwo = true;
      }
    },
    SocketMsg334(strArr) {
      let _socketNo = strArr[1];
      if (_socketNo == "0") {
        this.isScadaStop = true;
      } else if (_socketNo == "1") {
        this.isScadaStop = false;
      }
    },
    SocketMsg335(strArr) {
      let msg = strArr[1];
      Message({
        message: msg,
        type: "error",
        duration: 5 * 1000,
      });
    },
    SocketMsg336(strArr) {
      let _socketNo = strArr[1];
      let _socketFlag = strArr[2];
      if (_socketFlag == "0") {
        this.changeColor("stop" + _socketNo, "1");
      } else {
        this.changeColor("stop" + _socketNo, "2");
      }
    },
    SocketMsg337(strArr) {
      let _socketNo = strArr[1]; // 1下层 2上层
      let _socketFlag = strArr[2]; // 状态 0 未连接 1 已连接
      if (_socketFlag == "0") {
        // this.speedDial > 0  plc返回值 未连接  弹窗 ，上层plc异常断开连接
        // 未连接状态
        if (_socketNo == "2") {
          this.LowerLevelConnectionStatus = this.$t("scada.NotConnected");
          if (this.speedDial > 0) {
            this.getAlert(this.$t("scada.LowerLevelPLCDisconnect"));
          }
        } else {
          this.UpperLevelConnectionStatus = this.$t("scada.NotConnected");
          if (this.speedDial > 0) {
            this.getAlert(this.$t("scada.UpperLayerPLCDisconnect"));
          }
        }
        //   this.changeColor("PLC" + _socketNo, "2");
      } else {
        // 已连接状态
        if (_socketNo == "2") {
          this.LowerLevelConnectionStatus = this.$t("scada.Connected");
          //     this.changeColor("PLC" + _socketNo, "1");
        } else {
          this.UpperLevelConnectionStatus = this.$t("scada.Connected");
          //     this.changeColor("PLC" + _socketNo, "1");
        }
      }
    },
    SocketMsg401(strArr) {
      let _socketFlag = strArr[1];
      if (_socketFlag == "0") {
        this.showOverlay = false;
        Message({
          message: this.$t("common.OperationFailed"),
          type: "error",
          duration: 2 * 1000,
        });
      } else {
        this.showOverlay = false;
        Message({
          message: this.$t("common.OperationSuccessful"),
          type: "success",
          duration: 2 * 1000,
        });
      }
    },
    SocketMsg403(strArr) {
      // 报警未读条数
      this.AbnormalAlarmInfo.messageNum = strArr[1];
    },
    SocketMsg999() {
      ///this.ws_heart = true
    },
    getBoxValue(typeCode) {
      return request({
        url: "/obtain/db/getParamValueByName",
        method: "get",
        params: { paramName: "ScadaConnInfo" },
      }).then((response) => {
        this.systemOptions = JSON.parse(response.msg);
        this.systemOptions = this.systemOptions.filter(
          (item) => item.isWcsFlag
        );
        this.getSvg();
      });
    },
    handleOptionChange() {
      let typeCode = this.systemTypeCode;
      let type = this.systemOptions.find(
        (option) => option.dbCode === typeCode
      ).dbType;
      if (type == "dws") {
        this.isWcs = false;
      } else {
        this.isWcs = true;
      }

      this.websock.close();
      (this.isCreateCar = false),
        (this.isCreateChute = false),
        (this.isCreateSupplys = false),
        (this.time1 = 0);
      this.time2 = 0;
      this.getBoxValue(typeCode);
    },
    getAlert(value) {
      // 避免重复显示相同内容的弹窗
      if (this.alertInstance && this.isPopupContent === value) {
        return; // 如果是相同内容，不重复显示
      }

      // 创建新弹窗
      this.alertInstance = this.$alert(value, {
        confirmButtonText: "Yes",
        type: "error",
      });
      this.isPopupContent = value;
    },
    // 处理全屏状态变化
    handleFullscreenChange() {
      if (
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      ) {
        // 进入全屏状态
        this.isFullFlag = true;
        this.fullText = this.$t("scada.ExitFull");
      } else {
        // 退出全屏状态
        this.isFullFlag = false;
        this.fullText = this.$t("scada.FullScreen");
      }
    },
  },

  watch: {
    // 监听路由变化
    $route(to) {
      this.checkRoute(to);
    },
  },

  destroyed() {
    //离开路由之后断开websocket连接
    this.websock.close();
  },
};
</script>


<style lang="scss" scoped>
::v-deep .el-dialog__header {
  border-bottom: 1px solid #e5e5e5 !important;
}
::v-deep .el-dialog {
  background: url("../../assets/images/scada/forewarningDialog.png") no-repeat
    center center;
  background-size: 100% 100%;
}
.status-bottom {
  position: relative; /* 改为相对定位 */
  left: auto;
  width: 100%;
  max-width: 1236px;
  flex-shrink: 0; /* 防止被压缩 */
  margin-top: 0; /* 移除自动外边距 */
}

.status-bar {
  display: flex;
  flex-wrap: wrap;
  padding: 8px 15px;
  background-color: #ffffff;
  font-family: "Microsoft YaHei", Arial, sans-serif;
  font-size: 13px;
}

.status-row {
  display: flex;
  width: 100%;
  gap: 20px;
  padding: 5px 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: fit-content;
  color: #171717;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin: 0 2px;
}

.connected {
  background-color: #52c41a;
} /* 绿色 */
.disconnected {
  background-color: #f5222d;
} /* 红色 */
.pending {
  background-color: #722ed1;
} /* 紫色 */
.warning {
  background-color: #faad14;
} /* 黄色 */
.neutral {
  background-color: #bfbfbf;
} /* 灰色 */
.active {
  background-color: #1890ff;
} /* 蓝色 */
.black {
  background-color: #262626;
} /* 黑色 */

.FaultLevelSty {
  display: flex !important;
  justify-content: space-between;
  align-items: start;
}

.fontWidth {
  font-weight: 600;
}

.AbnormalStyle {
  margin-bottom: 20px;
}
text {
  font-family: "微软雅黑";
}
.page {
  width: 1920px;
  height: 1080px;
  background-image: url("../../assets/images/scada/scadacarbg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
  box-sizing: border-box;
  padding: 20px;
}

.content-wrapper {
  display: flex;
  gap: 20px;
  height: 100vh;
}

.left-container {
  min-width: 0;
  width: 1280px; /* 略大于SVG容器，留出边距 */
  height: 1040px;
  display: flex;
  flex-direction: column; /* 垂直布局 */
  align-items: center;
  justify-content: flex-start;
  position: relative;
  padding: 15px;
  box-sizing: border-box;
  background-color: #ffffff;
}

.right-container {
  height: 1040px;
  width: 624px;
  // overflow: auto;
  // transition: all 0.3s ease;

  // 添加弹性布局
  display: flex;
  flex-direction: column;
  gap: 1rem;
  // .white-box {
  //   height: auto;
  //   min-height: 200px;
  //   max-height: calc(100vh - 2rem);
  // }

  .upper-box {
    width: 624px;
    height: 765px;
  }
}

.white-box {
  width: 100%;
  height: 100%;
  // flex: 1; /* 占据剩余空间 */
  overflow: hidden;
  position: relative;
  background-color: white;
  border-radius: 8px;
  // box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px; /* 与footer的间距 */
}

.upper-box {
  flex: 2;
  width: 98%;
  border-radius: 6px;
  margin-bottom: 1rem;
}

.lower-box {
  flex: 1;
  background-color: #ffffff;
  border-radius: 6px;
}

/* 在有样式中添加 */
rect {
  transition: fill 0.3s ease;
}

rect:hover {
  cursor: pointer;
}

/* 添加到已有样式中 */
text {
  user-select: none;
  pointer-events: none;
}

/* 圆角点位的特殊样式 */
g rect {
  stroke: white;
  stroke-width: 1;
}

g rect:hover {
  cursor: pointer;
}

/* 控制按钮容器样式 - 更新为在left-container中显示 */
.control-buttons-container {
  width: 100%;
  max-width: 1236px; /* 与SVG容器相同的最大宽度 */
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  margin: 0 auto 15px auto; /* 居中并添加下边距 */
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  pointer-events: auto;
}

.control-buttons-wrapper {
  display: flex;
  justify-content: right;
  align-items: center;
  gap: 10px; /* 增加按钮间距 */
  flex-wrap: nowrap; /* 禁止换行，保持一行显示 */
  overflow-x: auto; /* 如果内容太多，允许水平滚动 */
  padding: 10px 5px; /* 添加左右内边距 */
}

.btn-group {
  display: flex;
  gap: 6px;
  align-items: center;
  flex-shrink: 0; /* 防止按钮组被压缩 */
}

/* 自定义按钮样式 */
.control-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px; /* 稍微增加内边距 */
  border: none;
  border-radius: 6px; /* 稍微增加圆角 */
  font-size: 14px; /* 稍微增加字体大小 */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-height: 36px; /* 稍微增加最小高度 */
  flex-shrink: 0; /* 防止按钮被压缩 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加轻微阴影 */
}

.control-btn:active {
  transform: translateY(0);
}

/* 按钮类型样式 */
.primary-btn {
  background: url("../../assets/scada/btnBg.png") no-repeat center center;
  background-size: cover;
  color: white;
}

.success-btn {
  background: url("../../assets/scada/btnBg.png") no-repeat center center;
  background-size: cover;
  color: white;
}

.info-btn {
  background: url("../../assets/scada/btnBg.png") no-repeat center center;
  background-size: cover;
  color: white;
}

.warning-btn {
  background: url("../../assets/scada/btnBg.png") no-repeat center center;
  background-size: cover;
  color: white;
}

/* 按钮图标样式 */
.btn-icon {
  width: 14px;
  height: 14px;
  object-fit: contain;
  flex-shrink: 0;
}

/* 徽章样式调整 */
.control-buttons-container .badge {
  display: inline-block;
  position: relative;
}

/* 修复异常报警徽章显示问题 */
::v-deep .el-badge {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

::v-deep .el-badge__content {
  position: absolute;
  top: -10px;
  right: -15px;
  z-index: 10;
  background-color: #f56c6c;
  border-radius: 10px;
  color: #fff;
  display: inline-block;
  font-size: 12px;
  font-weight: 700;
  height: 18px;
  line-height: 18px;
  padding: 0 6px;
  text-align: center;
  white-space: nowrap;
  border: 2px solid #fff;
  box-sizing: border-box;
  min-width: 18px;
}

::v-deep .el-badge__content.is-fixed {
  position: absolute !important;
  transform: translateY(0) translateX(0);
}

.irregular-rect {
  fill: rgb(99, 99, 99);
  stroke: rgb(99, 99, 99);
  stroke-width: 2;
  stroke-dasharray: 16, 5; /* 创建短划线效果 */
  stroke-linecap: round; /* 使短划线末端变圆 */
}
.irregular-rect1 {
  stroke: rgb(255, 255, 255);
  stroke-width: 3;
  stroke-linecap: round; /* 使短划线末端变圆 */
}
.center-text {
  width: 25px;
  height: 25px;
  font-weight: bold;
  fill: white;
  font-size: 1.3rem;
  text-align: center;
  dominant-baseline: middle; /* 垂直居中 */
}

.pause-play-group:hover {
  /* 移除缩放效果 */
  transform: scale(1.1);
}

.control-rect {
  /* 检查并调整 transition 属性 */
  transition: fill 0.3s ease;
}

.svgPic {
  width: 1236px;
  height: 658px;
  margin: 0 auto; /* 左右居中 */
  overflow: hidden;
  user-select: none;
  pointer-events: auto;
  transform-origin: center center;
  position: relative; /* 确保内部元素相对定位 */
  border-radius: 4px; /* 圆角边框 */
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
}

.svgPic svg {
  width: 100%;
  height: 100%;
  pointer-events: auto;
  transform-origin: center center;
  /* 确保SVG内容完整显示，保持长宽比 */
  display: block; /* 移除默认的inline样式 */
  max-width: 100%;
  max-height: 100%;
}

#outerGroup,
#innerGroup {
  pointer-events: auto;
  visibility: visible;
}

/* 添加格子的基本样式 */
.dot-red {
  transition: all 0.3s ease;
  pointer-events: auto;
}

/* 确保路径可见 */
#outerPath,
#innerPath {
  stroke-width: 1;
  visibility: visible;
}

.btnSty {
  position: absolute;
  top: 6.5rem;
  padding: 0 20px;
  display: flex;
  align-items: center;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

.control-buttons-container {
  width: 100%;
  z-index: 1000; /* 提高按钮层级 */
  pointer-events: auto; /* 添加此行 */
}

.exit-fullscreen-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  margin: 40px 40px 0px 0px;
  z-index: 1000;
  background: url("../../assets/scada/btnBg.png") no-repeat center center !important;
  background-size: cover !important;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
}

.exit-fullscreen-btn:hover {
  opacity: 0.8;
}

// Add hardware acceleration
// .svgPic svg {
//   transform: translate3d(0, 0, 0);
//   backface-visibility: hidden;
//   perspective: 1000;
//   will-change: transform;
// }

// Add transition for smooth animation
g rect {
  transition: all 0.3s ease;
  will-change: transform;
}

// Add hardware acceleration for cells
.outer-cell,
.inner-cell {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  will-change: transform;
}

// 添加新的动画样式
.outer-rect {
  fill: #0370fa;
  opacity: 0.8;
  filter: drop-shadow(0 0 2px rgba(3, 112, 250, 0.5));
}

.inner-rect {
  fill: #0370fa;
  opacity: 0.8;
  filter: drop-shadow(0 0 2px rgba(3, 112, 250, 0.5));
}

.play-button {
  position: fixed;
  top: 20px;
  left: 20px;
  padding: 10px 20px;
  background-color: #0370fa;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  z-index: 1000;
}

.play-button:hover {
  background-color: #0256c4;
}

/* 保留旧图标样式以兼容其他组件 */
.btnIcon {
  width: 1.2rem;
  height: 1.2rem;
  margin-right: 6px;
}

/* 添加文字的悬停效果 */
text {
  transition: font-size 0.3s;
}

text:hover {
  font-size: 14px;
  cursor: pointer;
}

.irregular-rect {
  fill: rgb(99, 99, 99);
  stroke: rgb(99, 99, 99);
  stroke-width: 2;
  stroke-dasharray: 16, 5; /* 创建短划线效果 */
  stroke-linecap: round; /* 使短划线末端变圆 */
}

.status-info-container {
  margin-bottom: 20px;
}

.status-row-single,
.status-container-dual {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 10px 0;
}

.status-header {
  display: flex;
  width: 100%;
  gap: 20px;
  padding: 5px 0;
}

.status-column {
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-width: fit-content;
}

.status-label {
  font-weight: bold;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-value {
  font-size: 14px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 5px;
}

.running {
  background-color: #52c41a;
}

.stopped {
  background-color: #f5222d;
}

.upper-level,
.lower-level {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 状态信息显示区域样式 */
.status-info-container {
  width: 100%;
  // background: #ffffff;
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 15px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: "Microsoft YaHei", Arial, sans-serif;
}

/* 单层模式样式 */
.status-row-single {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  gap: 30px;
  padding: 8px 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: fit-content;
  flex-shrink: 0;
}

/* 双层模式样式 - 表格布局 */
.status-container-dual {
  display: table;
  width: 100%;
  border-collapse: collapse;
}

.status-header {
  display: table-row;
  // background-color: #f5f5f5;
}

.status-values {
  display: table-row-group;
}

.status-row {
  display: flex;
}

.status-column {
  display: table-cell;
  padding: 10px 15px;
  // border-right: 1px solid #e8e8e8;
  vertical-align: middle;
  text-align: center;
  min-width: 120px;
}

.status-column:last-child {
  border-right: none;
}

/* 标签样式 */
.status-label {
  font-size: 14px;
  color: #666666;
  font-weight: bold;
  white-space: nowrap;
  display: block;
}

/* 数值样式 */
.status-value {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  white-space: nowrap;
}

/* 状态指示器样式 */
.status-indicator,
.connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 状态点样式 */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  flex-shrink: 0;
}

.status-dot.running {
  background-color: #52c41a; /* 绿色 - 运行中 */
}

.status-dot.stopped {
  background-color: #f5222d; /* 红色 - 停止 */
}

.status-dot.connected {
  background-color: #52c41a; /* 绿色 - 已连接 */
}

.status-dot.disconnected {
  background-color: #f5222d; /* 红色 - 未连接 */
}

/* 层级标识 */
.upper-level {
  background-color: #f8f9fa;
}

.lower-level {
  background-color: #f1f3f4;
}

/* 表头样式 */
.status-header .status-column {
  // background-color: #f5f5f5;
  // border-bottom: 2px solid #d9d9d9;
  font-weight: bold;
}

/* 单层模式样式 */
.status-info-container .status-row-single {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  gap: 30px;
  padding: 8px 0;
}

.status-info-container .status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: fit-content;
  flex-shrink: 0;
}

/* 双层模式样式 - 表格布局 */
.status-info-container .status-container-dual {
  display: table;
  width: 100%;
  border-collapse: collapse;
}

.status-info-container .status-header {
  display: table-row;
  // background-color: #f5f5f5;
}

.status-info-container .status-values {
  display: table-row-group;
}

.status-info-container .status-row {
  display: table-row;
}

.status-info-container .status-column {
  display: table-cell;
  padding: 10px 15px;
  // border-right: 1px solid #e8e8e8;
  vertical-align: middle;
  text-align: center;
  min-width: 120px;
}

.status-info-container .status-column:last-child {
  border-right: none;
}

/* 标签样式 */
.status-info-container .status-label {
  font-size: 14px;
  color: #666666;
  font-weight: bold;
  white-space: nowrap;
  display: block;
}

/* 数值样式 */
.status-info-container .status-value {
  font-size: 14px;
  font-weight: bold;
  color: #333333;
  white-space: nowrap;
}

/* 状态指示器样式 */
.status-info-container .status-indicator,
.status-info-container .connection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 状态点样式 */
.status-info-container .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  flex-shrink: 0;
}

.status-info-container .status-dot.running {
  background-color: #52c41a; /* 绿色 - 运行中 */
}

.status-info-container .status-dot.stopped {
  background-color: #f5222d; /* 红色 - 停止 */
}

.status-info-container .status-dot.connected {
  background-color: #52c41a; /* 绿色 - 已连接 */
}

.status-info-container .status-dot.disconnected {
  background-color: #f5222d; /* 红色 - 未连接 */
}

/* 层级标识 */
.status-info-container .upper-level {
  background-color: transparent; /* 改为透明，保持整体白色 */
}

.status-info-container .lower-level {
  background-color: transparent; /* 改为透明，保持整体白色 */
}

/* 表头样式 */
.status-info-container .status-header .status-column {
  // background-color: #f5f5f5;
  // border-bottom: 2px solid #d9d9d9;
  font-weight: bold;
}
</style>