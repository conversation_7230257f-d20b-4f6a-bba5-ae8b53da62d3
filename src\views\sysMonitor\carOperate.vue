<template>
<el-dialog :title="$t('scada.CarOperation')" :visible.sync="carVisible" width="30%">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item :label="$t('common.CarNumber')"> 
          <el-input v-model="form.name"></el-input>
        </el-form-item>
            <el-form-item label="层数:">
              <el-radio v-model="layerNum" label="1" @change="layerNumChange">{{ $t('common.UpperLevel') }}</el-radio>
              <el-radio v-model="layerNum" label="2" @change="layerNumChange">{{ $t('common.LowerLevel') }}</el-radio>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="carVisible = false">{{ $t('common.Lock') }}</el-button>
        <el-button type="primary" @click="carVisible = false">{{ $t('common.Unlock') }}</el-button>
        <el-button @click="carVisible = false">{{ $t('common.Forward') }}</el-button>
        <el-button type="primary" @click="carVisible = false">{{ $t('common.Reverse') }}</el-button>
      </span>
    </el-dialog>
</template>
 
<script>
 
export default {
 
  data() {
    
    return {
      //弹框
      carVisible:false
 
    }
  },
  methods:{
    //弹框
    sendmessage(){
        this.carVisible = true
    } 
 
  }
  
}
</script>