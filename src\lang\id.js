// 印尼语
const id = {
  login: {
    changelanguage: "Ganti Bahasa",
    index: "I'm six",
    title: "COGY Sorting System",
    Success: "Login Berhasil",
    plsuaername: "Masukkan Username",
    plspassword: "Masukkan Password",
    plscode: "Masukkan Kode Verif",
    login: "Login",
    code: "Kode Verif",
      chinese: "中文",
    english: "english",
    thai: "ภาษาไทย",
    ms: "Bahasa Melayu",
    vi: "Tiếng Việt",
    es: "Español",
    pt: "Bahasa Portugis",
    id: "Bahasa Indonesia",
    ph: "Filipino",
    SwipeRight: "Geser ke kanan",
  },
  welcome: {
    title: "Selamat Datang di Sistem Pengecekan Data Sortir",
  },
  headers: {
    title: "Sistem Pengecekan Data Sortir",
    loginout: "Log Out",
  },
  sidebar: {
    Home: "Beranda",
    Syscfg: "Konfigurasi Sistem",
    Userinfosup: "Manajemen Pengguna Feeder",
    User: "Manajemen User",
    Role: "Manaj<PERSON><PERSON> Peran",
    <PERSON><PERSON>: "Manaj<PERSON>en Menu",
    <PERSON>ys<PERSON><PERSON>: "Parameter Sistem",
    <PERSON>rrchute: "Slot Abnormal",
    Sysurl: "Jmlh Hari Penyimpanan Log",
    Sortingplan: "Skema Sortir",
    Datamgr: "Manajemen Data",
    Runninglog: "Log Pengoperasian",
    Locklog: "Lock Log",
    Sortinglog: "Log Sortir",
    Packagenumberlog: "Histori No Bagging",
    Sortingrepush: "Data Re-push",
    Sortingreport: "Laporan Statistik",
    Chutereport: "Jumlah Sortir Slot",
    Sortingcountsup: "Statistik Jumlah（by Feeder）",
    Sortingcountuser: "Statistik Jumlah（by No Staff）",
    Packagenumbercount: "Total Bagging",
    Sortingefficiency: "Efektivitas",
    Errorreport: "Tipe Statistik",
    Monitor: "Sistem Monitor",
    Scada: "Monitor SCADA",
    Job: "Tugas Terjadwal",
    Config: "Pengaturan Parameter",
    Dict: "Manajemen Kamus",
  },
  page: {
    homepage: {
      boardingcount: "Jumlah Kereta",
      scancount: "Jumlah Terbaca",
      cellcount: "Jumlah Paket Turun",
      cancelleditems: "Batalkan Paket",
      rabbitinterceptions: "Total Delay",
      unmatchedcodes: "Tdk Sesuai Skema",
      overbounds: "Total Berputar Max",
      anomalies: "Total Abnormal",
      sortingvolume: "Volume Sortir",
      monday: "Senin",
      tuesday: "Selasa",
      wednesday: "Rabu",
      thursday: "Kamis",
      friday: "Jumat",
      saturday: "Sabtu",
      sunday: "Minggu",
    },
    Serviceusermanagement: {
      Refresh: "Muat Ulang",
      AddUser: "Tambah Pengguna",
      BulkDelete: "Hapus Masal",
      FullName: "Nama",
      EmployeeID: "No ID Petugas",
      Query: "Cek",
      Import: "Impor",
      OnlyExcel: "Hanya bs impor template dengan format excel *.xlsx",
      SerialNumber: "No Serial",
      UserEmployeeID: "ID Karyawan",
      UserName: "Username",
      UserID: "User ID",
      Creator: "Pembuat",
      CreationTime: "Waktu Pembuatan",
      Operation: "Operasi",
      UserPassword: "Password",
      ConfirmPassword: "Konfirmasi Password",
      NewPassword: "Password Baru",
      Save: "Simpan",
      Cancel: "Batal",
      UpdateUser: "Ubah User",
    },
    usermanagement: {
      UserID: "User ID",
      UserName: "Username",
      UserAccount: "Akun Pengguna",
      PlaseUserAccount: "Masukkan Akun",
      PhoneNumber: "No HP",
      PleasePhoneNumber: "Masukkan No HP",
      Status: "Status",
      PleaseStatus: "Masukkan Status",
      StartDate: "Tgl Mulai",
      EndDate: "Tanggal Berakhir",
      CreationTime: "Waktu Pembuatan",
      Operation: "Operasi",
      UserGender: "Jenis Kelamin",
      UserNickname: "Nickname",
      Email: "Email",
      UserPassword: "Password",
      Active: "Normal",
      Inactive: "Nonaktif",
      Role: "Peran",
      Note: "Keterangan",
      Search: "Cari",
      Reset: "Reset",
      Add: "Tambah",
      Delete: "Hapus",
      Update: "Ubah",
      More: "Lebih banyak",
      ResetPassword: "Reset Password",
    },
  },
  common: {
    DerivedData: "Export",
    ViewDWSPeakEffect: "Cek Efektivitas Peak DWS",
    peakEffect: "Efektivitas saat Peak",
    SearchData: "Cari Data",
    NoData: "Tdk Ada Data",
    Arrival: "Incoming",
    Departure: "Outgoing",
    ArrivalAndDeparture: "In-Out",
    CloseOther: "Tutup yg lain",
    CloseAll: "Tutup Semua",
    RoleName: "Nama Peran",
    PlaseRoleName: "Masukkan Nama Peran",
    PermissionCharacters: "Permission Characters",
    PlasePermissionCharacters: "Masukkan Permission Characters",
    Order: "Urutan",
    Status: "Status",
    Search: "Cari",
    Reset: "Reset",
    Delete: "Hapus",
    Update: "Ubah",
    More: "Lebih banyak",
    CreationTime: "Waktu Pembuatan",
    Operation: "Operasi",
    Update: "Ubah",
    Add: "Tambah",
    UpdateCache: "Update Cache",
    Select: "Cek",
    Import: "Impor",
    Export: "Ekspor",
    SerialNumber: "No Serial",
    RoleNumber: "No Peran",
    Memo: "Keterangan",
    UpdateDate: "Update Waktu",
    ParameterName: "Nama Parameter",
    ParameterValue: "Value Parameter",
    PleaseSelectARole: "Pilih Peran",
    PleaseInputnickname: "Masukkan Nickname",
    PleaseInputPhoneNumber: "Masukkan No HP",
    PleaseInputEmail: "Masukkan E-mail",
    PleaseInputUserName: "Masukkan Username",
    PleaseInputPassWord: "Masukkan Password",
    RoleStatus: "Status Peran",
    RoleCode: "No User",
    PlaseInputRoleCode: "Masukkan No User",
    CreatorID: "ID Pembuat",
    Determine: "Konfirmasi",
    Cancellation: "Batal",
    Task: "Tugas",
    PlaseTask: "Masukkan Nama Tugas",
    TaskCode: "Nomor Kode Tugas",
    PlaseTaskCode: "Masukkan No Tugas",
    MenuName: "Nama Menu",
    PlaseMenuName: "Masukkan Nama Menu",
    MenuStatus: "Status Menu",
    ExpandAndCollapse: "Perluas/Kecilkan",
    SelectAllDonteSelectAll: "Pilih Semua/Tdk Semua",
    ParentChildLinkage: "Parent-child linkage",
    MenuPermissions: "Menu Hak Akses",
    PleaseEnterContent: "Masukkan Konten",
    Icon: "Icon",
    ComponentPath: "Jalur Komponen",
    SchemeID: "ID Skema",
    ModeType: "Tipe Penyortiran",
    PlanName: "Nama Skema",
    PlanCode: "No Skema",
    PlanDesc: "Keterangan Skema",
    PlanFlag: "Jenis Skema",
    IsSelected: "Aktif",
    UpdateDate: "Ubah Waktu",
    Detail: "Detail",
    Enable: "Aktifkan",
    Date: "Waktu",
    Grade: "Tingkatan",
    Source: "Source",
    Message: "Pesan",
    ExtraData: "Data Tambahan",
    plcType: "Layer",
    Number: "Nomor",
    Component: "Komponen",
    Chute: "Slot",
    Supply: "Feeder",
    dwsNo: "No Seri DWS",
    BigBarRate: "Presentase Paket Besar",
    Quantity: "Jumlah",
    SmallBarRate: "Presentase Paket Kecil",
    ExceptionCode: "Kode Abnormal",
    Code: "No AWB",
    ScanTime: "Waktu Scan",
    BoardingTime: "Waktu Naik Kereta",
    DropTime: "Waktu Turun Slot",
    NextPieceTime: "Waktu Paket",
    passBackTime: "Waktu Passback",
    arrivalTime: "Waktu Sampai",
    PacketNumber: "No Paket",
    TimeType: "Tipe Waktu",
    turns: "Jmlh Putaran",
    Image: "Gambar",
    UpperLevel: "Tingkat Atas",
    LowerLevel: "Tingkat Bawah",
    LayerNumber: "Jumlah Tingkat",
    PleaseEnterTheDictionaryLabel: "Silakan masukkan label kamus",
    DictionaryName: "Nama Kamus",
    DictionaryId: "ID Kamus",
    DictionaryType: "Tipe Kamus",
    PleaseEnterTheDictionaryType: "Silakan masukkan tipe kamus",
    PleaseEnterTheDictionaryName: "Silakan masukkan nama kamus",
    BagBindingBfficer: "Petugas Pembindung Paket",
    DictionaryEncoding: "Kode Kamus",
    DictionaryTag: "Label Kamus",
    DictionaryValue: "Nilai Kamus",
    DictionarySort: "Penyortiran Kamus",
    DictionaryRemark: "Keterangan",
    DictionaryCreateTime: "Waktu Pembuatan",
    DataTag: "Label Data",
    PleaseEnterTheDataLabel: "Silakan masukkan label data",
    DataKey: "Kunci Data",
    StyleAttribute: "Atribut Gaya",
    DisplayOrder: "Urutan Tampilan",
    EchoStyle: "Gaya Eko",
    ListClass: "Gaya Daftar",
    PleaseEnterTheDataKey: "Silakan masukkan kunci data",
    PleaseEnterTheStyleAttribute: "Silakan masukkan atribut gaya",
    PleaseEnterTheDisplayOrder: "Silakan masukkan urutan tampilan",
    PleaseEnterTheEchoStyle: "Silakan masukkan gaya eko",
    PleaseEnterTheListClass: "Silakan masukkan gaya daftar",
    PleaseEnterTheRemark: "Silakan masukkan keterangan",
    PleaseEnterTheContent: "Silakan masukkan konten",
    TheAddressNeedsToBehttp:
      "Jika memilih tautan eksternal, alamat harus dimulai dengan http(s)://",
    TheAddressNeedsToBehttpUser:
      "Alamat akses, seperti: user. Jika alamat eksternal perlu akses internal, dimulai dengan http(s)://",
    TheAddressNeedsToBehttpCatalogue:
      "Alamat komponen yang diakses, seperti: system/user/index, secara default di direktori views",
    TheDefaultPassingParametersForTheRoute:
      "Parameter default untuk akses rute, seperti: {'id': 1, 'name': 'ry'}",
    TheComponentWillBeCachedByKeepAlive:
      "Jika dipilih, akan disimpan oleh keep-alive. Komponen name harus sesuai dengan alamat",
    SelectHiddenThenTheRoute:
      "Jika dipilih tersembunyi, rute tidak akan muncul di sidebar tetapi masih dapat diakses",
    SelectDisableThenTheRouteSidebar:
      "Jika dipilih dinonaktifkan, rute tidak akan muncul di sidebar dan tidak dapat diakses",
    PleaseEnterTheRouteParameters: "Silakan masukkan parameter rute",
    Yes: "Ya",
    No: "Tidak",
    PermissionCharactersString: "String kebenaran yang ditentukan dalam controller, contoh: @PreAuthorize(`@ss.hasRole('admin')`)",
    Cache1: "Cache",
    NoCache: "Tanpa Cache",
    AddUser: "Tambah Pengguna",
    BatchCancelAuthorization: "Batal Otorisasi Massal",
    Close: "Tutup",
    CancelAuthorization: "Batal Otorisasi",
    View: "Lihat",
    UserType: "Tipe Pengguna",
    PleaseSelectUserType: "Silakan pilih tipe pengguna",
    Forward: "Teruskan",
    Reverse: "Balik",
    Lock: "Kunci",
    Unlock: "Buka Kunci",
    SendMessage: "Kirim Pesan",
    TaskGroup: "Nama Kelompok Tugas",
    PleaseSelectTaskGroup: "Silakan pilih nama kelompok tugas",
    TaskStatus: "Status Tugas",
    PleaseSelectTaskStatus: "Silakan pilih status tugas",
    Log: "Log",
    InvokeTarget: "Target Panggilan",
    CronExpression: "Ekspresi Cron",
    ExecuteOnce: "Eksekusi Sekali",
    TaskDetails: "Detail Tugas",
    DispatchLog: "Log Pengiriman",
    InvokeTarget: "Metode Panggilan",
    BeanCallExample: "Contoh Panggilan Bean: ryTask.ryParams('ry')",
    ClassCallExample:
      "Contoh Panggilan Kelas: com.ruoyi.quartz.task.RyTask.ryParams('ry')",
    ParameterDescription:
      "Deskripsi Parameter: Mendukung string, boolean, long, float, integer",
    PleaseInputInvokeTarget: "Silakan masukkan target panggilan",
    PleaseInputCronExpression: "Silakan masukkan ekspresi cron",
    GenerateExpression: "Generasi Ekspresi",
    ExecuteStrategy: "Strategi Eksekusi",
    MisfirePolicy: "Strategi Eksekusi",
    ImmediateExecution: "Eksekusi Segera",
    DelayExecution: "Eksekusi Ditunda",
    AbandonExecution: "Eksekusi Ditinggalkan",
    PleaseSelectExecuteStrategy: "Silakan pilih strategi eksekusi",
    Concurrent: "Apakah Bersamaan",
    Allow: "Izin",
    Prohibit: "Larang",
    PleaseSelectConcurrent: "Silakan pilih apakah bersamaan",
    CronExpressionGenerator: "Generator Ekspresi Cron",
    NextExecutionTime: "Waktu Eksekusi Berikutnya",
    TaskDetails: "Detail Tugas",
    TaskGroup1: "Kelompok Tugas",
    DefaultStrategy: "Strategi Default",
    ExecuteStatus: "Status Eksekusi",
    PleaseSelectExecuteStatus: "Silakan pilih status eksekusi",
    ExecutionTime: "Waktu Eksekusi",
    PleaseSelectExecutionTime: "Silakan pilih waktu eksekusi",
    Clear: "Kosongkan",
    JobLogId: "ID Log Tugas",
    JobMessage: "Pesan Log",
    Detail1: "Detail",
    DispatchLogDetails: "Detail Log Pengiriman",
    PleaseSelect: "Silakan pilih",
    SelectStartTime: "Pilih Waktu Mulai",
    SelectEndTime: "Pilih Waktu Akhir",
    DebugStart: "Debug (Mulai)",
    DebugClose: "Debug (Tutup)",
    ClearPacketNumber: "Kosongkan Nomor Paket",
    Upload: "Unggah",
    TotalQuery: "Total yang ditemukan",
    property: "Gambar",
    all: "Semua",
    UnloadingToDeliveryScanning:
      "Pemindaian Paket dari Pengunggahan ke Penerimaan",
    BuildingPackageScanning: "Pemindaian Pembentukan Paket",

    SelectUser: "Pilih Pengguna",
    InvokeTargetMethod: "Metode Pemanggilan Objek Tujuan",
    PleaseSelectTaskGroup: "Pilih Kelompok Tugas",
    CronExpression1: "cron expression",
    ExceptionInfo: "Informasi Pengecualian",

    Edit: "Edit",
    ScopeOfAuthority: "Lingkup Otoritas",
    DataPermission: "Otoritas Data",
    Confirm: "Konfirmasi",
    StartDate: "Tanggal Mulai",
    EndDate: "Tanggal Akhir",
    Weight: "Berat",
    length: "Panjang",
    width: "Lebar",
    heigth: "Tinggi",
    CarNumber: "No Kereta",
    RequestedGate: "Slot Request",
    PhysicalGrid: "Balikan no Slot PLC",
    taskNo: "Nomor Tugas",
    TerminalDispatchCode: "TLC",
    FallingGridTime: "Total Waktu Turun",
    PackageGrade: "No Bagging",
    ChipNumber: "No RFID",
    BindingTime: "Waktu Binding",
    BindingPersonnel: "Petugas Binding",
    ScanType: "Jenis Scan",
    NextStationNumber: "No Stasiun Berikutnya",
    NextStopNumber: "Next Station Number",
    Rfid: "E-bagging",
    TimeInterval: "Interval Waktu",
    SortingQuantity: "Jmlh Sortir",
    TotalSortingWeight: "Total Berat",
    NumberOfScannedBarcodeRecords: "Jmlh Paket Terbaca",
    RecordTheNumberOfPackagesLoadedOntoTheVehicle: "Jumlah Paket Naik Kereta",
    NumberOfDropFeedbackRecords: "Jmlh Paket Turun",
    scanQuantity: "Jmlh Scan",
    arrivalQuantity: "Jumlah Sampai",
    passBackQuantity: "Jmlh Passback",
    TotalNumberOfPackages: "Total Jmlh Paket",
    Packagenumb: "No Bagging (Jmlh Paket didalamnya)",
    Type: "Tipe",
    Count: "Jumlah",
    SystemType: "Tipe Sistem",
    EmployeeID: "Masukkan No ID Petugas",
    FullName: "Masukkan Nama",
    Password: "Masukkan Password",
    ConfirmPassword: "Masukkan Password",
    SelectGender: "Pilih Gender",
    Active: "Normal",
    Inactive: "Nonaktif",
    Male: "Laki-Laki",
    Female: "Perempuan",
    Unknown: "Tdk Diketahui",
    RoleOrder: "Urutan Peran",
    Show: "Tampil",
    Hide: "Sembunyikan",
    Default: "Default",
    System: "Sistem",
    Success: "Berhasil",
    Failure: "Gagal",
    AddMenu: "Tambah Menu",
    EditMenu: "Ubah Menu",
    ParentMenu: "Parent Menu",
    MenuType: "Tipe Menu",
    Directory: "Direktori",
    Menu: "Menu",
    Button: "Tombol",
    MenuIcon: "Menu Icon",
    SelectIcon: "Pilih Icon",
    RouteAddress: "Route Address",
    DisplayOrder: "Urutkan Tampilan",
    ExternalLink: "Tautan Eksternal",
    DisplayStatus: "Status Tampilan",
    MenuStatus: "Status Menu",
    RouteParameters: "Route Config",
    Cache: "Cache",
    ComponentPath: "Jalur Komponen",
    AddRole: "Tambah Peran",
    EditRole: "Ubah Peran",
    AddPlan: "Tambah Skema",
    Cancel: "Batal",
    FirstSegmentCode: "Kode Pertama",
    SecondSegmentCode: "Kode kedua",
    ThirdSegmentCode: "Kode Ketiga",
    NextStopCode: "Next Station Code",
    NextStopName: "Next Station Name",
    ModificationTime: "Waktu Pengubahan",
    BulkDelete: "Hapus Masal",
    AddDetails: "Tambah Detail",
    ByServicePackage: "Berdasarkan Feeder",
    ByDwsNo: "Berdasarkan no DWS",
    Granularity: "Besaran",
    LogicCode: "Kode Logic",
    PacketType: "Tipe Paket",
    IsUpload: "Upload",
    AddSuccess: "Sukses Menambahkan",
    EditSuccess: "Pengubahan berhasil",
    DelSuccess: "Berhasil Menghapus",
    ImportSuccessful: "Impor Berhasil",
    BeginExport: "Mulai Export",
    ModificationFailed: "Pengubahan Gagal",
    AddFailed: "Gagal Menambah",
    OperationSuccessful: "Operasi Sukses",
    OperationFailed: "Operasi Gagal",
    OperationCancellation: "Operasi Dibatalkan",
    ExportFailed: "Gagal Ekspor",
    LoginOut: "Anda sudah log out, silahkan login kembali",
    ConfirmLogout: "Keluar",
    LogAgain: "Login Ulang",
    Remark: "Keterangan",
    DwsNo: "No DWS",
    Notempty: "Password dan kode verifikasi tdk boleh kosong",
    Notpassword: "Password tdk boleh kosong",
    Id: "Parameter Utama",
    Parameter: "Parameter",
    PlParameter: "Masukkan Nama Parameter",
    ParameterKey: "Parameter Key",
    PlParameterKey: "Masukkan Nama Key Paramater",
    ParameterValue: "Value Parameter",
    PlParameterValue: "Masukkan Value Parameter",
    Group: "Nama Grup",
    PlGroup: "Masukkan Nama Grup",
  },
  scada: {
    DeviceRunningStatus: "Status Mesin",
    DeviceStopped: "Mesin Berhenti",
    DeviceRunning: "Mesin Sedang Beroperasi",
    StartTime: "Waktu Mulai Beroperasi",
    RunningSpeed: "Kecepatan",
    CartOccupancyRate: "Tingkat Okupansi Kereta",
    TotalDistanceTraveledByDevice: "Total Keseluruhan Jarak Tempuh",
    DistanceTraveledInCurrentRun: "Jarak Tempuh",
    TotalDistanceTraveled: "Total Jarak Tempuh",
    Scans: "Jlmlh Scan",
    PendingStart: "Pending Start",
    FullScreen: "Layar Fullscreen",
    ExitFull: "Keluar Fullscreen",
    UpperLevelRunningSpeed: "Kecepetan Layer Atas",
    UpperLevelDeviceRunningStatus: "Status Operasi Mesin Layer Atas",
    UpperLevelStartTime: "Waktu Mulai Operasi Layer Atas",
    UpperLevelCartOccupancyRate: "Presentase Okupansi Kereta Layer Atas",
    UpperLevelByDevice: "Total Jarak Tempuh Layer Atas",
    LowerLevelDeviceRunningStatus: "Status Operasi Mesin Layer Bawah",
    UpperLayerPLCDisconnect: "Koneksi PLC Layar Atas Abnormal",
    LowerLevelPLCDisconnect: "Koneksi PLC Layar Bawah Abnormal",
    UpperLayerPLCConnectionStatus: "Status Koneksi PLC Layer Atas",
    LowerLevelPLCConnectionStatus: "Status Koneksi PLC Layer Bawah",
    LowerLevelStartTime: "Waktu Mulai Operasi Layer Bawah",
    LowerLevelRunningSpeed: "Kecepetan Layer Bawah",
    LowerLevelCartOccupancyRate: "Presentase Okupansi Kereta Layer Bawah",
    LowerLevelTotalDistanceTraveledByDevice: "Total Jarak Tempuh Layer Bawah",
    UpperLevelScans: "Jumlah Terbaca Layer Atas",
    LowerLevelScans: "Jumlah Terbaca Layer Bawah",
    AbnormalQuantity: "Jumlah Abnormal",
    NumberOfSlotsOccupied: "Jmlh Slot Turun",
    FailedRepushQuantity: "Jumlah Upload Ulang",
    InterceptedQuantity: "Jmlh Paket Tersangkut",
    ExcessiveCirclesQuantity: "Jumlah Paket Berputar",
    UnconfiguredThreeSegmentCodeSlots: "Slot Tdk Terkonfigurasi",
    ComprehensiveExceptionSlots: "Slot Abnormal",
    CancelledItems: "Batalkan Paket",
    UnobtainedThreeSegmentCodeInformation: "Tdk Dapat Data",
    WebSocketStatus: "Status Web Socket",
    WCSCommunicationStatus: "Status Komunikasi WCS",
    Connected: "Terhubung",
    NotConnected: "Tdk Terhubung",
    SortingStatus: "Status Sortir",
    Idle: "Idle",
    Loaded: "Memuat Paket",
    CartStatus: "Status Kereta",
    LittleThingsAreQuick:'Tangan Cepat Kecil',
    Headscratcher: 'menyapu atas',
    OnLine:'secara online',
    Offline:'Offline',
    Locked: "Lock",
    FullPackage: "Penuh",
    SlotStatus: "Status Slot",
    InterceptedItem: "Paket Tersangkut",
    ExceptionSlot: "Slot Abnormal",
    PendingCommunication: "Pending",
    Max: "Paket Berputar Max",
    Cancellation: "Batalkan Paket",
    UnProgramme: "Tdk Ada Skema",
    UnThree: "TLC tdk sesuai",
    CartOperation: "Operasi Kereta",
    OneKeyUnlock: "Unlock Semua Slot",
    OneKeyLockSlot: "Lock Semua Slot",
    CartNumber: "No Kereta",
    FloorNumber: "No Layer",
    UpperLevel: "Layer Atas",
    LowerLevel: "Layer Bawah",
    Lock: "Lock",
    Unlock: "Buka Kunci",
    ManualSwitchStart: "Manual Switch (Hidup)",
    ManualSwitchClose: "Manual Switch (Mati)",
    Run: "Beroperasi",
    Close: "Tutup",
    DisabledList: "Daftar Nonaktif",
    AbnormalAlarm: "Alarm Abnormal",
    DisableCart: "Nonaktifkan Kereta",
    UpperLevelCart: "Kereta Layer Atas",
    LowerLevelCart: "Kereta Layer Bawah",
    VerificationPassword: "Masukkan Verifikasi Password",
    Confirm: "Konfirmasi",
    ClearTheKilometers: "Reset Jarak Tempuh",
    PleaseSelectClearKilometers: "Reset KM",
    FaultLevel: "Tingkat Kerusakan",
    StartTime: "Waktu Mulai",
    EndTime: "Waktu Berakhir",
    CriticalAlarm: "Alarm Bahaya",
    GeneralAlarm: "Alarm Umum",
    MinorAlarm: "Alarm Ringan",
    SearchCriteria: "Kondisi Pencarian",
    Time: "Waktu",
    Select: "Cek",
    PleaseEnterContent: "Masukkan Konten",
    SerialNumber: "No Serial",
    Operation: "Operasi",
    AlarmHelpLink: "Penanganan Kesalahan",
    AlarmType: "Jenis Alarm",
    AlarmSource: "Penyebab Alarm",
    Content: "Isi",
  },
};
export default id;
