{"name": "vue-admin-template", "version": "4.2.1", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "dev:test": "vue-cli-service serve --mode test", "build": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@svgdotjs/svg.js": "^3.1.2", "axios": "0.18.1", "echarts": "^4.2.1", "element-ui": "^2.15.1", "exceljs": "^4.3.0", "getmac": "^5.20.0", "i": "^0.3.7", "js-cookie": "2.2.0", "moment": "^2.29.4", "nodejs-websocket": "^1.7.2", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "screenfull": "^4.2.0", "uuid": "^9.0.0", "vue": "2.6.10", "vue-count-to": "^1.0.13", "vue-i18n": "^6.1.3", "vue-monoplasty-slide-verify": "^1.3.1", "vue-router": "3.0.6", "vuex": "3.1.0", "vxe-table": "^2.10.20", "vxe-table-plugin-export-xlsx": "^2.2.2", "xe-utils": "^2.8.3"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.6.0", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.6.3", "@vue/cli-service": "3.6.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.8.6", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "node-sass": "^4.9.0", "runjs": "^4.3.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "^4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}