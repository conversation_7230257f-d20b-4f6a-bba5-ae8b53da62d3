<template>
  <div class="screen-adaptive" :style="adaptiveStyle">
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'ScreenAdaptive',
  data() {
    return {
      scale: {
        x: 1,
        y: 1
      },
      topOffset: 0
    }
  },
  computed: {
    adaptiveStyle() {
      return {
        transform: `scaleX(${this.scale.x}) scaleY(${this.scale.y}) translate(-50%, -50%)`,
        width: '1920px',
        height: '1080px',
        left: '50%',
        position: 'absolute',
        top: '50%',
        transformOrigin: '0 0',
        WebkitTransformOrigin: '0 0',
        transition: '0.2s'
      }
    }
  },
  mounted() {
    this.calculateTopOffset()
    this.calculateScale()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 计算顶部偏移量
    calculateTopOffset() {
      // 获取顶部导航栏元素
      const header = document.querySelector('.navbar') // 根据实际类名调整
      const tabs = document.querySelector('.tags-view-container') // 根据实际类名调整
      
      // 计算总偏移量
      this.topOffset = (header ? header.offsetHeight : 0) + 
                      (tabs ? tabs.offsetHeight : 0)
    },
    
    // 处理窗口大小变化
    handleResize() {
      this.calculateTopOffset()
      this.calculateScale()
    },
    
    // 计算缩放比例
    calculateScale() {
      const designWidth = 1920
      const designHeight = 1080
      const currentWidth = document.documentElement.clientWidth
      const currentHeight = document.documentElement.clientHeight - this.topOffset

      // 计算缩放比例，保持宽高比
      const scaleX = currentWidth / designWidth
      const scaleY = currentHeight / designHeight
      
      // 使用较小的缩放比例，确保内容完全显示
      const scale = Math.min(scaleX, scaleY)
      
      this.scale.x = scale
      this.scale.y = scale
    }
  }
}
</script>

<style scoped>
.screen-adaptive {
  box-sizing: border-box;
  overflow: hidden;
    
}
</style> 