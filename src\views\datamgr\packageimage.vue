<template>
  <div style="padding:5px">

    <el-form
      :model="form"
      style="margin-bottom:5px"
    >
      <el-form-item style="margin:0 0 5px 0">
        <span>
          {{ $t('common.Code') }}:
          <el-input v-model="form.barCode" style="width:200px" />
        </span>

        <span style="margin:0 0 0 20px">
          <el-button-group>
            <el-button type="primary" icon="el-icon-search" @click="loadData">{{ $t('common.Select') }}</el-button>
            <el-button type="primary" icon="el-icon-refresh-left" @click="resetForm">{{ $t('common.Reset') }}</el-button>
          </el-button-group>
        </span>
      </el-form-item>
    </el-form>

    <el-tag size="medium">{{ $t('common.TotalQuery') }}【{{ imageInfos.length }}】{{ $t('common.property') }}</el-tag>

    <el-carousel indicator-position="none" :autoplay="false" width="100vh" height="100vh">
      <el-carousel-item v-for="(imageInfo,index) in imageInfos" :key="index">
        <table class="mailTable" cellspacing="0" cellpadding="0">
          <tr>
            <td class="column">{{ $t('common.SerialNumber') }}</td>
            <td>第【{{ index+1 }}】张 / 共【{{ imageInfos.length }}】张</td>
          </tr>
          <tr>
            <td class="column">{{ $t('common.Code') }}</td>
            <td>{{ imageInfo.barCode }}</td>
          </tr>
          <tr>
            <td class="column">{{ $t('common.Supply') }}</td>
            <td>{{ imageInfo.supNo }}</td>
          </tr>
          <tr>
            <td class="column">{{ $t('common.Upload') }}
              {{ $t('common.Date') }}</td>
            <td>{{ imageInfo.businessDate }}</td>
          </tr>
        </table>
        <el-image style="width:900px;height:600px" :src="imageInfo.imageUrl" :preview-src-list="[imageInfo.imageUrl]" />
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'Packageimage',
  data() {
    return {
      form: {
        barCode: ''
      },
      loading: false,
      imageInfos: [],
      tableData: []
    }
  },
  methods: {
    loadData() {
      if (this.form.barCode === '') {
        this.$message({
          message: '条码输入条码不允许为空！',
          type: 'warning',
          duration: 2 * 1000
        })
      } else {
        this.loading = true
        return request({
          url: '/packageimage/loaddata',
          method: 'post',
          data: { startTime:this.form.start,endTime: this.form.end}
        }).then(resp => {
          this.imageInfos = resp.data.result || []
          this.loading = false
        }).catch(error => {
          this.imageInfos = []
          this.loading = false
          this.$message({
            message: error || '加载失败！',
            type: 'error',
            duration: 2 * 1000
          })
        })
      }
    },
    resetForm() {
      this.form = {
        barCode: ''
      }
      this.imageInfos = []
    }
  }
}
</script>

<style lang="scss" scoped>
.mailTable{
    width: 270px;
    margin-right: 10px;
    border-top: 1px solid #E6EAEE;
    border-left: 1px solid #E6EAEE;
}
.mailTable tr td{
    height: 35px;
    line-height: 35px;
    box-sizing: border-box;
    padding: 0 10px;
    border-bottom: 1px solid #E6EAEE;
    border-right: 1px solid #E6EAEE;
}
.mailTable tr td.column {
    background-color: #EFF3F6;
    color: #393C3E;
    width: 26%;
}
.el-carousel__item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .carousel-image {
    max-width: 100%;
    max-height: 100%;
  }
}
</style>
