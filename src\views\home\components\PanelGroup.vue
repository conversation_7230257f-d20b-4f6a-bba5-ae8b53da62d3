<template>
  <el-row :gutter="40" class="panel-group">
    <div>
      <span style="margin: 0 0 0 20px">
        {{ $t('common.SystemType') }}:
        <el-select v-model="systemTypeCode" style="width:120px" @change="handleOptionChange">
          <el-option v-for="item in systemOptions" :key="item.dbCode" :label="item.dbType" :value="item.dbCode" />
        </el-select>
      </span>
    </div>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('car')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="shopping" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{ $t('page.homepage.boardingcount') }}
          </div>
          <count-to :start-val="0" :end-val="getCarCount" :duration="2600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('scanning')">
        <div class="card-panel-icon-wrapper icon-message">
          <svg-icon icon-class="component" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{ $t('page.homepage.scancount') }}
          </div>
          <count-to :start-val="0" :end-val="getScanningCount" :duration="3000" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('fallFeedBack')">
        <div class="card-panel-icon-wrapper icon-money">
          <svg-icon icon-class="example" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{ $t('page.homepage.cellcount') }}
          </div>
          <count-to :start-val="0" :end-val="getFallBehindCount" :duration="3200" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('cancel')">
        <div class="card-panel-icon-wrapper icon-shopping-cancel">
          <svg-icon icon-class="nested" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{ $t('page.homepage.cancelleditems') }}
          </div>
          <count-to :start-val="0" :end-val="getCancelCount" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('intercept')">
        <div class="card-panel-icon-wrapper icon-shopping-intercept">
          <svg-icon icon-class="star" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{ $t('page.homepage.rabbitinterceptions') }}
          </div>
          <count-to :start-val="0" :end-val="getInterceptCount" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('noMatchThreeSegment')">
        <div class="card-panel-icon-wrapper icon-shopping-matching">
          <svg-icon icon-class="guide" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{ $t('page.homepage.unmatchedcodes') }}
          </div>
          <count-to :start-val="0" :end-val="getNoMatchingCount" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('superCycle')">
        <div class="card-panel-icon-wrapper icon-shopping-supercircle">
          <svg-icon icon-class="clipboard" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{ $t('page.homepage.overbounds') }}
          </div>
          <count-to :start-val="0" :end-val="getHypercycleCount" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('totalFail')">
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="bug" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{ $t('page.homepage.anomalies') }}
          </div>
          <count-to :start-val="0" :end-val="getErrorCount" :duration="3600" class="card-panel-num" />
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from 'vue-count-to'
import request from '@/utils/request'

export default {
  components: {
    CountTo
  },
  data() {
    return {
      getScanningCount: 0,
      getCarCount: 0,
      getFallBehindCount: 0,
      getCancelCount: 0,
      getInterceptCount: 0,
      getNoMatchingCount: 0,
      getHypercycleCount: 0,
      getErrorCount: 0,
      systemTypeCode: "1",
      systemOptions: this.getBoxValue(),
    }
  },
  created() {
      this.loadData('/scada/quantity/list')
  },
  methods: {
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type,this.systemTypeCode)
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params:{paramName: 'ScadaConnInfo'}
      }).then((res) => {
        console.log(res,'测试')
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    },
    handleOptionChange() {

      let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;
      console.log(type,'测试数据')

      let url = '/scada/quantity/list'
      
      if (type == 'ffs'){
          url = '/ffs/scada/quantity/list'
      }

      this.loadData(url)

    },
    loadData(url) {
      return request({
        url: url,
        method: 'post',
        data: {dbCode: this.systemTypeCode}
      }).then(resp => {
        this.getScanningCount = resp.data.result[0].scanningCount
        this.getCarCount = resp.data.result[0].carCount
        this.getFallBehindCount = resp.data.result[0].fallFeedBackCount
        this.getCancelCount = resp.data.result[0].cancelCount
        this.getInterceptCount = resp.data.result[0].interceptCount
        this.getNoMatchingCount = resp.data.result[0].noMatchThreeSegmentCount
        this.getHypercycleCount = resp.data.result[0].superCycleCount
        this.getErrorCount = resp.data.result[0].totalFailCount
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || '加载失败！',
          type: 'error',
          duration: 2 * 1000
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }

      .icon-shopping-cancel {
        background: #9c3b98
      }

      .icon-shopping-intercept {
        background: #56d561
      }

      .icon-shopping-matching {
        background: #c49812
      }

      .icon-shopping-supercircle {
        background: #0b7eb7
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #03352b
    }

    .icon-shopping-cancel {
      color: #9c3b98
    }

    .icon-shopping-intercept {
      color: #56d561
    }

    .icon-shopping-matching {
      color: #c49812
    }

    .icon-shopping-supercircle {
      color: #0b7eb7
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
