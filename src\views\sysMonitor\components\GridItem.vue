<template>
  <div class="grid-container">
    <div v-for="(row, rowIndex) in images" :key="rowIndex" class="grid-row">
      <div v-for="(item, itemIndex) in row" :key="itemIndex" class="grid-item">
        <img
          :src="item.img"
          :alt="item.name"
          style="width: 3.25rem; height: 3.25rem; margin-right: 6px"
        />
        <div class="item-info">
          <span class="item-name">{{ item.name }}</span>
          <span class="item-value">{{ item.value }}</span>
        </div>
      </div>
    </div>

    <div class="speed-container" :style="{ height: isTwo ? '255px' : '155px' }">
      <div
        v-for="(item, index) in isTwo ? img : img1"
        :key="index"
        class="speed-item"
      >
        <div
          v-for="(itemCh, itemIndex) in item"
          :key="itemIndex"
          style="position: relative"
        >
          <img
            v-if="isTwo || !itemCh.runSpeedName.includes('下层')"
            :src="itemCh.img"
            :alt="itemCh.runSpeedName"
            style="width: 13rem; overflow: hidden"
          />
          <!-- <div
            class="speed-info"
            :style="{
              right: !isTwo
                ? 'clamp(2rem, 5.2rem, 5rem)'
                : 'clamp(1rem, 4.2rem, 5rem)',
            }"
          > -->
          <div
            class="speed-info"
            :style="{
              '--dynamic-right': !isTwo
                ? 'clamp(2rem, 5.2rem, 5rem)'
                : 'clamp(1rem, 4.2rem, 5rem)',
            }"
          >
            <span style="font-size: 12px; font-weight: 600">{{
              itemCh.flag
            }}</span>
            <span class="speed-value">{{
              itemCh.runSpeedName.includes("速度")
                ? Number(itemCh.runSpeed) + 0
                : Number(itemCh.runSpeed) + 0
            }}</span>
            <span style="font-size: 12px; font-weight: 600">{{
              itemCh.runSpeedName
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
<script>
export default {
  name: "GridItem",
  props: {
    isTwo: {
      type: Boolean,
      default: false,
    },
    dataNum: {
      type: Object,
      default: () => ({
        scansNum: 0,
        scansNumDown: 0,
        distanceDial: 0,
        sortingErrorNum: 0,
        fallingNum: 0,
        exceedsMaxNum: 0,
        interceptNum: 0,
        hypercycleNum: 0,
        notConfiguredGrid: 0,
        abnormalNum: 0,
        cancelNum: 0,
        notObtainedNum: 0,
      }),
    },
    ShowTheNumberOfBottomScans: {
      type: Boolean,
      default: false,
    },
    runSpeedAndRate: {
      type: Object,
      default: () => ({
        speedDial: 2,
        speedDialDown: 1,
        sortingMinutesCarShare: 3,
        sortingMinutesCarShareDown: 0,
      }),
    },
  },
  data() {
    return {
      images: [
        [
          {
            img: require("../../../assets/images/scada/1.png"),
            name: `${this.$t("scada.UpperLevelScans")}`,
            value: "1000",
          },
          {
            img: require("../../../assets/images/scada/1.png"),
            name: `${this.$t("scada.LowerLevelScans")}`,
            value: "1000",
          },
          {
            img: require("../../../assets/images/scada/3.png"),
            name: `${this.$t("scada.TotalDistanceTraveledByDevice")}`,
            value: "1000",
          },
        ],
        [
          {
            img: require("../../../assets/images/scada/4.png"),
            name: `${this.$t("scada.AbnormalQuantity")}`,
            value: "1000",
          },
          {
            img: require("../../../assets/images/scada/5.png"),
            name: `${this.$t("scada.NumberOfSlotsOccupied")}`,
            value: "1000",
          },
          {
            img: require("../../../assets/images/scada/6.png"),
            name: `${this.$t("scada.FailedRepushQuantity")}`,
            value: "1000",
          },
        ],
        [
          {
            img: require("../../../assets/images/scada/7.png"),
            name: `${this.$t("scada.InterceptedQuantity")}`,
            value: "1000",
          },
          {
            img: require("../../../assets/images/scada/8.png"),
            name: `${this.$t("scada.ExcessiveCirclesQuantity")}`,
            value: "1000",
          },
          {
            img: require("../../../assets/images/scada/9.png"),
            name: `${this.$t("scada.UnconfiguredThreeSegmentCodeSlots")}`,
            value: "1000",
          },
        ],
        [
          {
            img: require("../../../assets/images/scada/10.png"),
            name: `${this.$t("scada.ComprehensiveExceptionSlots")}`,
            value: "1000",
          },
          {
            img: require("../../../assets/images/scada/11.png"),
            name: `${this.$t("scada.CancelledItems")}`,
            value: "1000",
          },
          {
            img: require("../../../assets/images/scada/12.png"),
            name: `${this.$t("scada.UnobtainedThreeSegmentCodeInformation")}`,
            value: "1000",
          },
        ],
      ],
      img1: [
        [
          {
            flag: "m/s",
            img: require("../../../assets/images/scada/speed.png"),
            runSpeedName: `${this.$t("scada.UpperLevelRunningSpeed")}`,
            runSpeed: 0,
          },
          {
            flag: "%",
            img: require("../../../assets/images/scada/speed.png"),
            runSpeedName: `${this.$t("scada.UpperLevelCartOccupancyRate")}`,
            runSpeed: 0,
          },
        ],
      ],
      img: [
        [
          {
            flag: "m/s",
            img: require("../../../assets/images/scada/speed.png"),
            runSpeedName: `${this.$t("scada.UpperLevelRunningSpeed")}`,
            runSpeed: 0,
          },
          {
            flag: "%",
            img: require("../../../assets/images/scada/speed.png"),
            runSpeedName: `${this.$t("scada.UpperLevelCartOccupancyRate")}`,
            runSpeed: 0,
          },
        ],
        [
          {
            flag: "m/s",
            img: require("../../../assets/images/scada/speed.png"),
            runSpeedName: `${this.$t("scada.LowerLevelRunningSpeed")}`,
            runSpeed: 0,
          },
          {
            flag: "%",
            img: require("../../../assets/images/scada/speed.png"),
            runSpeedName: `${this.$t("scada.LowerLevelCartOccupancyRate")}`,
            runSpeed: 0,
          },
        ],
      ],
    };
  },
  watch: {
    dataNum: {
      handler(newVal, oldVal) {
        if (!this.isObjectEqual(newVal, oldVal)) {
          this.updateAllValues(newVal);
        }
      },
      deep: true,
      immediate: true,
    },
    runSpeedAndRate: {
      handler(newVal, oldVal) {
        if (!this.isObjectEqual(newVal, oldVal)) {
          this.updateSpeedValues(newVal);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    isObjectEqual(obj1, obj2) {
      if (!obj1 || !obj2) return false;
      return JSON.stringify(obj1) === JSON.stringify(obj2);
    },
    updateAllValues(val) {
      const mapping = [
        ["scansNum", "scansNumDown", "distanceDial"],
        ["sortingErrorNum", "fallingNum", "exceedsMaxNum"],
        ["interceptNum", "hypercycleNum", "notConfiguredGrid"],
        ["abnormalNum", "cancelNum", "notObtainedNum"],
      ];

      mapping.forEach((row, i) => {
        row.forEach((key, j) => {
          this.images[i][j].value = val[key];
          // 两套逻辑，一个是双圈小车，一个是单圈小车，单圈小车需要判断ShowTheNumberOfBottomScans  // this.isTwo && this.images[i][j].name === "下层扫描数" && this.ShowTheNumberOfBottomScans
          // 单圈逻辑  this.images[i][j].name === "下层扫描数"
          if (
            (this.images[i][j].name === `${this.$t("scada.LowerLevelScans")}` &&
              !this.ShowTheNumberOfBottomScans) ||
            (this.images[i][j].name ===
              `${this.$t("scada.DistanceTraveledInCurrentRun")}` &&
              this.ShowTheNumberOfBottomScans)
          ) {
            this.images[i][j].name = `${this.$t(
              "scada.DistanceTraveledInCurrentRun"
            )}`;
          }

          if (
            this.images[i][j].name ===
              `${this.$t("scada.TotalDistanceTraveledByDevice")}` &&
            !this.ShowTheNumberOfBottomScans
          ) {
            this.images[i][j].value = this.dataNum.distanceDial;
          }
        });
      });
    },
    updateSpeedValues(val) {
      const speedMapping = [
        ["speedDial", "sortingMinutesCarShare"],
        ["speedDialDown", "sortingMinutesCarShareDown"],
      ];

      speedMapping.forEach((row, i) => {
        row.forEach((key, j) => {
          this.img[i][j].runSpeed = val[key];
          if (
            !this.isTwo &&
            this.img[i][j].runSpeedName ===
              `${this.$t("scada.UpperLevelRunningSpeed")}`
          ) {
            this.img[i][j].runSpeedName = `${this.$t("scada.RunningSpeed")}`;
          }
          if (
            !this.isTwo &&
            this.img[i][j].runSpeedName ===
              `${this.$t("scada.UpperLevelCartOccupancyRate")}`
          ) {
            this.img[i][j].runSpeedName = `${this.$t(
              "scada.CartOccupancyRate"
            )}`;
          }
        });
      });
    },
  },
};
</script>
  
<style scoped lang="scss">
.grid-container {
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  background-color: #ffffff;
  // margin-right: 1.6rem;
  // width: 100%;
  // padding: 1rem;
  // height: 100%;
  width: 624px;
  // height: 765px;

  @media screen and (max-width: 1550px) and (max-height: 965px) {
    .grid-item {
      * {
        font-size: 10px !important;
      }
      img {
        width: 45px !important;
        height: 45px !important;
      }
    }
  }

  @media screen and (max-width: 1200px) {
    width: 100%;
    margin-right: 0;
    padding: 0.8rem;
  }

  @media screen and (max-width: 768px) {
    width: 100%;
    margin-right: 0;
    padding: 0.5rem;
  }

  @media screen and (max-width: 660px) and (max-height: 1050px) {
    * {
      font-size: 10px !important;
    }
    img {
      width: 40px !important;
      height: 40px !important;
    }
    .item-value {
      font-size: 12px !important;
    }
    .speed-value {
      font-size: 10px !important;
    }
    .speed-info span {
      font-size: 10px !important;
    }
    .grid-row {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .grid-item {
      width: calc(50% - 0.5rem) !important;
      margin: 0.5rem 0;
    }
  }
}

.grid-row {
  display: flex;
  justify-content: space-evenly;
  height: auto;
  min-height: 5.2rem;
  overflow: hidden;
  flex-wrap: wrap;
  // padding: 0.5rem;

  @media screen and (max-width: 768px) {
    // padding: 0.3rem;
  }
}

.grid-item {
  display: flex;
  align-items: center;
  width: calc(33.33% - 1rem);
  margin: 1rem 0rem;
  overflow: hidden;

  img {
    width: clamp(2.5rem, 3.25rem, 4rem);
    height: clamp(2.5rem, 3.25rem, 4rem);
    margin-right: 0.5rem;
    object-fit: contain;
  }

  @media screen and (max-width: 992px) {
    width: calc(50% - 1rem);
  }

  @media screen and (max-width: 576px) {
    width: 100%;
    justify-content: center;
  }
}

.item-info {
  display: flex;
  flex-direction: column;
  text-align: left;
  font-size: clamp(12px, 14px, 16px);
}

.item-value {
  font-size: clamp(1.2rem, 1.6rem, 2rem);
  font-weight: 600;
  color: #2d89ff;
}

.item-name {
  display: block;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.speed-container {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-evenly;
  flex-direction: column;
  width: 624px;

  // margin: 0 0 1.6rem;
  // padding: 0 1rem;

  // @media screen and (max-width: 768px) {
  //   // padding: 0 0.5rem;
  // }
}

.speed-item {
  width: 100%;
  // margin: 1.2rem 0 0;
  display: flex;
  align-items: center;
  overflow: hidden;
  justify-content: space-around;
  flex-wrap: wrap;

  img {
    width: clamp(10rem, 13rem, 15rem);
    height: auto;
    object-fit: contain;
  }

  @media screen and (max-width: 768px) {
    margin: 1rem 0 0;
  }
}

.speed-info {
  display: flex;
  flex-direction: column;
  text-align: center;
  margin-top: 8px;
  position: absolute;
  top: 2rem;
  right: var(--dynamic-right);
  span {
    font-size: clamp(10px, 12px, 14px);
  }
}

.speed-value {
  font-size: clamp(14px, 18px, 22px);
  font-weight: 600;
  color: #2d89ff;
}
</style>