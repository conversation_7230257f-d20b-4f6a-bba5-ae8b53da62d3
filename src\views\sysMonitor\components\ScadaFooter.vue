<template>
  <div
    class="status-bottom"
    style="background-color: rgba(0, 0, 0, 0.5)"
    :style="{
      bottom: bottomPosition,
    }"
  >
    <div class="status-bar">
      <div class="status-row">
        <div class="status-item">
          <span style="color: #7e7e7e"
            >{{ $t("scada.WebSocketStatus") }}:</span
          >
          <span
            class="status-dot"
            style="background-color: rgba(255, 255, 255, 0)"
          ></span>
          <span> {{ webSocketRunText }}</span>
        </div>
        <div class="status-item">
          <span style="color: #7e7e7e"
            >{{ $t("scada.WCSCommunicationStatus") }}:</span
          >
          <span
            class="status-dot"
            style="background-color: rgba(255, 255, 255, 0);margin-left: 0.2rem;"
          ></span>
          <span>{{ wcsRunText }}</span>
        </div>
        <div class="status-item">
          <span style="color: #7e7e7e"
            >{{ $t("scada.CartStatus") }}:</span
          >
          <span>{{ $t("scada.Idle") }}:</span>
          <span
            class="status-dot connected"
            style="background-color: #5ecb80"
          ></span>
        </div>
        <div class="status-item">
          <span>{{ $t("scada.Loaded") }}:</span>
          <span class="status-dot warning"></span>
        </div>
      </div>
      <div class="status-row">
        <div class="status-item">
          <span style="color: #7e7e7e"
            >{{ $t("scada.SlotStatus") }}:</span
          >
          <span>{{ $t("scada.Locked") }}:</span>
          <span class="status-dot disconnected"></span>
        </div>
        <div class="status-item">
          <span>{{ $t("common.Active") }}:</span>
          <span
            class="status-dot"
            style="background-color: #5ecb80"
          ></span>
        </div>
        <div class="status-item">
          <span>{{ $t("scada.FullPackage") }}:</span>
          <span
            class="status-dot pending"
            style="background-color: #8c008b"
          ></span>
        </div>
        <div class="status-item">
          <span>{{ $t("scada.InterceptedItem") }}:</span>
          <span
            class="status-dot active"
            style="background-color: #0101b5"
          ></span>
        </div>
        <div class="status-item">
          <span>{{ $t("scada.ExceptionSlot") }}:</span>
          <span
            class="status-dot black"
            style="background-color: #262626"
          ></span>
        </div>
        <div class="status-item">
          <span>{{ $t("scada.PendingCommunication") }}:</span>
          <span
            class="status-dot neutral"
            style="background-color: #979797"
          ></span>
        </div>
        <div class="status-item">
          <span>{{ $t("scada.Max") }}:</span>
          <span
            class="status-dot disconnected"
            style="background-color: #ffd222"
          ></span>
        </div>
        <div class="status-item">
          <span>{{ $t("common.Cancel") }}:</span>
          <span
            class="status-dot active"
            style="background-color: #4a428f"
          ></span>
        </div>
        <div class="status-item">
          <span>{{ $t("scada.UnProgramme") }}:</span>
          <span
            class="status-dot"
            style="background-color: #82cfff"
          ></span>
        </div>
        <div class="status-item">
          <span>{{ $t("scada.UnThree") }}:</span>
          <span
            class="status-dot"
            style="background-color: #695592"
          ></span>
        </div>
      </div>
    </div>
    <svg
      width="100%"
      height="100px"
      style="
        z-index: 999;
        position: absolute;
        bottom: 2.2rem;
        left: 1rem;
      "
    >
      <g>
        <!-- websocket状态圆点 -->
        <circle
          cx="124"
          :cy="75"
          r="5"
          fill="#FF5161"
          id="run2"
        ></circle>
        <!-- wcs通讯状态圆点 -->
        <circle
          cx="295"
          :cy="75"
          r="5"
          fill="#5ECB80"
          id="run3"
        ></circle>
      </g>
    </svg>
  </div>
</template>

<script>
export default {
  name: 'ScadaFooter',
  props: {
    bottomPosition: {
      type: String,
      default: '0'
    },
    webSocketRunText: {
      type: String,
      default: ''
    },
    wcsRunText: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped>
.status-bottom {
  position: absolute;
  left: 0;
  width: 100%;
  color: #fff;
  padding: 0.5rem;
  z-index: 999;
}
.status-bar {
  display: flex;
  flex-direction: column;
}
.status-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0.5rem;
}
.status-item {
  display: flex;
  align-items: center;
  margin-right: 1rem;
}
.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 0 0.3rem;
}
.connected {
  background-color: #5ecb80;
}
.warning {
  background-color: #ff9900;
}
.disconnected {
  background-color: #ff5161;
}
.pending {
  background-color: #8c008b;
}
.active {
  background-color: #0101b5;
}
.black {
  background-color: #262626;
}
.neutral {
  background-color: #979797;
}
</style> 