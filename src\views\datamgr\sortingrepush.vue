<template>
  <div style="padding:5px">

    <el-form
      :model="form"
      style="margin:15px 0 0 0"
    >
      <el-form-item>
        <span>
          {{ $t('common.Date') }}:
          <el-date-picker
            v-model="form.start"
            :clearable="false"
            type="datetime"
            :placeholder="$t('page.usermanagement.StartDate')"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:185px"
          />
          -
          <el-date-picker
            v-model="form.end"
            :clearable="false"
            type="datetime"
            :placeholder="$t('page.usermanagement.EndDate')"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:185px"
          />
        </span>

        <span style="margin:0 0 0 20px">
          {{ $t('common.ScanType') }}:
          <el-select v-model="form.pushFlag" style="width:120px">
            <el-option
              v-for="item in pushFlagOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </span>

        <span style="margin:0 0 0 20px">
          {{ $t('common.PlanFlag') }}:
          <el-select v-model="form.planFlag" style="width:80px">
            <el-option
              v-for="item in planFlagOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </span>
        <span style="margin:0 0 0 20px">
          {{ $t('common.Code') }}:
          <el-input v-model="form.barCode" style="width:200px" />
        </span>

        <span style="margin: 0 0 0 20px">
        {{$t('common.SystemType')}}:
          <el-select v-model="systemTypeCode" style="width:120px">
            <el-option
              v-for="item in systemOptions"
              :key="item.dbCode"
              :label="item.dbName"
              :value="item.dbCode"
            />
          </el-select>
        </span>

        <span style="margin:0 0 0 20px">
          <el-button-group>
            <el-button style="margin-right:15px" icon="el-icon-search" @click="setloadData" type="primary">{{ $t('common.Select') }}</el-button>
            <el-button icon="el-icon-refresh-left" @click="resetForm" type="warning">{{ $t('common.Reset') }}</el-button>
          </el-button-group>
        </span>
      </el-form-item>
      <el-form-item>
        <el-button-group>
          <el-button style="margin-right:15px" icon="el-icon-download" @click="exportDataEvent">{{ $t('common.Export') }}</el-button>
          <!-- <el-button icon="el-icon-upload" @click="uploadDataEvent" type="info">补推上传失败数据</el-button> -->
        </el-button-group>
      </el-form-item>
    </el-form>
    <!-- <template style="margin-bottom:15px">
      <vxe-button icon="vxe-icon--download" @click="exportDataEvent">导出数据</vxe-button>
      <vxe-button icon="vxe-icon--upload" @click="uploadDataEvent">补推上传失败数据</vxe-button>
    </template> -->

    <vxe-table
      ref="xTable"
      resizable
      show-overflow
      :height="tableHeight"
      row-id="id"
      :loading="loading"
      :data="tableData"
    >
      <vxe-table-column :resizable="true" type="seq" :title="$t('common.SerialNumber')" width="120" />
      <vxe-table-column :resizable="true" field="businessDate" :title="$t('common.Date')" />
      <vxe-table-column :resizable="true" field="barCode" :title="$t('common.Code')" />
      <vxe-table-column :resizable="true" field="weight" :title="$t('common.Weight')" />
      <vxe-table-column :resizable="true" field="chuteNo" :title="$t('common.Chute')" />
      <vxe-table-column :resizable="true" field="nextStopCode" :title="$t('common.NextStationNumber')" />
      <vxe-table-column :resizable="true" field="packageNumber" :title="$t('common.PacketNumber')" />
      <vxe-table-column :resizable="true" field="rfid" :title="$t('common.Rfid')" />
      <vxe-table-column :resizable="true" field="pushFlag" :title="$t('common.ScanType')" />
      <vxe-table-column :resizable="true" field="planFlag" :title="$t('common.PlanFlag')" />
      <vxe-table-column :resizable="true" field="message" :title="$t('common.Message')" />
    </vxe-table>

    <vxe-pager
      background
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.totalResult"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    />
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'SortingRepush',
  data() {
    return {
      pushFlagOptions: [
        { value: '', label: this.$t('common.all') },
        { value: '1', label: this.$t('common.UnloadingToDeliveryScanning') },
        { value: '2', label: this.$t('common.BuildingPackageScanning') }
      ],
      planFlagOptions: [
        { value: '', label: this.$t('common.all') },
        { value: '1', label: this.$t('common.Departure')},
        { value: '2', label: this.$t('common.Arrival') }
      ],
      tableHeight: window.innerHeight - 245,
      loading: false,
      form: {
        start: this.getDate(0),
        end: this.getDate(1),
        deviceFlag: '',
        pushFlag: '',
        planFlag: ''
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      },
      systemOptions:this.getBoxValue(),
      systemTypeCode:"1"
    }
  },
  methods: {
    getDate(format) {
      var today = new Date()
      var year = today.getFullYear()
      var month = today.getMonth() + 1
      var date = today.getDate()

      if (format === 0) {
        return year + '-' + month + '-' + date + ' 00:00:00'
      } else if (format === 1) {
        return year + '-' + month + '-' + date + ' 23:59:59'
      }
    },
    setloadData() {
      this.tablePage.currentPage = 1
      this.loadData()
    },
    loadData() {
      this.loading = true
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

      let url = '/log/repush/list'
      if (type == 'dws'){
          url = '/dws/log/repush/list'
      }
      if (type == 'ffs'){
          url = '/ffs/log/repush/list'
      }
      return request({
        url: url,
        method: 'post',
        data: { startTime:this.form.start,endTime: this.form.end,pushFlag: this.form.pushFlag,planFlag: this.form.planFlag,barcode: this.form.barCode, pageSize: this.tablePage.pageSize,curPage : this.tablePage.currentPage,dbCode:typeCode }
      }).then(resp => {
        if (typeof (resp.data.result) !== 'undefined') {
          resp.data.result.records.forEach(element => {
            switch (element.pushFlag) {
              case '1': { element.pushFlag = '卸车到件扫描'; break }
              case '2': { element.pushFlag = '建包扫描'; break }
            }
          })

          resp.data.result.records.forEach(element => {
            switch (element.planFlag) {
              case '0': { element.planFlag = '综合'; break }
              case '1': { element.planFlag = '出港'; break }
              case '2': { element.planFlag = '进港'; break }
            }
          })
        }
        this.tableData = resp.data.result.records
        this.tablePage.currentPage = resp.data.result.current
        this.tablePage.pageSize = resp.data.result.size
        this.tablePage.totalResult = resp.data.result.total
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || '加载失败！',
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.loadData()
    },
    resetForm() {
      this.form = {
        start: this.getDate(0),
        end: this.getDate(1),
        pushFlag: '',
        planFlag: ''
      }
      this.tableData = []
      this.tablePage = {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      }
    },
    exportDataEvent() {
      this.loading = true
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;
      console.log(type,'测试数据')

      let url = '/log/repush/export'
      if (type == 'dws'){
          url = '/dws/log/repush/export'
      }
      if (type == 'ffs'){
          url = '/ffs/log/repush/export'
      }
      return request({
        url: url,
        method: 'post',
        data: { startTime:this.form.start,endTime: this.form.end,pushFlag: this.form.pushFlag,planFlag: this.form.planFlag,barcode: this.form.barCode,dbCode:typeCode },
        responseType: 'blob'
      }).then(resp => {
        const blob = new Blob([resp], { type: 'application/vnd.ms-excel' })
        const a = document.createElement('a')
        a.href = URL.createObjectURL(blob)
        a.download = 'SupplementaryPushData.xlsx'
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(a.href)
        document.body.removeChild(a)
        this.$message({
          message: this.$t('common.BeginExport'),
          type: 'success',
          duration: 2 * 1000
        })
        // this.$refs.xTable.exportData({
        //   filename: '补推数据',
        //   type: 'csv',
        //   original: true,
        //   data: resp.data.result
        // })
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || 'Export failed！',
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    uploadDataEvent() {
      this.$confirm('Please confirm whether to push again【' + this.form.start + '  to  ' + this.form.end + '】Failed to upload data during the time period？', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(() => {
        this.loading = true
        return request({
          url: '/sortingrepush/uploaddata',
          method: 'post',
          data: { startTime:this.form.start,endTime: this.form.end}
        }).then(resp => {
          this.loading = false
          if (resp.data > 0) {
            this.$alert('Successfully distributed the push task, totaling【' + resp.data + '】Failed upload data', 'prompt', {
              confirmButtonText: 'Yes',
              type: 'success'
            })
          } else {
            this.$message({
              message: 'No upload failure data found',
              type: 'warning',
              duration: 2 * 1000
            })
          }
        }).catch(error => {
          this.loading = false
          this.$message({
            message: 'Failed to issue supplementary push task！' + error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      })
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params:{paramName: 'ScadaConnInfo'}
      }).then((res) => {
        console.log(res,'测试')
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    },
  }
}
</script>
