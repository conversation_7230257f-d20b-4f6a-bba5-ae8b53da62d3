<template>
  <div class="detail-dialog">
    <div class="dialog-content">
      <!-- 根据实际需求填充内容 -->
      <div v-if="loading" class="loading">
        <el-spinner></el-spinner>
      </div>
      <div v-else>
        <div class="detail-item" v-for="(value, key) in detailData" :key="key">
          <span class="label">{{ key }}:</span>
          <span class="value">{{ value }}</span>
        </div>
      </div>
    </div>
    <div class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DetailDialog',
  props: {
    id: {
      type: [String, Number],
      required: true
    },
    // 其他需要的props
  },
  data() {
    return {
      loading: true,
      detailData: {}
    };
  },
  created() {
    // 加载数据
    this.fetchData();
  },
  methods: {
    fetchData() {
      // 模拟数据加载
      setTimeout(() => {
        this.detailData = {
          编号: this.id,
          状态: '正常',
          创建时间: new Date().toLocaleString(),
          // 其他数据字段
        };
        this.loading = false;
      }, 500);
    },
    handleClose() {
      this.$emit('close');
    },
    handleConfirm() {
      this.$emit('success', this.detailData);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../styles/scada-dialogs.scss';
</style> 