// 越南语
const vi = {
  login: {
    changelanguage: "Chuyển đổi ngôn ngữ",
    index: "I'm six",
    title: "<PERSON><PERSON> thống tra cứu phân loại COGY",
    Success: "<PERSON><PERSON><PERSON> nhập thành công",
    plsuaername: "<PERSON><PERSON> lòng nhập tên người dùng",
    plspassword: "<PERSON>ui lòng nhập mật khẩu",
    plscode: "<PERSON>ui lòng nhập mật khẩu mã xác nhận",
    login: "<PERSON>ăng nhập",
    code: "Mã xác nhận",
    chinese: "中文",
    english: "english",
    thai: "ภาษาไทย",
    ms: "Bahasa Melayu",
    vi: "Tiếng Việt",
    es: "Español",
    pt: "Bahasa Portugis",
    id: "Bahasa Indonesia",
    ph: "Filipino",
    SwipeRight: "<PERSON>u<PERSON><PERSON> sang phải",
  },
  welcome: {
    title: "<PERSON><PERSON><PERSON> mừng bạn sử dụng hệ thống tra cứu nhật ký phân loại",
  },
  headers: {
    title: "<PERSON>ệ thống tra cứu dữ liệu phân loại",
    loginout: "Thoát hệ thống",
  },
  sidebar: {
    Home: "Trang chủ",
    Syscfg: "Cấu hình hệ thống",
    Userinfosup: "Quản lý người dùng cung cấp bao bì",
    User: "Quản lý người dùng",
    Role: "Quản lý vai trò",
    Menu: "Quản lý menu",
    Sysparam: "Tham số hệ thống",
    Errchute: "Khe bất thường",
    Sysurl: "Số ngày lưu trữ nhật ký",
    Sortingplan: "Kế hoạch phân loại",
    Datamgr: "Quản lý dữ liệu",
    Runninglog: "Nhật ký hoạt động",
    Locklog: "Nhật ký khóa",
    Sortinglog: "Nhật ký phân loại",
    Packagenumberlog: "Mã bao bì lịch sử",
    Sortingrepush: "Dữ liệu bổ sung",
    Sortingreport: "Thống kê báo cáo",
    Chutereport: "Lượng phân loại tại khe",
    Sortingcountsup: "Thống kê số lượng (bàn cung cấp gói)",
    Sortingcountuser: "Thống kê số lượng (số hiệu công nhân)",
    Packagenumbercount: "Thống kê tổng bao bì",
    Sortingefficiency: "Thống kê hiệu suất",
    Errorreport: "Thống kê loại",
    Monitor: "Giám sát hệ thống",
    Scada: "Giám sát SCADA",
    Job: "Nhiệm vụ định kỳ",
    Config: "Cài đặt tham số",
    Dict: "Quản lý từ điển",
  },
  page: {
    homepage: {
      boardingcount: "Số lượng xe nhỏ",
      scancount: "Số lượng quét",
      cellcount: "Số lượng rơi vào giá",
      cancelleditems: "Hủy bỏ hàng",
      rabbitinterceptions: "Tổng số hàng bị chặn của J&T",
      unmatchedcodes: "Mã ba đoạn chưa khớp khe",
      overbounds: "Tổng số vòng vượt",
      anomalies: "Tổng số bất thường",
      sortingvolume: "Lượng phân loại",
      monday: "Thứ Hai",
      tuesday: "Thứ Ba",
      wednesday: "Thứ Tư",
      thursday: "Thứ Năm",
      friday: "Thứ Sáu",
      saturday: "Thứ Bảy",
      sunday: "Chủ Nhật",
    },
    Serviceusermanagement: {
      Refresh: "Làm mới",
      AddUser: "Thêm người dùng mới",
      BulkDelete: "Xóa theo lô",
      FullName: "Tên",
      EmployeeID: "Số hiệu công nhân",
      Query: "Tra cứu",
      Import: "Nhập khẩu",
      OnlyExcel: "(Chỉ có thể nhập khẩu tệp định dạng excel.xlsx)",
      SerialNumber: "Số thứ tự",
      UserEmployeeID: "Mã số công nhân người dùng",
      UserName: "Tên người dùng",
      UserID: "ID người dùng",
      Creator: "Người tạo",
      CreationTime: "Thời gian tạo",
      Operation: "Thao tác",
      UserPassword: "Mật khẩu người dùng",
      ConfirmPassword: "Xác nhận mật khẩu",
      NewPassword: "Mật khẩu mới",
      Save: "Lưu",
      Cancel: "Hủy bỏ",
      UpdateUser: "Người sửa đổi",
    },
    usermanagement: {
      UserID: "Mã số người dùng",
      UserName: "Tên người dùng",
      UserAccount: "Tài khoản người dùng",
      PlaseUserAccount: "Vui lòng nhập tài khoản người dùng",
      PhoneNumber: "Số điện thoại di động",
      PleasePhoneNumber: "Vui lòng nhập số điện thoại di động",
      Status: "Trạng thái",
      PleaseStatus: "Vui lòng nhập trạng thái",
      StartDate: "Ngày bắt đầu",
      EndDate: "Ngày kết thúc",
      CreationTime: "Thời gian tạo",
      Operation: "Thao tác",
      UserGender: "Giới tính người dùng",
      UserNickname: "Biệt danh người dùng",
      Email: "Hộp thư điện tử",
      UserPassword: "Mật khẩu người dùng",
      Active: "Bình thường",
      Inactive: "Ngừng sử dụng",
      Role: "Vai trò",
      Note: "Ghi chú",
      Search: "Tìm kiếm",
      Reset: "Đặt lại",
      Add: "Thêm mới",
      Delete: "Xóa",
      Update: "Sửa đổi",
      More: "Thêm",
      ResetPassword: "Đặt lại mật khẩu",
    },
  },
  common: {
    DerivedData: "Xuất dữ liệu",
    ViewDWSPeakEffect: "Xem hiệu suất cực đại DWS",
    peakEffect: "Hiệu suất cực đại",
    SearchData: "Dữ liệu tìm kiếm",
    NoData: "Chưa có dữ liệu",
    Arrival: "Vào cảng",
    Departure: "Xuất cảng",
    ArrivalAndDeparture: "Vào và ra cảng",
    CloseOther: "Đóng các cái khác",
    CloseAll: "Đóng tất cả",
    RoleName: "Tên vai trò",
    PlaseRoleName: "Vui lòng nhập tên vai trò",
    PermissionCharacters: "Ký tự quyền hạn",
    PlasePermissionCharacters: "Vui lòng nhập ký tự quyền hạn",
    Order: "Thứ tự",
    Status: "Trạng thái",
    Search: "Tìm kiếm",
    Reset: "Đặt lại",
    Delete: "Xóa",
    Update: "Sửa đổi",
    More: "Thêm",
    CreationTime: "Thời gian tạo",
    Operation: "Thao tác",
    Update: "Sửa đổi",
    Add: "Thêm mới",
    UpdateCache: "Cập nhật bộ nhớ cache",
    Select: "Tra cứu",
    Import: "Nhập khẩu",
    Export: "Xuất khẩu",
    SerialNumber: "Số thứ tự",
    RoleNumber: "Mã số vai trò",
    Memo: "Ghi chú",
    UpdateDate: "Thời gian cập nhật",
    ParameterName: "Tên tham số",
    ParameterValue: "Giá trị tham số",
    PleaseSelectARole: "Vui lòng chọn vai trò",
    PleaseInputnickname: "Vui lòng nhập biệt danh người dùng",
    PleaseInputPhoneNumber: "Vui lòng nhập số điện thoại di động",
    PleaseInputEmail: "Vui lòng nhập email",
    PleaseInputUserName: "Vui lòng nhập tên người dùng",
    PleaseInputPassWord: "Vui lòng nhập mật khẩu",
    RoleStatus: "Trạng thái vai trò",
    RoleCode: "Mã số người dùng",
    PlaseInputRoleCode: "Vui lòng nhập mã người dùng",
    CreatorID: "Mã người tạo",
    Determine: "Xác nhận",
    Cancellation: "Hủy bỏ",
    Task: "Tên nhiệm vụ",
    PlaseTask: "Vui lòng nhập tên nhiệm vụ",
    TaskCode: "Mã số nhiệm vụ",
    PlaseTaskCode: "Vui lòng nhập mã số nhiệm vụ",
    MenuName: "Tên menu",
    PlaseMenuName: "Vui lòng nhập tên menu",
    MenuStatus: "Trạng thái menu",
    ExpandAndCollapse: "Mở rộng/Giảm bớt",
    SelectAllDonteSelectAll: "Chọn tất cả/Chọn không",
    ParentChildLinkage: "Liên kết cha con",
    MenuPermissions: "Quyền truy cập menu",
    PleaseEnterContent: "Vui lòng nhập nội dung",
    Icon: "Biểu tượng",
    ComponentPath: "Đường dẫn linh kiện",
    SchemeID: "ID kế hoạch",
    ModeType: "Loại phân loại",
    PlanName: "Tên kế hoạch",
    PlanCode: "Mã số kế hoạch",
    PlanDesc: "Mô tả kế hoạch",
    PlanFlag: "Loại kế hoạch",
    IsSelected: "Có kích hoạt không",
    UpdateDate: "Thời gian sửa đổi",
    Detail: "Chi tiết",
    Enable: "Kích hoạt",
    Date: "Thời gian",
    Grade: "Cấp bậc",
    Source: "Nguồn gốc",
    Message: "Tin nhắn",
    ExtraData: "Dữ liệu bổ sung",
    plcType: "Tầng",
    Number: "Mã số",
    Component: "Bộ phận",
    Chute: "Khe (lỗ) xếp hàng",
    Supply: "Bàn cung cấp gói",
    dwsNo: "Mã số DWS",
    BigBarRate: "Tỷ lệ gói lớn",
    Quantity: "Số lượng",
    SmallBarRate: "Tỷ lệ bao nhỏ",
    ExceptionCode: "Mã bất thường",
    Code: "Số đơn hàng",
    ScanTime: "Thời gian quét",
    BoardingTime: "Thời gian lên xe",
    DropTime: "Thời gian rơi vào giá",
    NextPieceTime: "Thời gian đóng gói",
    passBackTime: "Thời gian phản hồi",
    arrivalTime: "Thời gian hàng đến",
    PacketNumber: "Mã bao bì",
    TimeType: "Loại thời gian",
    turns: "Số vòng",
    Image: "Hình ảnh",
    UpperLevel: "Cấp trên",
    LowerLevel: "Cấp dưới",
    LayerNumber: "Số tầng",
    PleaseEnterTheDictionaryLabel: "Vui lòng nhập nhãn từ điển",
    DictionaryName: "Tên từ điển",
    DictionaryId: "Mã từ điển",
    DictionaryType: "Loại từ điển",
    PleaseEnterTheDictionaryType: "Vui lòng nhập loại từ điển",
    PleaseEnterTheDictionaryName: "Vui lòng nhập tên từ điển",
    BagBindingBfficer: "Người đóng gói",
    DictionaryEncoding: "Mã từ điển",
    DictionaryTag: "Nhãn từ điển",
    DictionaryValue: "Giá trị từ điển",
    DictionarySort: "Sắp xếp từ điển",
    DictionaryRemark: "Ghi chú",
    DictionaryCreateTime: "Thời gian tạo",
    DataTag: "Nhãn dữ liệu",
    PleaseEnterTheDataLabel: "Vui lòng nhập nhãn dữ liệu",
    DataKey: "Giá trị dữ liệu",
    StyleAttribute: "Thuộc tính kiểu",
    DisplayOrder: "Thứ tự hiển thị",
    EchoStyle: "Kiểu hiển thị phản hồi",
    ListClass: "Kiểu danh sách",
    PleaseEnterTheDataKey: "Vui lòng nhập giá trị dữ liệu",
    PleaseEnterTheStyleAttribute: "Vui lòng nhập thuộc tính kiểu",
    PleaseEnterTheDisplayOrder: "Vui lòng nhập thứ tự hiển thị",
    PleaseEnterTheEchoStyle: "Vui lòng nhập kiểu hiển thị phản hồi",
    PleaseEnterTheListClass: "Vui lòng nhập kiểu danh sách",
    PleaseEnterTheRemark: "Vui lòng nhập ghi chú",
    PleaseEnterTheContent: "Vui lòng nhập nội dung",
    TheAddressNeedsToBehttp:
      "Nếu chọn liên kết ngoài, địa chỉ cần bắt đầu bằng `http(s)://`",
    TheAddressNeedsToBehttpUser:
      "Địa chỉ truy cập, như: `user`, nếu địa chỉ ngoài mạng cần truy cập nội mạng, bắt đầu bằng `http(s)://`",
    TheAddressNeedsToBehttpCatalogue:
      "Đường dẫn truy cập thành phần, như: `system/user/index`, mặc định trong thư mục `views`",
    TheDefaultPassingParametersForTheRoute:
      "Tham số mặc định truyền cho đường dẫn, như: `{'id': 1, 'name': 'ry'}`",
    TheComponentWillBeCachedByKeepAlive:
      "Nếu chọn, sẽ được lưu trữ bởi `keep-alive`, cần phù hợp với `name` của thành phần và địa chỉ",
    SelectHiddenThenTheRoute:
      "Nếu chọn ẩn, đường dẫn sẽ không xuất hiện trên thanh bên, nhưng vẫn có thể truy cập",
    SelectDisableThenTheRouteSidebar:
      "Nếu chọn vô hiệu hóa, đường dẫn sẽ không xuất hiện trên thanh bên, cũng không thể truy cập",
    PleaseEnterTheRouteParameters: "Vui lòng nhập tham số đường dẫn",
    Yes: "Có",
    No: "Không",
    PermissionCharactersString: "Chuỗi quyền hạn được định nghĩa trong controller, ví dụ: @PreAuthorize(`@ss.hasRole('admin')`)",
    Cache1: "Lưu trữ",
    NoCache: "Không lưu trữ",
    AddUser: "Thêm người dùng",
    BatchCancelAuthorization: "Hủy ủy quyền hàng loạt",
    Close: "Đóng",
    CancelAuthorization: "Hủy ủy quyền",
    View: "Xem",
    UserType: "Loại người dùng",
    PleaseSelectUserType: "Vui lòng chọn loại người dùng",
    Forward: "Chuyển tiếp",
    Reverse: "Điều chỉnh ngược",
    Lock: "Khóa",
    Unlock: "Mở khóa",
    SendMessage: "Gửi tin nhắn",
    TaskGroup: "Tên nhóm nhiệm vụ",
    PleaseSelectTaskGroup: "Vui lòng chọn tên nhóm nhiệm vụ",
    TaskStatus: "Trạng thái nhiệm vụ",
    PleaseSelectTaskStatus: "Vui lòng chọn trạng thái nhiệm vụ",
    Log: "Nhật ký",
    InvokeTarget: "Chuỗi mục tiêu gọi",
    CronExpression: "Biểu thức cron",
    ExecuteOnce: "Chạy một lần",
    TaskDetails: "Chi tiết nhiệm vụ",
    DispatchLog: "Nhật ký phân phát",
    InvokeTarget: "Phương thức gọi",
    BeanCallExample: "Ví dụ gọi Bean: ryTask.ryParams('ry')",
    ClassCallExample:
      "Ví dụ gọi lớp: com.ruoyi.quartz.task.RyTask.ryParams('ry')",
    ParameterDescription:
      "Mô tả tham số: hỗ trợ chuỗi, kiểu布尔, kiểu dài số nguyên, kiểu số thực, kiểu số nguyên",
    PleaseInputInvokeTarget: "Vui lòng nhập chuỗi mục tiêu gọi",
    PleaseInputCronExpression: "Vui lòng nhập biểu thức cron",
    GenerateExpression: "Tạo biểu thức",
    ExecuteStrategy: "Chiến lược chạy",
    MisfirePolicy: "Chiến lược chạy",
    ImmediateExecution: "Chạy ngay lập tức",
    DelayExecution: "Chạy sau đó",
    AbandonExecution: "Bỏ chạy",
    PleaseSelectExecuteStrategy: "Vui lòng chọn chiến lược chạy",
    Concurrent: "Chạy đồng thời",
    Allow: "Cho phép",
    Prohibit: "Cấm",
    PleaseSelectConcurrent: "Vui lòng chọn chạy đồng thời",
    CronExpressionGenerator: "Máy tạo biểu thức cron",
    NextExecutionTime: "Thời gian chạy tiếp theo",
    TaskDetails: "Chi tiết nhiệm vụ",
    TaskGroup1: "Nhóm nhiệm vụ",
    DefaultStrategy: "Chiến lược mặc định",
    ExecuteStatus: "Trạng thái chạy",
    PleaseSelectExecuteStatus: "Vui lòng chọn trạng thái chạy",
    ExecutionTime: "Thời gian chạy",
    PleaseSelectExecutionTime: "Vui lòng chọn thời gian chạy",
    Clear: "Xóa",
    JobLogId: "Mã nhật ký",
    JobMessage: "Thông tin nhật ký",
    Detail1: "Chi tiết",
    DispatchLogDetails: "Chi tiết nhật ký phân phát",
    PleaseSelect: "Vui lòng chọn",
    SelectStartTime: "Chọn thời gian bắt đầu",
    SelectEndTime: "Chọn thời gian kết thúc",
    DebugStart: "Chạy thử (bắt đầu)",
    DebugClose: "Chạy thử (đóng)",
    ClearPacketNumber: "Xóa số gói",
    Upload: "Tải lên",
    TotalQuery: "Tìm thấy tổng cộng",
    property: "tấm hình",
    all: "tất cả",
    UnloadingToDeliveryScanning: "Quét xuất xe đến kiện",
    BuildingPackageScanning: "Quét xây gói",

    SelectUser: "Chọn người dùng",
    InvokeTargetMethod: "Gọi phương thức mục tiêu",
    PleaseSelectTaskGroup: "Vui lòng chọn nhóm nhiệm vụ",
    CronExpression1: "cron biểu thức",
    ExceptionInfo: "Thông tin ngoại lệ",

    Edit: "Sửa",
    ScopeOfAuthority: "Phạm vi quyền hạn",
    DataPermission: "Quyền hạn dữ liệu",
    Confirm: "Xác nhận",
    StartDate: "Ngày bắt đầu",
    EndDate: "Ngày kết thúc",
    Weight: "Trọng lượng",
    length: "Dài",
    width: "Chiều rộng",
    heigth: "Cao",
    CarNumber: "Số hiệu xe nhỏ",
    RequestedGate: "Khe nhận được yêu cầu",
    PhysicalGrid: "Khe vật lý trả về từ PLC",
    taskNo: "Mã số nhiệm vụ",
    TerminalDispatchCode: "Mã ba đoạn",
    FallingGridTime: "Thời gian rơi vào giá",
    PackageGrade: "Mã bao bì",
    ChipNumber: "Số chip",
    BindingTime: "Thời gian đóng gói",
    BindingPersonnel: "Nhân viên đóng gói",
    ScanType: "Loại quét",
    NextStationNumber: "Mã số trạm tiếp theo",
    Rfid: "Bao bì điện tử",
    TimeInterval: "Khoảng thời gian",
    SortingQuantity: "Số lượng phân loại",
    TotalSortingWeight: "Tổng trọng lượng phân loại (KG)",
    NumberOfScannedBarcodeRecords: "Số lượng bản ghi quét mã vạch",
    RecordTheNumberOfPackagesLoadedOntoTheVehicle:
      "Số lượng bản ghi hàng lên xe",
    NumberOfDropFeedbackRecords: "Số lượng bản ghi phản hồi khe",
    scanQuantity: "Số lượng quét",
    arrivalQuantity: "Số lượng hàng đến",
    passBackQuantity: "Số lượng phản hồi",
    TotalNumberOfPackages: "Tổng số bao bì",
    Packagenumb: "Mã bao bì (Số lượng hàng trong khoảng thời gian này)",
    Type: "Loại",
    Count: "Số lượng",
    SystemType: "Loại hệ thống",
    EmployeeID: "Vui lòng nhập số hiệu công nhân",
    FullName: "Vui lòng nhập tên",
    Password: "Vui lòng nhập mật khẩu",
    ConfirmPassword: "Vui lòng nhập lại mật khẩu",
    SelectGender: "Vui lòng chọn giới tính",
    Active: "Bình thường",
    Inactive: "Ngừng sử dụng",
    Male: "Nam",
    Female: "Nữ",
    Unknown: "Không xác định",
    RoleOrder: "Thứ tự vai trò",
    Show: "Hiển thị",
    Hide: "Ẩn",
    Default: "Mặc định",
    System: "Hệ thống",
    Success: "Thành công",
    Failure: "Thất bại",
    AddMenu: "Thêm menu",
    EditMenu: "Sửa menu",
    ParentMenu: "Menu cấp trên",
    MenuType: "Loại menu",
    Directory: "Mục lục",
    Menu: "Menu",
    Button: "Nút bấm",
    MenuIcon: "Biểu tượng menu",
    SelectIcon: "Chọn biểu tượng",
    RouteAddress: "Địa chỉ định tuyến",
    DisplayOrder: "Sắp xếp hiển thị",
    ExternalLink: "Có phải liên kết ngoài không",
    DisplayStatus: "Trạng thái hiển thị",
    MenuStatus: "Trạng thái menu",
    RouteParameters: "Tham số định tuyến",
    Cache: "Có phải là bộ nhớ đệm không",
    ComponentPath: "Đường dẫn linh kiện",
    AddRole: "Thêm vai trò",
    EditRole: "Sửa vai trò",
    AddPlan: "Thêm mới kế hoạch",
    Cancel: "Hủy bỏ",
    FirstSegmentCode: "Mã đoạn đầu tiên",
    SecondSegmentCode: "Mã đoạn thứ hai",
    ThirdSegmentCode: "Mã đoạn thứ ba",
    NextStopCode: "Mã số trạm tiếp theo",
    NextStopName: "Tên trạm tiếp theo",
    ModificationTime: "Thời gian sửa đổi",
    BulkDelete: "Xóa theo lô",
    AddDetails: "Chi tiết thêm mới",
    ByServicePackage: "Theo bàn cung cấp gói",
    ByDwsNo: "Theo số thứ tự DWS",
    Granularity: "Mức độ chi tiết",
    LogicCode: "logic",
    PacketType: "Loại bao bì",
    IsUpload: "Có tải lên không",
    AddSuccess: "Thêm thành công",
    EditSuccess: "Sửa thành công",
    DelSuccess: "Xóa thành công",
    ImportSuccessful: "Nhập khẩu thành công",
    BeginExport: "Bắt đầu xuất khẩu",
    ModificationFailed: "Sửa đổi thất bại",
    AddFailed: "Thêm mới thất bại",
    OperationSuccessful: "Thao tác thành công",
    OperationFailed: "Thao tác thất bại",
    OperationCancellation: "Hủy thao tác",
    ExportFailed: "Xuất khẩu thất bại",
    LoginOut:
      "Bạn đã bị đăng xuất, có thể hủy bỏ và tiếp tục ở lại trang này hoặc đăng nhập lại",
    ConfirmLogout: "Xác nhận đăng xuất",
    LogAgain: "Đăng nhập lại",
    Remark: "Ghi chú",
    DwsNo: "Số thứ tự DWS",
    Notempty: "Tài khoản, mật khẩu hoặc mã xác nhận không được để trống!",
    Notpassword: "Tài khoản và mật khẩu không được để trống!",
    Id: "Khóa chính tham số",
    Parameter: "Tên tham số",
    PlParameter: "Vui lòng nhập tên tham số",
    ParameterKey: "Tên khóa tham số",
    PlParameterKey: "Vui lòng nhập tên khóa tham số",
    ParameterValue: "Giá trị khóa tham số",
    PlParameterValue: "Vui lòng nhập giá trị khóa tham số",
    Group: "Tên nhóm",
    PlGroup: "Vui lòng chọn tên nhóm",
  },
  scada: {
    DeviceRunningStatus: "Trạng thái hoạt động của thiết bị",
    DeviceStopped: "Thiết bị ngừng hoạt động",
    DeviceRunning: "Thiết bị đang hoạt động",
    StartTime: "Thời gian bắt đầu hoạt động",
    RunningSpeed: "Tốc độ hoạt động",
    CartOccupancyRate: "Tỷ lệ chiếm dụng xe nhỏ",
    TotalDistanceTraveledByDevice: "Tổng số km hoạt động của thiết bị",
    DistanceTraveledInCurrentRun: "Số km chạy lần này",
    TotalDistanceTraveled: "Tổng số km đã chạy",
    Scans: "Số lần quét",
    PendingStart: "Đang chờ bắt đầu hoạt động",
    FullScreen: "Hiển thị toàn màn hình",
    ExitFull: "Thoát chế độ toàn màn hình",
    UpperLevelRunningSpeed: "Tốc độ hoạt động cấp trên",
    UpperLevelDeviceRunningStatus: "Trạng thái hoạt động thiết bị cấp trên",
    UpperLevelStartTime: "Thời gian bắt đầu hoạt động của cấp trên",
    UpperLevelCartOccupancyRate: "Tỷ lệ chiếm dụng xe nhỏ cấp trên",
    UpperLevelByDevice: "Tổng số km hoạt động của thiết bị cấp trên",
    LowerLevelDeviceRunningStatus: "Trạng thái hoạt động thiết bị tầng dưới",
    UpperLayerPLCDisconnect: "Kết nối PLC cấp trên bị ngắt bất thường",
    LowerLevelPLCDisconnect: "Kết nối PLC tầng dưới bị gián đoạn bất thường",
    UpperLayerPLCConnectionStatus: "Trạng thái kết nối PLC cấp trên",
    LowerLevelPLCConnectionStatus: "Trạng thái kết nối PLC tầng dưới",
    LowerLevelStartTime: "Thời gian bắt đầu hoạt động tầng dưới",
    LowerLevelRunningSpeed: "Tốc độ hoạt động tầng dưới",
    LowerLevelCartOccupancyRate: "Tỷ lệ chiếm dụng xe nhỏ tầng dưới",
    LowerLevelTotalDistanceTraveledByDevice:
      "Tổng số km hoạt động thiết bị tầng dưới",
    UpperLevelScans: "Số lần quét cấp trên",
    LowerLevelScans: "Số lần quét tầng dưới",
    AbnormalQuantity: "Lượng bất thường",
    NumberOfSlotsOccupied: "Số lượng rơi vào giá",
    FailedRepushQuantity: "Số lần bổ sung thất bại",
    InterceptedQuantity: "Số lượng bị chặn",
    ExcessiveCirclesQuantity: "Số lần vượt vòng",
    UnconfiguredThreeSegmentCodeSlots: "Khe chưa cấu hình mã ba đoạn",
    ComprehensiveExceptionSlots: "Khe bất thường tổng hợp",
    CancelledItems: "Hủy bỏ hàng",
    UnobtainedThreeSegmentCodeInformation:
      "Chưa nhận được thông tin mã ba đoạn",
    WebSocketStatus: "Trạng thái WebSocket",
    WCSCommunicationStatus: "Trạng thái giao tiếp WCS",
    Connected: "Đã kết nối",
    NotConnected: "Chưa kết nối",
    SortingStatus: "Trạng thái phân loại",
    Idle: "Rảnh",
    Loaded: "Tải hàng",
    CartStatus: "Trạng thái xe nhỏ",
    LittleThingsAreQuick:'Nhỏ nhanh chóng',
    Headscratcher: 'Sau-lơ trên đỉnh',
    OnLine:'Trực tuyến',
    Offline:"ngoại tuyến",
    Locked: "Khóa",
    FullPackage: "Gói đầy",
    SlotStatus: "Trạng thái khe",
    InterceptedItem: "Hàng bị chặn",
    ExceptionSlot: "Khe bất thường",
    PendingCommunication: "Đang chờ giao tiếp",
    Max: "Vòng lặp tối đa",
    Cancellation: "Hủy bỏ hàng",
    UnProgramme: "Chưa cấu hình kế hoạch",
    UnThree: "Chưa cấu hình mã ba đoạn",
    CartOperation: "Thao tác xe nhỏ",
    OneKeyUnlock: "Mở khóa khe bằng một cú nhấp chuột",
    OneKeyLockSlot: "Khóa khe bằng một cú nhấp chuột",
    CartNumber: "Số hiệu xe nhỏ",
    FloorNumber: "Số tầng",
    UpperLevel: "Cấp trên",
    LowerLevel: "Tầng dưới",
    Lock: "Khóa",
    Unlock: "Mở khóa",
    ManualSwitchStart: "Chuyển đổi thủ công (khởi động)",
    ManualSwitchClose: "Chuyển đổi thủ công (đóng)",
    Run: "Hoạt động",
    Close: "Đóng",
    DisabledList: "Danh sách bị vô hiệu hóa",
    AbnormalAlarm: "Cảnh báo bất thường",
    DisableCart: "Vô hiệu hóa xe nhỏ",
    UpperLevelCart: "Xe nhỏ cấp trên",
    LowerLevelCart: "Xe nhỏ tầng dưới",
    VerificationPassword: "Vui lòng nhập mật khẩu xác nhận",
    Confirm: "Xác nhận",
    ClearTheKilometers: "Xóa số km",
    PleaseSelectClearKilometers: "Chọn xóa số km",
    FaultLevel: "Cấp độ sự cố",
    StartTime: "Thời gian bắt đầu",
    EndTime: "Thời gian kết thúc",
    CriticalAlarm: "Cảnh báo nghiêm trọng",
    GeneralAlarm: "Cảnh báo thông thường",
    MinorAlarm: "Cảnh báo nhẹ",
    SearchCriteria: "Điều kiện tìm kiếm",
    Time: "Thời gian",
    Select: "Tra cứu",
    PleaseEnterContent: "Vui lòng nhập nội dung",
    SerialNumber: "Số thứ tự",
    Operation: "Thao tác",
    AlarmHelpLink: "Tài liệu trợ giúp",
    AlarmType: "Loại cảnh báo",
    AlarmSource: "Nguồn cảnh báo",
    Content: "Nội dung",
  },
};
export default vi;
