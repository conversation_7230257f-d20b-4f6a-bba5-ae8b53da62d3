import Vue from 'vue'

// 将对象改为工厂函数，接收i18n实例作为参数
export function createInitData(i18n) {
  const $t = i18n ? i18n.t.bind(i18n) : key => key;
  return {
    Distinguish: "0", //0为一个PLC、1为两个PLC
    //LayerNum: 1,//层数
    carCountState: 1, //1 无锡小车号特殊处理,
    isOne: false,
    isTwo: true,
    carVisible: false,
    form: {
      name: "",
      number: "2",
      chuteLockPwd: "",
      chuteUnLockPwd: "",
    },
    cellNum: "", // 解锁格口
    speedDial: "0.000", //运行速度
    speedDialDown: "0.000", //下层运行速度
    sortingMinutesCarShare: "0.000", //小车占有率
    sortingMinutesCarShareDown: "0.000", //下层小车占有率
    distanceDial: "0.000", //设备运行总公里数
    distanceDialDown: "0.000", //下层设备运行总公里数
    UpperLevelConnectionStatus: $t("scada.NotConnected"), // 上层PLC连接状态文案
    LowerLevelConnectionStatus: $t("scada.NotConnected"), // 下层PLC连接状态文案
    clearTheKilometersVisible: false, // 清除公里数弹框
    AbnormalAlarmTime: {
      startTime: "",
      endTime: "",
    },
    AbnormalAlarmInfo: {
      logSource: " ",
      loading: false,
      SearchCriteria: "",
      AbnormalAlarmList: [
        { name: "设备报警", id: 1 },
        { name: "业务报警", id: 2 },
      ],
      clickGroup: "设备报警",
      FaultLevelValueList: [false, false, true],
      FaultLevelList: [
        $t("scada.CriticalAlarm"),
        $t("scada.GeneralAlarm"),
        $t("scada.MinorAlarm"),
      ],
      abnormalListColumn: [
        {
          prop: "SerialNumber",
          label: $t("scada.SerialNumber"),
          align: "center",
          width: 80,
        },
        {
          prop: "createTime",
          label: $t("scada.Time"),
          align: "center",
          width: 135,
        },
        {
          prop: "alarmSource",
          label: $t("scada.AlarmSource"),
          align: "center",
          width: 135,
        },
        {
          prop: "message",
          label: $t("scada.Content"),
          align: "center",
          width: 420,
        },
        {
          prop: "alarmHelpUrl",
          label: $t("scada.AlarmHelpLink"),
          align: "center",
          width: 135,
        },
        {
          prop: "Operation",
          label: $t("scada.Operation"),
          align: "center",
          width: 200,
          fixed: "right",
        },
      ],
      totalResult: 0,
      currentPage: 1,
      pageSize: 10,
      messageNum: 0,
    },
    alertInstance: null, // 弹窗实例
    isPopupContent: "", // 弹窗内容是否和上次一样,值
    forbiddenVisible: false,
    chuteUnLockVisible: false,
    chuteLockVisible: false,
    abnormalAlarmVisible: false,
    abnormalList: [],
    forbidden: {
      upCarStr: "",
      downCarStr: "",
    },
    forbiddenDis: "false",
    scalesNum: 1, // 缩放比例
    WsUrl: "ws://10.142.91.154:9001/ws", //"ws://82.157.123.54:9010/ajaxchattest",
    websock: null,
    ws_heart: null,
    connectTimer: null, // 重连对象
    socket: null,
    isFullFlag: false, //是否全屏
    CarNum: 622,
    ChuteNum: 352,
    SupplysNum: 24,
    width: "1700px",
    height: "845px",
    heightDown: 805,
    // 上层格口1 2，下层格口3 4
    ChuteNum1: "157-162;",
    ChuteNum2: "163-198;",
    ChuteNum3: "199-214;",
    ChuteNum4: "250-215;",
    ChuteNum5: "251-256;",
    ChuteNum6: "1-16;",
    ChuteNum7: "62-17;",
    ChuteNum8: "78-63;",
    ChuteNum9: "257-262;",
    ChuteNum10: "263-298;",
    ChuteNum11: "299-314;",
    ChuteNum12: "350-315;",
    ChuteNum13: "351-358;",
    ChuteNum14: "79-94;",
    ChuteNum15: "140-95;",
    ChuteNum16: "156-141;",
    //异常格口
    ChuteErro: "160",
    //上层供包台34，下层供包台12
    SupplysNum1: "13-18",
    SupplysNum2: "19-24",
    SupplysNum3: "1-6",
    SupplysNum4: "7-12",
    isCreateCar: false,
    isCreateChute: false,
    isCreateSupplys: false,
    oneTime: 62.2, //小车一圈时间/S
    time1: 0,
    time2: 0,
    runPath1: "",
    runPath2: "",
    scansNum: "0", //上层扫描数
    scansNumDown: "0", //下层扫描数
    sortingMinutesDistance: "0", //本次运行公里数
    sortingMinutesDistanceDown: "0", //下层本次运行公里数
    sortingErrorNum: "0", //异常量
    fallingNum: "0", //落格数
    exceedsMaxNum: "0", //失败补推数
    interceptNum: "0", //拦截数
    hypercycleNum: "0", //超圈数
    notConfiguredGrid: "0", //未配置三段码格口
    abnormalNum: "0", //综合异常口
    cancelNum: "0", //取消件
    notObtainedNum: "0", //未获取三段码信息
    runningText: $t("scada.DeviceStopped"), //设备运行状态
    runningTextDown: $t("scada.DeviceStopped"), //设备运行状态
    nowStartDate: $t("scada.PendingStart"), //开始运行时间
    nowStartDateDown: $t("scada.PendingStart"), //开始运行时间
    speedDial: "0.000", //运行速度
    speedDialDown: "0.000", //下层运行速度
    sortingMinutesCarShare: "0.000", //小车占有率
    sortingMinutesCarShareDown: "0.000", //下层小车占有率
    webSocketRunText: $t("scada.NotConnected"),
    wcsRunText: $t("scada.NotConnected"),
    isMainIp: false, // 白名单
    fullText: $t("scada.FullScreen"),
    testPoint: 30,
    isDragging: false,
    startX: 0,
    startY: 0,
    translateX: 0,
    translateY: 0,
    scale: 1,
    minScale: 0.5,
    maxScale: 2,
    isMobile: false,
    cellSize: 12,
    cornerPoints: [],
    number: 21,
    isCtrlPressed: false,
    isOuterPaused: true,
    isInnerPaused: true,
    isOuterClockwise: false,
    isInnerClockwise: false,
    outerAnimationDuration: 60,
    innerAnimationDuration: 60,
    packagesNum: 3,
    //上层供包台1 2，下层供包台3 4
    innerPathData: "M1286.87,133.33 Q1302.87,131.87 1303.53,149.2 V686.8 Q1306.87,700.13 1266.67,697.33 H85.93 Q68,699.47 70.6,661.47 V599.87 V586.53 Q67.27,547.2 105.27,550.53 H1080 Q1080,556.53 1108.6,529.87 V359.47 Q1116.67,264.13 1070,266.67 H118.6 Q68.6,269.47 70.6,254.8 V161.2 Q67.27,133.87 88.6,133.33 H1286.87 Z", // 添加 H1286.87 Z 闭合路径
    pathData: "M1296.87,117.2 Q1313.53,117.2 1319.53,140.53 V698.8 Q1312.2,720.13 1270,713.47 H80.6 Q56.6,709.47 53.93,692.8 V580.53 Q53.93,531.87 86.6,533.87 H1067.27 Q1087.27,533.87 1092.6,513.87 V312.13 Q1083.27,282.8 1060,282.8 H93.33 Q53.93,282.8 53.93,268.13 V147.87 Q53.93,119.87 83.27,117.2 H1296.87 Z", // 添加 H1296.87 Z 闭合路径
    baseHeightNum: 1920,
    baseHeightNum: 937,
    upNo: "2",
    downNo: "1",
    isScadaStop: true,
    isLog: false,
    systemTypeCode: "1",
    isWcs: true,
    isFlb: false,
    showOverlay: false,
    account: "",
    // 移除对this.getBoxValue()的调用，改为在组件中初始化
    systemOptions: [],
    // 添加dialog列表数据
    dialogList: [],
  };
}