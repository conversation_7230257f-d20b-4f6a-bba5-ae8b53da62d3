<template>
  <div class="scadaBox">
    <div class="list">
      <div
        class="listItem"
        :style="
          item.deviceStatus == '已启动'
            ? ' border-top: 4px solid #5ECB80;'
            : ' border-top: 4px solid #FF5161;'
        "
        @click="linkClick(item)"
        v-for="(item, index) in scadaList"
        :key="index"
      >
        <div class="tittle">
          <div class="txt">{{ item.deviceName }}</div>
          <div class="status" v-if="item.deviceStatus == '已启动'">
            {{ item.deviceStatus }}
          </div>
          <div class="statusRed" v-if="item.deviceStatus == '未启动'">
            {{ item.deviceStatus }}
          </div>
        </div>
        <!-- 通过设备编号判断是否是哪个交叉带，哪个fbj 
                 wcs 交叉带 1
                 fbj 翻板机
                 wcs2 环扫交叉带 7
                -->
        <div class="equipmentCode">
          设备编号：<span style="color: #262626">{{ item.deviceCode }}</span>
        </div>
        <div class="equipmentType">
          设备类型：<span style="color: #262626">{{ item.deviceType }}</span>
        </div>
        <div class="startTime">
          开始运行时间：<span style="color: #262626">
            {{ item.scadaWcsConnTime }}</span
          >
        </div>
        <div class="statusBox">
          <div class="webStatus">
            <div class="txt">PLC状态</div>
            <div class="status" v-if="item.plcCoonStatus == '已连接'">
              ● {{ item.plcCoonStatus }}
            </div>
            <div class="statusRed" v-if="item.plcCoonStatus == '未连接'">
              ● {{ item.plcCoonStatus }}
            </div>
          </div>
          <div class="wcsStatus">
            <div class="txt">WCS通讯状态</div>
            <div class="status" v-if="item.scadaWcsConnStatus == '已连接'">
              ● {{ item.scadaWcsConnStatus }}
            </div>
            <div class="statusRed" v-if="item.scadaWcsConnStatus == '未连接'">
              ● {{ item.scadaWcsConnStatus }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import request from "@/utils/request";
import { Message } from "element-ui";
export default {
  data() {
    return {
      scadaList: [],
    };
  },
  methods: {
    // 获取列表
    getScadaList() {
      request({
        url: "/scada/transitData",
        method: "get",
      }).then((res) => {
        this.scadaList = res.data.result;
      });
    },
    // 跳转
    linkClick(item) {
      const routeUrl = this.$router.resolve({
        path: item.url,
        query: { dbCode: item.dbCode, port: item.scadaConnPort },
      });
      window.open(routeUrl.href, "_blank");
    },
  },
  mounted() {
    this.getScadaList();
  },
};
</script>

<style scoped lang="scss">
.scadaBox {
  /* height: 100%; */
  width: 100%;
  padding: 16px;
  background-color: #f7f8f9;
  min-height: calc(100vh - 84px);
  box-sizing: border-box;

      .list {
    background: #ffffff;
    /* height: 100%; */
    width: 100%;
    min-height: calc(100vh - 116px);
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    align-items: flex-start;

          .listItem {
      width: 280px;
      height: 244px;
      background: #ffffff;
      border-radius: 8px 8px 8px 8px;
      padding: 16px;
      box-sizing: border-box;
      box-shadow: 1px 2px 9px 1px rgba(0, 0, 0, 0.2);
      cursor: pointer;
      margin-right: 20px;
      margin-bottom: 20px;
      

      .tittle {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        .txt {
          font-weight: 500;
          font-size: 20px;
          color: #262626;
        }

        .status {
          width: 60px;
          height: 24px;
          font-weight: 400;
          font-size: 14px;
          color: #5ecb80;
          text-align: center;
          line-height: 24px;
          border: 1px solid #5ecb80;
          background: #f2f8ff;
          border-radius: 4px 4px 4px 4px;
        }

        .statusRed {
          color: #ff5161;
          width: 60px;
          height: 24px;
          font-weight: 400;
          font-size: 14px;

          text-align: center;
          line-height: 24px;
          border: 1px solid #ff5161;
          background: #f2f8ff;
          border-radius: 4px 4px 4px 4px;
        }
      }

      .equipmentCode {
        font-size: 14px;
        color: #737373;
        margin-bottom: 12px;
      }

      .equipmentType {
        font-size: 14px;
        color: #737373;
        margin-bottom: 12px;
      }

      .startTime {
        font-size: 14px;
        color: #737373;
        margin-bottom: 12px;
      }

      .statusBox {
        display: flex;
        width: 248px;
        height: 64px;
        background: #f8f8f8;
        border-radius: 4px 4px 4px 4px;
        margin: auto;
        justify-content: space-around;

        .webStatus {
          padding: 10px;

          .txt {
            font-weight: 400;
            font-size: 13px;
            color: #262626;
            text-align: center;
            margin-bottom: 8px;
          }

          .status {
            font-weight: 400;
            font-size: 13px;
            color: #5ecb80;
            text-align: center;
          }

          .statusRed {
            font-weight: 400;
            font-size: 13px;
            color: #ff5161;
            text-align: center;
          }
        }

        .wcsStatus {
          padding: 10px;

          .txt {
            font-weight: 400;
            font-size: 13px;
            color: #262626;
            text-align: center;
            margin-bottom: 8px;
          }

          .status {
            font-weight: 400;
            font-size: 13px;
            color: #5ecb80;
            text-align: center;
          }

          .statusRed {
            font-weight: 400;
            font-size: 13px;
            color: #ff5161;
            text-align: center;
          }
        }
      }
    }
  }
}
</style>