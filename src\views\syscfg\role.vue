<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
    >
      <el-form-item :label="$t('common.RoleCode')" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          :placeholder="$t('common.PlaseInputRoleCode')"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="权限字符" prop="roleKey">
        <el-input
          v-model="queryParams.roleKey"
          placeholder="请输入权限字符"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-button-group style="margin-top: 2px; margin-bottom: 15px">
        <el-button
          icon="el-icon-search"
          style="margin-right: 10px"
          @click="handleQuery"
          >{{ $t('common.Search') }}</el-button
        >
        <el-button
          icon="el-icon-refresh"
          style="margin-right: 10px"
          @click="resetQuery"
          type="warning"
          >{{ $t('common.Reset') }}</el-button
        >
        <el-button
          icon="el-icon-refresh"
          style="margin-right: 10px"
          @click="() => getList()"
          type="primary"
          >{{ $t('page.Serviceusermanagement.Refresh') }}</el-button
        >
        <!-- v-hasPermi="['system:role:add']" -->
        <el-button
          icon="el-icon-plus"
          style="margin-right: 10px"
          @click="handleAdd"
          type="success"
          >{{ $t('common.Add') }}</el-button
        >
      </el-button-group>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >{{ $t('common.Add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >{{ $t('common.Delete') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:role:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >{{ $t('common.Export') }}</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row> -->
    <el-table
      v-loading="loading"
      :data="roleList"
      border
      :header-cell-style="{ background: '#f8f8f9' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="index"
        width="120"
        align="center"
        :index="indexMethod"
        :title="$t('common.SerialNumber')"
      />
      <el-table-column :label="$t('common.RoleNumber')" prop="roleId" />
      <el-table-column
        :label="$t('common.RoleName')"
        prop="roleName"
        :show-overflow-tooltip="true"
      />
      <!-- <el-table-column label="角色描述" prop="roleDescribe" :show-overflow-tooltip="true" /> -->
      <el-table-column :label="$t('common.Memo')" prop="note" />
      <el-table-column :label="$t('common.CreationTime')" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.CreatorID')" prop="creatUserId" />
      <el-table-column :label="$t('common.Operation')" class-name="small-padding fixed-width">
        <template v-if="scope.row.roleId !== 1" slot-scope="scope">
          <!-- v-hasPermi="['system:role:edit']" -->
          <el-button
            :disabled="$store.state.user.userInfo.userType !== '1'"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >{{ $t('common.Update') }}</el-button
          >
          <!-- v-hasPermi="['system:role:remove']" -->
          <el-button
            :disabled="$store.state.user.userInfo.userType !== '1'"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >{{ $t('common.Delete') }}</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleLook(scope.row)"
            >{{ $t('common.View') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item :label="$t('common.RoleName')" prop="roleName">
          <el-input
            v-model="form.roleName"
            :disabled="title === '查看角色'"
            :placeholder="$t('common.PlaseRoleName')"
          />
        </el-form-item>
        <el-form-item :label="$t('common.MenuPermissions')">
          <!-- <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">{{ $t('common.ExpandAndCollapse') }}</el-checkbox> -->
          <el-checkbox
            v-model="menuNodeAll"
            :disabled="title === '查看角色'"
            @change="handleCheckedTreeNodeAll($event, 'menu')"
            >{{ $t('common.SelectAllDonteSelectAll') }}</el-checkbox
          >
          <el-checkbox
            v-model="form.menuCheckStrictly"
            :disabled="title === '查看角色'"
            @change="handleCheckedTreeConnect($event, 'menu')"
            >{{ $t('common.ParentChildLinkage') }}</el-checkbox
          >
          <el-tree
            ref="menu"
            node-key="menuName"
            :default-expanded-keys="['系统配置', '数据管理', '报表统计']"
            class="tree-border"
            :data="menuOptions"
            show-checkbox
            empty-text="加载中，请稍候"
            :props="defaultProps"
            :check-strictly="!form.menuCheckStrictly"
          />
        </el-form-item>
        <el-form-item :label="$t('common.Memo')">
          <el-input
            v-model="form.note"
            :disabled="title === '查看角色'"
            type="textarea"
            :placeholder="$t('common.PleaseEnterContent')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.Determine') }}</el-button>
        <el-button @click="cancel">{{ $t('common.Cancellation') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRole,
  delRole,
  addRole,
  updateRole,
  dataScope,
  QueryRoleByRoleId,
} from "@/api/system/role";
import {
  treeselect as menuTreeselect,
  roleMenuTreeselect,
} from "@/api/system/menu";
// import { treeselect as deptTreeselect, roleDeptTreeselect } from '@/api/system/dept'

export default {
  name: "Role",
  dicts: ["sys_normal_disable"],
  data() {
    return {
      // 所有的树节点
      allMenuOptions: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roleName: "",
        roleKey: "",
        status: "",
      },
      // 表单参数
      form: {
        roleId: "",
        roleName: "",
        status: "0",
        menuInfos: [],
        note: "",
      },
      defaultProps: {
        children: "children",
        label: "menuName",
        // disabled: this.isdisabledFn
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" },
        ],
        // roleKey: [
        //   { required: true, message: '权限字符不能为空', trigger: 'blur' }
        // ],
        // roleSort: [
        //   { required: true, message: '角色顺序不能为空', trigger: 'blur' }
        // ]
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // isdisabledFn() {
    //   return false
    // },
    indexMethod(index) {
      return index + 1;
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      listRole().then((response) => {
        this.roleList = response.data._menuInfo;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 处理数据结构
    handleMenuData(reqMenuOptions) {
      const tempArr = [];
      reqMenuOptions.forEach((item) => {
        item.children = [];
        if (item.parentId === undefined) {
          for (let index = 0; index < reqMenuOptions.length; index++) {
            if (
              reqMenuOptions[index].parentId &&
              item.id === reqMenuOptions[index].parentId
            ) {
              item.children.push(reqMenuOptions[index]);
            }
          }
          tempArr.push(item);
        }
        if (item.children.length === 0) {
          delete item.children;
        }
      });
      return tempArr;
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect().then((response) => {
        const reqMenuOptions = response.data._menuInfo;
        this.allMenuOptions = response.data._menuInfo;
        this.menuOptions = this.handleMenuData(reqMenuOptions);
      });
    },
    /** 根据角色ID查询已选中的菜单树 */
    getRoleMenuTreeselect(roleId) {
      return roleMenuTreeselect(roleId).then((response) => {
        // this.menuOptions = response.data._roleInfo
        return response;
      });
    },
    // 所有菜单节点数据
    getMenuAllCheckedKeys() {
      // 目前被选中的菜单节点
      const checkedKeys = this.$refs.menu.getCheckedNodes();
      // 半选中的菜单节点
      const halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
      let halfCheckedObjArr = [];
      halfCheckedKeys.forEach((item) => {
        let activeItem = this.allMenuOptions.find((menu) => {
          return item === menu.menuName;
        });
        halfCheckedObjArr.push(activeItem);
      });
      checkedKeys.unshift.apply(checkedKeys, halfCheckedObjArr);
      return checkedKeys;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu !== undefined) {
        this.$refs.menu.setCheckedNodes([]);
      }
      this.menuExpand = false;
      this.menuNodeAll = false;
      this.deptExpand = true;
      this.deptNodeAll = false;
      this.form = {
        roleId: "",
        roleName: "",
        status: "0",
        menuInfos: [],
        note: "",
        menuCheckStrictly: true,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.queryParams.pageNum = 1
      this.loading = true;
      const params = Number(this.queryParams.roleName);
      QueryRoleByRoleId(params).then((response) => {
        this.roleList = response.data._roleInfo;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.roleId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type === "menu") {
        const treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type === "menu") {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type === "menu") {
        this.form.menuCheckStrictly = !!value;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getMenuTreeselect();
      this.open = true;
      this.form.menuCheckStrictly = true;
      this.title = this.$t('common.AddRole');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset(); 
      const roleId = row.roleId;
      this.getMenuTreeselect(); // 将整个菜单树全部展示
      const roleMenu = this.getRoleMenuTreeselect(roleId); // 获取当前角色id下被选中的菜单
      this.open = true;
      roleMenu.then((res) => {
        const reqMenuOptions = res.data._roleInfo;
        const selectMenuOptions = this.handleMenuData(reqMenuOptions);
        this.setSelectMenu(selectMenuOptions);
      });
      this.title = this.$t('common.EditRole');
      this.form.menuCheckStrictly = true; // 默认父子联动以正常回显
      this.form.roleName = row.roleName;
      this.form.note = row.note;
      this.form.roleId = row.roleId;
    },
    /** 查看按钮操作 */
    handleLook(row) {
      this.reset();
      const roleId = row.roleId;
      this.getMenuTreeselect(); // 将整个菜单树全部展示
      const roleMenu = this.getRoleMenuTreeselect(roleId); // 获取当前角色id下被选中的菜单
      this.open = true;
      this.form.menuCheckStrictly = true; // 默认父子联动以正常回显
      this.title = "查看角色";
      this.form.roleName = row.roleName;
      this.form.note = row.note;
      this.form.roleId = row.roleId;
      roleMenu.then((res) => {
        const reqMenuOptions = res.data._roleInfo;
        const selectMenuOptions = this.handleMenuData(reqMenuOptions);
        this.setSelectMenu(selectMenuOptions);
      });
    },
    // 设置选中menu
    setSelectMenu(selectMenuOptions) {
      let selectMenuList = [];
      selectMenuOptions.forEach((item) => {
        selectMenuList = selectMenuList.concat(item.children);
      });  
      this.$nextTick(() => {
        this.$refs.menu.setCheckedNodes(selectMenuList);
      });
    },
    /** 选择角色权限范围触发 */  
    dataScopeSelectChange(value) {
      if (value !== "2") {
        this.$refs.dept.setCheckedKeys([]);
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.title === this.$t('common.EditRole')) {
            const params = Object.assign(this.form, {
              createTime: this.parseTime(Date.now()),
              creatUserId: "",
              roleId: this.form.roleId,
            });
            this.form.menuInfos = this.getMenuAllCheckedKeys(); // 获取所选的菜单列表
            updateRole(params)
              .then((response) => {
                this.$message.success(this.$t('common.EditSuccess'));
                this.open = false;
                this.getList();
              })
              .catch((error) => {
                console.error("searchFun::error", error);
                // this.open = false
                this.$message.error(this.$t('common.ModificationFailed'));
              });
          } else if (this.title === this.$t('common.AddRole')) {
            const params = Object.assign(this.form, {
              createTime: this.parseTime(Date.now()),
              creatUserId: "",
              roleId: 0,
            });
            this.form.menuInfos = this.getMenuAllCheckedKeys();
            addRole(params)
              .then((response) => {
                this.$message.success(this.$t('common.AddSuccess'));
                this.open = false;
                this.getList();
              })
              .catch((error) => {
                console.error("searchFun::error", error);
                // this.open = false
                this.$message.error(this.$t('common.AddFailed'));
              });
          } else {
            this.open = false;
          }
        }
      });
    },
    /** 提交按钮（数据权限） */
    submitDataScope: function () {
      if (this.form.roleId !== undefined) {
        this.form.deptIds = this.getDeptAllCheckedKeys();
        dataScope(this.form).then((response) => {
          this.$message.success(this.$t('common.EditSuccess'));
          this.openDataScope = false;
          this.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleName = row.roleName || "undefined";
      this.$confirm('Are you sure to delete the role name as"' + roleName + '"Data?', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(function () {
          return delRole([row.roleId]); // 删除单个
        })
        .then(() => {
          this.getList();
          this.$message.success(this.$t('common.DelSuccess'));
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/role/export",
        {
          ...this.queryParams,
        },
        `role_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
