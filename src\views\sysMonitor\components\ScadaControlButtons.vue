<template>
  <div class="control-buttons" style="margin-top: 2rem">
    <div style="display: flex">
      <div class="operation-btn" style="margin-right: 1rem">
        <el-badge
          v-if="abnormalAlarmInfo.messageNum >= 0"
          :value="
            abnormalAlarmInfo.messageNum ? abnormalAlarmInfo.messageNum : 0
          "
          class="badge"
        >
          <el-button type="primary" @click="abnormalAlarm" size="medium">
            <div style="display: flex; justify-content: space-between">
              <img
                src="../../../assets/images/scada/forewarning.png"
                class="btnIcon"
                :alt="`${$t('scada.AbnormalAlarm')}`"
              />
              <div style="height: 1.2rem; line-height: 1.2rem">
                {{ $t("scada.AbnormalAlarm") }}
              </div>
            </div>
          </el-button>
        </el-badge>
      </div>
      <el-button
        type="success"
        @click="chuteUnLockAllBtn"
        class="operation-btn"
        size="medium"
        style="margin-right: 0.8rem"
      >
        <div style="display: flex; justify-content: space-between">
          <img
            src="../../../assets/images/scada/unlock.png"
            class="btnIcon"
            :alt="`${$t('scada.Unlock')}`"
          />
          <div style="height: 1.2rem; line-height: 1.2rem">
            {{ $t("scada.OneKeyUnlock") }}
          </div>
        </div>
      </el-button>
      <el-button
        type="success"
        @click="chuteLockAllBtn"
        class="operation-btn"
        size="medium"
      >
        <div style="display: flex; justify-content: space-between">
          <img
            src="../../../assets/images/scada/lock.png"
            class="btnIcon"
            :alt="`${$t('scada.OneKeyLockSlot')}`"
          />
          <div style="height: 1.2rem; line-height: 1.2rem">
            {{ $t("scada.OneKeyLockSlot") }}
          </div>
        </div>
      </el-button>
    </div>

    <el-button
      type="info"
      @click="clearTheKilometers"
      class="operation-btn"
      size="medium"
    >
      <div style="display: flex; justify-content: space-between">
        <img
          src="../../../assets/images/scada/kilometres.png"
          class="btnIcon"
          :alt="`${$t('scada.ClearTheKilometers')}`"
        />
        <div style="height: 1.2rem; line-height: 1.2rem">
          {{ $t("scada.ClearTheKilometers") }}
        </div>
      </div>
    </el-button>
    <el-button
      type="primary"
      @click="requestFullScreen"
      class="operation-btn fullscreen-btn"
      size="medium"
    >
      <div style="display: flex; justify-content: space-between">
        <img
          src="../../../assets/images/scada/BlowUp.png"
          class="btnIcon"
          alt="全屏"
        />
        <div style="height: 1.2rem; line-height: 1.2rem">
          {{ fullText }}
        </div>
      </div>
    </el-button>
  </div>
</template>

<script>
export default {
  name: "ScadaControlButtons",
  props: {
    abnormalAlarmInfo: {
      type: Object,
      default: () => ({
        messageNum: 0,
      }),
    },
    fullText: {
      type: String,
      default: "全屏",
    },
  },
  methods: {
    abnormalAlarm() {
      this.$emit("abnormal-alarm");
    },
    chuteUnLockAllBtn() {
      this.$emit("chute-unlock-all");
    },
    chuteLockAllBtn() {
      this.$emit("chute-lock-all");
    },
    clearTheKilometers() {
      this.$emit("clear-kilometers");
    },
    requestFullScreen() {
      this.$emit("request-full-screen");
    },
  },
};
</script>

<style scoped lang="scss">
.control-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  z-index: 10;
}
.btnIcon {
  width: 1.2rem;
  height: 1.2rem;
  margin-right: 0.5rem;
}

.supply-station {
  cursor: pointer;
  transition: all 0.3s;
}

.supply-station:hover {
  filter: brightness(1.2);
}

.btnIcon {
  width: 1.2rem; /* 根据需要调整图片大小 */
  height: 1.2rem; /* 根据需要调整图片大小 */
  margin-right: 6px;
}

.control-buttons button {
  padding: 8px 16px;
  white-space: nowrap; /* 防止文本换行 */
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: space-between;
  background: linear-gradient(to bottom right, #2d89ff, #0053cd) !important;
  font-size: 16px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
  & span {
    display: flex;
    justify-content: space-between;
  }
}

.control-buttons button:hover {
  background-color: #357abd;
}

.FaultLevelSty {
  display: flex !important;
  justify-content: space-between;
  align-items: start;
}
</style> 

