<template>
  <div style="padding:5px">

    <el-button-group style="margin-top:15px;margin-bottom:15px">
      <el-button style="margin-right:10px" icon="el-icon-refresh" @click="loadPlan" type="primary">{{
        $t('page.Serviceusermanagement.Refresh') }}</el-button>
      <el-button style="margin-right:10px" icon="el-icon-plus" @click="showNewPlanModal" type="success">{{
        $t('common.Add') }}</el-button>
      <el-button style="margin-right:10px" icon="el-icon-edit" @click="updatePlan" type="info">{{
        $t('page.Serviceusermanagement.Save') }}</el-button>
      <el-button icon="el-icon-upload2" @click="updatePlan2">{{ $t('common.UpdateCache') }}</el-button>
    </el-button-group>

    <div style="margin: 15px 0 15px 20px; display: inline-block;">
      {{ $t('common.SystemType') }}:
      <el-select v-model="systemTypeCode" style="width:120px; margin-left: 5px;" @change="handleOptionChange" 
                 :popper-class="'system-type-select-dropdown'">
        <el-option v-for="item in systemOptions" :key="item.dbCode" :label="item.dbName" :value="item.dbCode" />
      </el-select>
    </div>

    <vxe-table resizable show-overflow row-id="id" :loading="planLoading" :data="tableDataPlan"
      :edit-config="{ trigger: 'click', mode: 'cell' }" @edit-closed="handlerEditClosedPlan">
      <vxe-table-column type="seq" :title="$t('common.SerialNumber')" width="120" :resizable="true" />
      <vxe-table-column :title="$t('common.SchemeID')" :resizable="true">
        <template v-slot="{ row }">
          <span>{{ row.modeType === 2 ? row.id : '' }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="modeType" :title="$t('common.ModeType')" :resizable="true" />
      <vxe-table-column field="planName" :title="$t('common.PlanName')" :resizable="true"
        :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
      <vxe-table-column :title="$t('common.PlanCode')" :resizable="true">
        <template v-slot="{ row }">
          <span>{{ row.modeType === 1 ? row.planCode : '' }}</span>
        </template>
      </vxe-table-column>
       <vxe-table-column v-if="isFfsSystem" title="内外圈" :resizable="true">
        <template v-slot="{ row }">
          <span>{{ row.circleLevel  == "1" ? '内圈' : '外圈' }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="planDesc" :title="$t('common.PlanDesc')" :resizable="true"
        :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
      <vxe-table-column field="planFlag" :title="$t('common.PlanFlag')" :resizable="true"
        :edit-render="{ name: '$select', options: planFlagOptions }" />
      <!-- <vxe-table-column field="isSelected" :title="$t('common.IsSelected')" :resizable="true" :formatter="isSelectedFormatter" /> -->
      <vxe-table-column :label="$t('common.IsSelected')" width="80">
        <template v-slot="{ row }">
          <dict-tag :options="IsSelected" :value="row.isSelected" />
        </template>
      </vxe-table-column>

      <vxe-table-column field="updateDate" :title="$t('common.UpdateDate')" :resizable="true" />
      <vxe-table-column field="remark" :title="$t('common.Remark')" :resizable="true"
        :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
      <vxe-table-column :label="$t('common.Operation')" fixed="right" :resizable="true">
        <template v-slot="{ row }">
          <el-row>
            <el-button type="text" @click="showDetailModal(row)">{{ $t('common.Detail') }}</el-button>
            <el-button type="text" @click="deletePlan(row)">{{ $t('common.Delete') }}</el-button>
            <el-button type="text" @click="selectPlan(row)">{{ $t('common.Enable') }}</el-button>
          </el-row>
        </template>
      </vxe-table-column>
    </vxe-table>

    <vxe-modal ref="newPlanModal" v-model="newPlanModal" :title="$t('common.AddPlan')" width="500" destroy-on-close>
      <vxe-form :data="newPlanForm" @submit="addPlan">
        <vxe-form-item :title="$t('common.PlanName')" field="planName" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item :title="$t('common.PlanDesc')" field="planDesc" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item :title="$t('common.Remark')" field="remark" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item :title="$t('common.PlanFlag')" field="planFlag" span="8"
          :item-render="{ name: '$select', options: planFlagOptions }" />
        <vxe-form-item v-if="isFfsSystem" title="内外圈" field="circleLevel" span="8"
          :item-render="{ name: '$select', options: circleLevelOptions }" />
        <vxe-form-item align="right" span="24">
          <vxe-button @click="$refs.newPlanModal.close()">{{ $t('common.Cancel') }}</vxe-button>
          <vxe-button type="submit" status="danger">{{ $t('page.Serviceusermanagement.Save') }}</vxe-button>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>

    <vxe-modal v-model="detailModal" :title="detailModalTitle" width="950" :height="detailModalHeight"
      :position="{ top: 25 }" destroy-on-close @beforeClose="handleCancel">
      <el-form :model="detailForm" style="margin-bottom:5px">
        <el-row type="flex">
          <el-col :span="3">
            <span>
              {{ $t('common.FirstSegmentCode') }}:
              <el-input v-model="detailForm.firstDispatchCode" style="width:100px" />
            </span>
          </el-col>
          <el-col :span="3">
            <span style="margin:0 0 0 20px">
              {{ $t('common.SecondSegmentCode') }}:
              <el-input v-model="detailForm.secondDispatchCode" style="width:100px" />
            </span>
          </el-col>
          <el-col :span="3">
            <span style="margin:0 0 0 20px">
              {{ $t('common.ThirdSegmentCode') }}:
              <el-input v-model="detailForm.thirdlyDispatchCode" style="width:100px" />
            </span>
          </el-col>
          <el-col :span="3">
            <span style="margin:0 0 0 20px">
              {{ $t('common.Chute') }}:
              <el-input v-model="detailForm.chuteNo" style="width:100px" />
            </span>
          </el-col>
          <el-col :span="12">
            <el-button-group style="margin:35px 0 0 20px">
              <el-button style="margin-right:15px" icon="el-icon-search" @click="queryDetail">{{ $t('common.Select')
              }}</el-button>
              <el-button icon="el-icon-refresh-left" @click="resetDetail">{{ $t('common.Reset') }}</el-button>
            </el-button-group>
          </el-col>
        </el-row>
      </el-form>
      <el-row style="margin:20px 0 15px 0">
        <el-button-group v-if="isShowButton">
          <el-button style="margin-right:15px" icon="el-icon-plus" @click="showNewDetailModal">{{ $t('common.Add')
          }}</el-button>
          <el-button style="margin-right:15px" icon="el-icon-edit" @click="updateDetail">{{
            $t('page.Serviceusermanagement.Save') }}</el-button>
          <el-button style="margin-right:15px" icon="el-icon-delete" @click="deleteDetails">{{ $t('common.BulkDelete')
          }}</el-button>
        </el-button-group>
        <el-button style="display:inline-block;margin-right:15px" icon="el-icon-download" @click="exportDetail">{{
          $t('common.Export') }}</el-button>
        <el-upload style="display:inline-block;margin-right:15px" :action="uploadUrl" accept=".xlsx"
          :show-file-list="false" :data="planEditing" :on-success="importDetailSucc" v-if="isShowButton"
          :headers="headerObj">
          <el-button style="margin-right:15px" icon="el-icon-upload2">{{ $t('page.Serviceusermanagement.Import')
            }}</el-button>
          <span slot="tip" class="el-upload__tip">{{ $t('page.Serviceusermanagement.OnlyExcel') }}</span>
        </el-upload>
      </el-row>

      <vxe-table ref="xTableSortingPlanDetail" resizable show-overflow row-id="id" :loading="detailLoading"
        :data="tableDataDetail" :edit-config="{ trigger: 'click', mode: 'cell' }"
        :checkbox-config="{ highlight: true, range: true }" @edit-closed="handlerEditClosedDetail"
        :editable="isShowButton" :sticky="true" :max-height="650">
        >
        <vxe-table-column type="checkbox" width="40" :resizable="true" v-if="isShowButton" />
        <vxe-table-column type="seq" :title="$t('common.SerialNumber')" width="120" :resizable="true" />
        <vxe-table-column field="firstDispatchCode" :title="$t('common.FirstSegmentCode')" width="185" :resizable="true"
          :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
        <vxe-table-column field="secondDispatchCode" :title="$t('common.SecondSegmentCode')" width="185"
          :resizable="true" :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
        <vxe-table-column field="thirdlyDispatchCode" :title="$t('common.ThirdSegmentCode')" width="185"
          :resizable="true" :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
        <vxe-table-column v-if="isFLB" field="logicCode" :title="$t('common.LogicCode')" width="85" :resizable="true"
          :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
        <vxe-table-column v-if="!isWcs" field="packetType" :title="$t('common.PacketType')" width="85" :resizable="true"
          :edit-render="{ name: '$select', options: dict.type.dwsPacketType }" />
        <vxe-table-column field="nextStopCode" :title="$t('common.NextStopCode')" width="125" :resizable="true"
          :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
        <vxe-table-column field="nextStopName" :title="$t('common.NextStopName')" width="120" :resizable="true"
          :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
        <vxe-table-column field="chuteNo" :title="$t('common.Chute')" width="120" :resizable="true"
          :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
        <vxe-table-column field="updateDate" :title="$t('common.ModificationTime')" width="150" :resizable="true" />
        <vxe-table-column field="remark" :title="$t('common.Remark')" width="80" :resizable="true"
          :edit-render="{ name: 'input', immediate: true, attrs: { type: 'text' } }" />
        <vxe-table-column :label="$t('common.Operation')" fixed="right" width="80" :resizable="true"
          v-if="isShowButton">
          <template v-slot="{ row }">
            <el-button-group>
              <el-button type="text" @click="deleteDetail(row)">{{ $t('common.Delete') }}</el-button>
            </el-button-group>
          </template>
        </vxe-table-column>
      </vxe-table>
    </vxe-modal>

    <vxe-modal ref="newDetailModal" v-model="newDetailModal" :title="$t('common.AddDetails')" width="500"
      :position="{ top: 25 }" destroy-on-close>
      <vxe-form title-width="95" :data="newDetailForm" @submit="addDetail">
        <vxe-form-item :title="$t('common.FirstSegmentCode')" field="firstDispatchCode" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item :title="$t('common.SecondSegmentCode')" field="secondDispatchCode" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item :title="$t('common.ThirdSegmentCode')" field="thirdlyDispatchCode" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item v-if="isFLB" :title="$t('common.LogicCode')" field="logicCode" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item v-if="!isWcs" :title="$t('common.PacketType')" field="packetType" span="24"
          :item-render="{ name: '$select', options: dict.type.dwsPacketType }" />
        <vxe-form-item :title="$t('common.NextStopCode')" field="nextStopCode" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item :title="$t('common.NextStopName')" field="nextStopName" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item :title="$t('common.Chute')" field="chuteNo" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item :title="$t('common.Remark')" field="remark" span="24"
          :item-render="{ name: 'input', attrs: { placeholder: '' } }" />
        <vxe-form-item align="right" span="24">
          <vxe-button type="submit" status="primary">{{ $t('page.Serviceusermanagement.Save') }}</vxe-button>
          <vxe-button status="primary" @click="$refs.newDetailModal.close()">{{ $t('common.Cancel') }}</vxe-button>
        </vxe-form-item>
      </vxe-form>
    </vxe-modal>
  </div>
</template>

<script>
import request from '@/utils/request'
import { getToken } from '@/utils/auth'

export default {
  name: 'SortingPlan',
  dicts: ['dwsPacketType'],
  data() {
    return {
      planLoading: false,
      tableDataPlan: [],
      planEdited: false,
      newPlanModal: false,
      newPlanForm: {},
      planFlagOptions: [
        { value: '1', label: this.$t('common.Departure') },
        { value: '2', label: this.$t('common.Arrival') },
        { value: '3', label: this.$t('common.ArrivalAndDeparture') }
      ],
      circleLevelOptions: [
        { value: 1, label: '内' },
        { value: 2, label: '外' }
      ],
      detailLoading: false,
      detailModal: false,
      detailModalHeight: window.innerHeight - 50,
      detailModalTitle: '',
      tableDataDetail: [],
      detailEdited: false,
      planEditing: {},
      newDetailModal: false,
      newDetailForm: {},
      detailForm: {},
      uploadUrl: process.env.VUE_APP_URL + '/cfg/plan/detail/import',
      isShowButton: true,
      systemOptions: "",
      systemTypeCode: "1",
      isWcs: true,
      isFLB: false,//菲律宾显示逻辑码
      form: {
      },
      headerObj: {
        token: getToken(),
      },
      IsSelected: [
        { value: "0", label: "No", listClass: "danger" },
        { value: "1", label: "Yes", listClass: "primary" }
      ]
    }
  },
  computed: {
    isFfsSystem() {
      if (this.systemOptions && this.systemTypeCode) {
        const systemOption = this.systemOptions.find(option => option.dbCode === this.systemTypeCode);
        return systemOption && systemOption.dbType === 'ffs';
      }
      return false;
    }
  },
  mounted() {
    this.getBoxValue()
    this.loadPlan()
  },
  methods: {
    handleCancel() {
      console.log('handleCancel')
    },
    isSelectedFormatter({ cellValue, row, column }) {
      if (row.isSelected) {
        return 'Yes'
      } else {
        return 'No'
      }
    },
    handlerEditClosedPlan({ row }) {
      this.planEdited = true
    },
    showNewPlanModal() {
      this.newPlanModal = true
      this.newPlanForm = {
        planName: '',
        planDesc: '',
        planFlag: '1',
        circleLevel: this.isFfsSystem ? '' : 1
      }
    },
    handleOptionChange() {
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;



      if (type == 'dws') {
        this.isWcs = false
        this.uploadUrl = process.env.VUE_APP_URL + '/dws/cfg/plan/detail/import'
      } else if (type == 'ffs') {
        this.isWcs = false
        this.isFLB = true
        this.uploadUrl = process.env.VUE_APP_URL + '/ffs/cfg/plan/detail/import'
      }else {
        this.isWcs = true
        this.isFLB = false
        this.uploadUrl = process.env.VUE_APP_URL + '/cfg/plan/detail/import'
      }
      this.loadPlan()
    },
    loadPlan() {
      this.planLoading = true
      //if(this.systemOptions==""){ this.getBoxValue() }
      let typeCode = this.systemTypeCode
      let type = ''
      if (this.systemOptions) {
        type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;
      }

      let url = '/cfg/plan/list'
      if (type == 'dws') {
        url = '/dws/cfg/plan/list'
      }
      if(type=='ffs'){
          url = '/ffs/cfg/plan/list'
      }

      return request({
        url: url,
        method: 'post',
        data: { curPage: 1, pageSize: 999, dbCode: this.systemTypeCode }
      }).then(response => {
        this.planEdited = false
        this.planLoading = false
        this.tableDataPlan = response.data.result
      }).catch(error => {
        this.planLoading = false
        this.$message({
          message: error,
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    updatePlan() {
      if (this.planEdited) {
        this.planLoading = true
        this.tableDataPlan.forEach(item => {
          item.dbCode = this.systemTypeCode
        })

        let typeCode = this.systemTypeCode
        let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

        let url = '/cfg/plan/batchEdit'
        if (type == 'dws') {
          url = '/dws/cfg/plan/batchEdit'
        }
        if(type=='ffs'){
          url = '/ffs/cfg/plan/batchEdit'
      }

        return request({
          url: url,
          method: 'post',
          data: this.tableDataPlan
        }).then(response => {
          this.planEdited = false
          this.planLoading = false
          this.loadPlan()
          this.$message({
            message: this.$t('common.EditSuccess'),
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.planLoading = false
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      } else {
        this.$message({
          message: '无需要保存的数据！',
          type: 'warning',
          duration: 2 * 1000
        })
      }
    },
    updatePlan2() {
      if (!this.planEdited) {
        this.$confirm('Update Cache will immediately update the WCS sorting scheme. Please confirm whether to continue execution？', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(() => {
          this.loading = true
          let typeCode = this.systemTypeCode
          let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

          let url = '/cfg/plan/update/cache'
          if (type == 'dws') {
            url = '/dws/cfg/plan/update/cache'
          }
          if(type=='ffs'){
          url = '/ffs/cfg/plan/update/cache'
      }
          return request({
            url: url,
            method: 'get',
            params: { dbCode: this.systemTypeCode }
            //data: this.tableDataPlan
          }).then(response => {
            this.loading = false
            this.$message({
              message: response.msg,
              type: 'success',
              duration: 2 * 1000
            })
          }).catch(error => {
            this.loading = false
            this.$message({
              message: error,
              type: 'error',
              duration: 2 * 1000
            })
          })
        }).catch(() => {
          this.$message({
            message: this.$t('common.OperationCancellation'),
            type: 'info',
            duration: 2 * 1000
          })
        })
      } else {
        this.$message({
          message: '请先保存数据！',
          type: 'warning',
          duration: 2 * 1000
        })
      }
    },
    isExistPlanName() {
      let flag = false
      this.tableDataPlan.forEach(element => {
        if (element.planName === this.newPlanForm.planName) flag = true
      })
      return flag
    },
    addPlan() {
      if (this.newPlanForm.planName === '') {
        this.$message({
          message: '方案名称不允许为空！',
          type: 'warning',
          duration: 2 * 1000
        })
      } else if (this.isFfsSystem && this.newPlanForm.circleLevel === '') {
        this.$message({
          message: '内外圈不允许为空！',
          type: 'warning',
          duration: 2 * 1000

        })
      } else if (this.isExistPlanName()) {
        this.$message({
          message: '方案名称已存在！',
          type: 'warning',
          duration: 2 * 1000
        })
      } else {
        this.newPlanForm.dbCode = this.systemTypeCode
        let typeCode = this.systemTypeCode
        let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

        let url = '/cfg/plan/add'
        if (type == 'dws') {
          url = '/dws/cfg/plan/add'
        }
        if(type=='ffs'){
          url = '/ffs/cfg/plan/add'
      }
        return request({
          url: url,
          method: 'post',
          data: this.newPlanForm
        }).then(response => {
          this.loadPlan()
          this.newPlanModal = false
          this.$message({
            message: this.$t('common.AddSuccess'),
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      }
    },
    deletePlan(row) {
      if (!row.isSelected) {
        this.$confirm('This action will permanently delete the selected【' + row.planName + '】, Do you want to continue?', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(() => {
          let typeCode = this.systemTypeCode
          let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

          let url = '/cfg/plan/batch/delete'
          if (type == 'dws') {
            url = '/dws/cfg/plan/batch/delete'
          }

          if(type=='ffs'){
          url = '/ffs/cfg/plan/batch/delete'
      }
          return request({
            url: url,
            method: 'post',
            data: { ids: row.id, dbCode: this.systemTypeCode }
          }).then(response => {
            this.loadPlan()
            this.$message({
              message: this.$t('common.DelSuccess'),
              type: 'success',
              duration: 2 * 1000
            })
          }).catch(error => {
            this.$message({
              message: error,
              type: 'error',
              duration: 2 * 1000
            })
          })
        }).catch(() => {
          this.$message({
            message: this.$t('common.OperationCancellation'),
            type: 'info',
            duration: 2 * 1000
          })
        })
      } else {
        this.$message({
          message: '方案已启用，不允许删除！',
          type: 'warning',
          duration: 2 * 1000
        })
      }
    },
    selectPlan(row) {
      this.$confirm('Please confirm whether to enable the scheme【' + row.planName + '】?', 'prompt', {
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        type: 'warning'
      }).then(() => {
        row.dbCode = this.systemTypeCode
        let typeCode = this.systemTypeCode
        let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

        let url = '/cfg/plan/switch'
        if (type == 'dws') {
          url = '/dws/cfg/plan/switch'
        }
        if(type=='ffs'){
          url = '/ffs/cfg/plan/switch'
      }
        return request({
          url: url,
          method: 'post',
          data: row
        }).then(response => {
          this.loadPlan()
          this.$message({
            message: this.$t('common.OperationSuccessful'),
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      }).catch(() => {
        this.$message({
          message: this.$t('common.OperationCancellation'),
          type: 'info',
          duration: 2 * 1000
        })
      })
    },
    //
    showDetailModal(row) {
      this.detailModal = true
      this.detailModalTitle = '方案明细（' + row.planName + '）'
      row.dbCode = this.systemTypeCode
      this.planEditing = row
      if (row.planCode) {
        this.isShowButton = false
      } else {
        this.isShowButton = true
      }
      this.loadDetail()
    },
    handlerEditClosedDetail() {
      this.detailEdited = true
    },
    loadDetail() {
      this.detailLoading = true
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

      let url = '/cfg/plan/detail/list'
      if (type == 'dws') {
        url = '/dws/cfg/plan/detail/list'
      }
      if(type=='ffs'){
          url = '/ffs/cfg/plan/detail/list'
      }
      return request({
        url: url,
        method: 'post',
        data: { planId: this.planEditing.id, planCode: this.planEditing.planCode, pageSize: 9999, dbCode: this.systemTypeCode }
      }).then(response => {
        this.detailEdited = false
        this.detailLoading = false
        this.tableDataDetail = response.data.result.records
      }).catch(error => {
        this.detailLoading = false
        this.tableDataDetail = []
        this.$message({
          message: error,
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    showNewDetailModal() {
      this.newDetailModal = true
      this.newDetailForm = { planId: this.planEditing.id, planCode: this.planEditing.planCode, firstDispatchCode: '', secondDispatchCode: '', nextStopCode: '', nextStopName: '', chuteNo: '', packetType: '' }
    },
    addDetail() {
      if (this.newDetailForm.chuteNo === '') {
        this.$message({
          message: '格口不允许为空！',
          type: 'warning',
          duration: 2 * 1000
        })
      } else {
        this.newDetailForm.dbCode = this.systemTypeCode
        let typeCode = this.systemTypeCode
        let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

        let url = '/cfg/plan/detail/add'
        if (type == 'dws') {
          url = '/dws/cfg/plan/detail/add'
        }
        if(type=='ffs'){
          url = '/ffs/cfg/plan/detail/add'
      }
        return request({
          url: url,
          method: 'post',
          data: this.newDetailForm
        }).then(response => {
          this.loadDetail()
          this.newDetailModal = false
          this.$message({
            message: this.$t('common.AddSuccess'),
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      }
    },
    deleteDetail(row) {
      this.$confirm('This operation will permanently delete the details. Do you want to continue?', 'prompt', {
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        type: 'warning'
      }).then(() => {
        let typeCode = this.systemTypeCode
        let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

        let url = '/cfg/plan/detail/batch/delete'
        if (type == 'dws') {
          url = '/dws/cfg/plan/detail/batch/delete'
        }
        if(type=='ffs'){
          url = '/ffs/cfg/plan/detail/batch/delete'
      }
        return request({
          url: url,
          method: 'post',
          data: { ids: row.id, dbCode: this.systemTypeCode }
        }).then(response => {
          this.loadDetail()
          this.$message({
            message: this.$t('common.DelSuccess'),
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      }).catch(() => {
        this.$message({
          message: this.$t('common.OperationCancellation'),
          type: 'info',
          duration: 2 * 1000
        })
      })
    },
    deleteDetails(row) {
      const selectRecords = this.$refs.xTableSortingPlanDetail.getCheckboxRecords()
      if (selectRecords.length > 0) {
        this.$confirm('This action will permanently delete the selected【' + selectRecords.length + '】Details, do you want to continue?', 'prompt', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }).then(() => {
          let ids = "";
          selectRecords.forEach(i => {
            ids += i.id + ','
          })
          let typeCode = this.systemTypeCode
          let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

          let url = '/cfg/plan/detail/batch/delete'
          if (type == 'dws') {
            url = '/dws/cfg/plan/detail/batch/delete'
          }
          if(type=='ffs'){
          url = '/ffs/cfg/plan/detail/batch/delete'
      }
          return request({
            url: url,
            method: 'post',
            data: { ids: ids, dbCode: this.systemTypeCode }
          }).then(response => {
            this.loadDetail()
            this.$message({
              message: this.$t('common.DelSuccess'),
              type: 'success',
              duration: 2 * 1000
            })
          }).catch(error => {
            this.$message({
              message: error,
              type: 'error',
              duration: 2 * 1000
            })
          })
        }).catch(() => {
          this.$message({
            message: this.$t('common.OperationCancellation'),
            type: 'info',
            duration: 2 * 1000
          })
        })
      } else {
        this.$message({
          message: '未选中数据！',
          type: 'warning',
          duration: 2 * 1000
        })
      }
    },
    updateDetail() {
      if (this.detailEdited) {
        this.detailLoading = true
        this.tableDataDetail.forEach(item => {
          item.dbCode = this.systemTypeCode
        })
        let typeCode = this.systemTypeCode
        let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

        let url = '/cfg/plan/detail/batch/edit'
        if (type == 'dws') {
          url = '/dws/cfg/plan/detail/batch/edit'
        }
        if(type=='ffs'){
          url = '/ffs/cfg/plan/detail/batch/edit'
      }
        return request({
          url: url,
          method: 'post',
          data: this.tableDataDetail
        }).then(response => {
          this.detailEdited = false
          this.detailLoading = false
          this.loadDetail()
          this.$message({
            message: '保存成功！',
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.detailLoading = false
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      } else {
        this.$message({
          message: '无需要保存的数据！',
          type: 'warning',
          duration: 2 * 1000
        })
      }
    },
    queryDetail() {
      this.detailLoading = true
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

      let url = '/cfg/plan/detail/list'
      if (type == 'dws') {
        url = '/dws/cfg/plan/detail/list'
      }
      if(type=='ffs'){
          url = '/ffs/cfg/plan/detail/list'
      }
      return request({
        url: url,
        method: 'post',
        data: { planId: this.planEditing.id, planCode: this.planEditing.planCode, firstDispatchCode: this.detailForm.firstDispatchCode, secondDispatchCode: this.detailForm.secondDispatchCode, thirdlyDispatchCode: this.detailForm.thirdlyDispatchCode, chuteNo: this.detailForm.chuteNo, pageSize: 9999, curPage: this.planEditing.curPage, dbCode: this.systemTypeCode }
      }).then(response => {
        this.detailEdited = false
        this.detailLoading = false
        this.tableDataDetail = response.data.result.records
      }).catch(error => {
        this.detailLoading = false
        this.tableDataDetail = []
        this.$message({
          message: error,
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    exportDetail() {
      this.detailLoading = true
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

      let url = '/cfg/plan/detail/export'
      if (type == 'dws') {
        url = '/dws/cfg/plan/detail/export'
      }
      if(type=='ffs'){
          url = '/ffs/cfg/plan/detail/export'
      }
      return request({
        url: url,
        method: 'post',
        data: { planId: this.planEditing.id, planCode: this.planEditing.planCode, firstDispatchCode: this.detailForm.firstDispatchCode, secondDispatchCode: this.detailForm.secondDispatchCode, thirdlyDispatchCode: this.detailForm.thirdlyDispatchCode, chuteNo: this.detailForm.chuteNo, pageSize: 9999, dbCode: this.systemTypeCode },
        responseType: 'blob'
      }).then(resp => {
        console.log(resp)
        const blob = new Blob([resp], { type: 'application/vnd.ms-excel' })
        const a = document.createElement('a')
        a.href = URL.createObjectURL(blob)
        a.download = '方案明细（' + this.planEditing.planName + '）.xlsx'
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(a.href)
        document.body.removeChild(a)
        this.detailLoading = false
      }).catch(error => {
        console.log(error)
        this.detailLoading = false
        this.$message({
          message: error || this.$t('common.ExportFailed'),
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    importDetailSucc() {
      this.$message({
        message: this.$t('common.ImportSuccessful'),
        type: 'success',
        duration: 2 * 1000
      })
      this.loadDetail()
    },
    resetDetail() {
      this.detailForm = {}
      this.loadDetail()
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params: { paramName: 'ScadaConnInfo' }
      }).then((res) => {
        console.log(res, '测试')
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    }
  }
}
</script>

<style scoped>
/* 确保系统类型下拉选项垂直显示 */
</style>

<style>
.system-type-select-dropdown .el-select-dropdown__item {
  display: block !important;
  float: none !important;
  width: 100% !important;
  text-align: left !important;
}

.system-type-select-dropdown .el-select-dropdown__list {
  display: block !important;
  flex-direction: column !important;
}

.system-type-select-dropdown {
  min-width: 120px !important;
}
</style>
