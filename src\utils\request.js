import axios from 'axios'
// import { Message } from 'element-ui'
import { MessageBox } from 'element-ui'
import store from '@/store'
import { removeToken } from '@/utils/auth'
import { getToken } from '@/utils/auth'
console.log(process.env.VUE_APP_URL)
const service = axios.create({
  baseURL: process.env.VUE_APP_URL,
  // baseURL: 'http://10.66.103.148:5000',
  timeout: 30000
})

// request拦截器
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      //config.headers['X-Token'] = getToken()
      config.headers['token'] = getToken()
    }
    return config
  },
  error => {
    console.log(error)
    return Promise.reject(error)
  }
)

// response拦截器
service.interceptors.response.use(
  response => {
    if (response.config.responseType === 'blob') {
      return response.data
    }
    const res = response.data
    if (res.code !== 20000) {
      // 50008:非法的Token;50012:其他客户端登录了;50014:Token过期了
      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        MessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          removeToken()
          location.reload()
        }).catch(() => {

        })
      }
      return Promise.reject(res.msg || res.code)
    } else {
      return res
    }
  },
  error => {
    console.log('error-' + error)
    // Message({
    //   message: error.message,
    //   type: 'error',
    //   duration: 2 * 1000
    // })
    return Promise.reject(error)
  }
)

export default service
