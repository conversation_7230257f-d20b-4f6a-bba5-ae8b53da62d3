<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="ic_sy_lgsl">
<rect id="Rectangle 9" width="60" height="60" rx="4" fill="#F7F8FF"/>
<g id="Security" clip-path="url(#clip0_382_18302)">
<g id="Group 1982663612">
<path id="Body" d="M30.8135 40.1401C33.3698 39.1749 43.3879 34.366 43.8313 18.1526C43.8502 17.4619 43.4898 16.8091 42.9031 16.444C35.1404 11.6139 25.3076 11.6139 17.5449 16.444C16.9583 16.8091 16.5979 17.4619 16.6168 18.1526C17.0601 34.366 27.0783 39.1749 29.6345 40.1401C30.0162 40.2842 30.4318 40.2842 30.8135 40.1401Z" fill="url(#paint0_linear_382_18302)"/>
<g id="Shape 1" filter="url(#filter0_b_382_18302)">
<path d="M16.7037 22.1228L15.4721 22.8266C14.7647 23.2308 14.3229 23.9937 14.3498 24.808C14.9298 42.384 26.9734 47.3862 29.6829 48.291C30.0393 48.41 30.4027 48.41 30.759 48.291C33.4685 47.3861 45.5122 42.384 46.0922 24.808C46.119 23.9937 45.6773 23.2308 44.9699 22.8266L43.7382 22.1228C35.3623 17.3366 25.0797 17.3365 16.7037 22.1228Z" fill="#9BA7FF" fill-opacity="0.28"/>
<path d="M15.5281 22.9246L16.7597 22.2208C25.101 17.4544 35.341 17.4544 43.6822 22.2208L44.9139 22.9246C45.5863 23.3089 46.0048 24.0333 45.9793 24.8043C45.6903 33.5641 42.5458 39.1822 39.1428 42.723C35.7376 46.266 32.0676 47.735 30.7233 48.1839C30.3901 48.2951 30.0518 48.2951 29.7187 48.1839C28.3743 47.735 24.7044 46.266 21.2992 42.723C17.8962 39.1822 14.7517 33.5641 14.4626 24.8043C14.4372 24.0333 14.8557 23.3089 15.5281 22.9246Z" stroke="url(#paint1_linear_382_18302)" stroke-width="0.225754"/>
</g>
</g>
<g id="Group 1982663625" filter="url(#filter1_d_382_18302)">
<path id="Vector" d="M29.0759 36.1227C28.9089 36.1227 28.7419 36.0586 28.615 35.9317L25.0507 32.3674C24.7956 32.1123 24.7956 31.6995 25.0507 31.4443C25.3059 31.1891 25.7187 31.1891 25.9739 31.4443L29.5381 35.0099C29.7933 35.2651 29.7933 35.6779 29.5381 35.933C29.4099 36.0599 29.2429 36.1227 29.0759 36.1227Z" fill="white" stroke="white" stroke-width="0.5"/>
<path id="Vector_2" d="M29.0741 36.1228C28.9072 36.1228 28.7402 36.0587 28.6132 35.9317C28.3581 35.6766 28.3581 35.2638 28.6132 35.0086L35.7431 27.8801C35.9983 27.6249 36.4111 27.6249 36.6662 27.8801C36.9214 28.1353 36.9214 28.5481 36.6662 28.8032L29.5364 35.9317C29.4081 36.06 29.2411 36.1228 29.0741 36.1228Z" fill="white" stroke="white" stroke-width="0.5"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_b_382_18302" x="9.81075" y="13.9923" width="40.8238" height="38.9288" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2.27041"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_382_18302"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_382_18302" result="shape"/>
</filter>
<filter id="filter1_d_382_18302" x="23.2516" y="26.4383" width="15.2155" height="11.6496" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.357309"/>
<feGaussianBlur stdDeviation="0.678887"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.258824 0 0 0 0 0.352941 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_382_18302"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_382_18302" result="shape"/>
</filter>
<linearGradient id="paint0_linear_382_18302" x1="22.6609" y1="19.8495" x2="52.2661" y2="42.1308" gradientUnits="userSpaceOnUse">
<stop stop-color="#93A1FF"/>
<stop offset="1" stop-color="#3C55FF"/>
</linearGradient>
<linearGradient id="paint1_linear_382_18302" x1="14.9411" y1="21.6228" x2="54.3462" y2="50.6819" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.734375" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_382_18302">
<rect width="42" height="42" fill="white" transform="translate(9 9)"/>
</clipPath>
</defs>
</svg>
