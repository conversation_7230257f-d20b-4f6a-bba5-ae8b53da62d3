<template>
  <div style="width: 100%; display: flex; justify-content: center;">
    <div
      ref="chart"
      style="width: 100%; height: 500px; display: flex; justify-content: center; align-items: center;"
    ></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "LineBar",
  props: {
    lineXAxis: {
      type: Array,
      default: () => []
    },
    lineTitle: {
      type: String,
      default: "峰值效率"
    },
    lineData: {
      type: Array,
      default: () => []
    },
    legendDate: {
      type: Array,
      default: () => []
    },
    titlePosition: {
      type: String,
      default: "center"
    }
  },
  data() {
    return {
      chart: null,
      lineDataArr: []
    };
  },
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart);

      const options = {
        title: {
          text: this.lineTitle,
          left: this.titlePosition
        },
        tooltip: {
          trigger: "item"
        },
        legend: {
          left: "left",
          data: this.legendDate
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: this.lineXAxis,
          axisLabel: {
            rotate: 45,
            textStyle: {
              fontSize: 15
            }
          }
        },
        yAxis: {
          type: "value",
          axisLabel: {
            interval: 0,
            textStyle: {
              fontSize: 15
            }
          }
        },
        series: [
          {
            name: "Evaporation",
            type: "bar",
            data: this.lineDataArr[0],
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 16
              }
            }
          },
          {
            name: "aaaa",
            type: "bar",
            data: this.lineDataArr[1],
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 16
              }
            }
          },
          {
            name: "bbbbb",
            type: "Line",
            data: this.lineDataArr[2],
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 16
              }
            }
          }
          // {
          //   data: this.lineData,
          //   type: "line",
          //   label: {
          //     show: true,
          //     position: "top",
          //     textStyle: {
          //       fontSize: 16
          //     }
          //   }
          // }
        ]
      };

      this.chart.setOption(options);
    }
  }
};
</script>

<style scoped></style>
