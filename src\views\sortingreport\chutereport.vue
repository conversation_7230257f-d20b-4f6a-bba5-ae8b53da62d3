<template>
  <div style="padding:5px">
    <el-form
      :model="form"
      style="margin:15px 0 15px 0"
    >
      <el-form-item>
        <span>
          {{ $t('common.Date') }}:
          <el-date-picker
            v-model="form.start"
            :clearable="false"
            type="datetime"
            :placeholder="$t('page.usermanagement.StartDate')"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:185px"
          />
          -
          <el-date-picker
            v-model="form.end"
            :clearable="false"
            type="datetime"
            :placeholder="$t('page.usermanagement.EndDate')"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:185px"
          />
        </span>
        <span style="margin: 0 0 0 20px">
        {{$t('common.SystemType')}}:
        <el-select v-model="systemTypeCode" style="width:120px" @change="handleOptionChange">
          <el-option v-for="item in systemOptions" :key="item.databaseCode" :label="item.dbName" :value="item.dbCode" />
        </el-select>
        </span>

        <span style="margin:0 0 0 20px">
          <el-button-group>
            <el-button style="margin-right:15px" icon="el-icon-search" @click="loadData" type="primary">{{ $t('common.Select') }}</el-button>
            <el-button style="margin-right:15px" icon="el-icon-refresh-left" @click="resetForm" type="warning">{{ $t('common.Reset') }}</el-button>
            <el-button icon="el-icon-download" @click="exportDataEvent">{{ $t('common.Export') }}</el-button>
          </el-button-group>
        </span>
      </el-form-item>
    </el-form>
    <vxe-table
      ref="xTable"
      show-footer
      resizable
      show-overflow
      :height="tableHeight"
      :loading="loading"
      :footer-span-method="footerColspanMethod"
      :footer-method="footerMethod"
      :data="tableData"
    >
      <vxe-table-column :resizable="true" type="seq" :title="$t('common.SerialNumber')" width="120" />
      <vxe-table-column :resizable="true" field="timespan" :title="$t('common.TimeInterval')" />
      <vxe-table-column :resizable="true" field="chuteNo" :title="$t('common.Chute')" />
      <vxe-table-column :resizable="true" field="quantity" :title="$t('common.SortingQuantity')" />
      <vxe-table-column :resizable="true" field="grossWeight" :title="$t('common.TotalSortingWeight')" />
    </vxe-table>
  </div>
</template>

<script>
import request from '@/utils/request'
import XEUtils from 'xe-utils'

export default {
  name: 'ChuteReport',
  data() {
    return {
      form: {
        start: this.getDate(0),
        end: this.getDate(1)
      },
      tableHeight: window.innerHeight - 180,
      loading: false,
      tableData: [],
      dataTotal: '',
      systemOptions:this.getBoxValue(),
      systemTypeCode:"1"
    }
  },
  methods: {
    getDate(format) {
      var today = new Date()
      var year = today.getFullYear()
      var month = today.getMonth() + 1
      var date = today.getDate()

      if (format === 0) {
        return year + '-' + month + '-' + date + ' 00:00:00'
      } else if (format === 1) {
        return year + '-' + month + '-' + date + ' 23:59:59'
      }
    },
    resetForm() {
      this.form = {
        start: this.getDate(0),
        end: this.getDate(1)
      }
      this.tableData = []
      this.tablePage = {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      }
    },
    loadData() {
      this.loading = true
        let typeCode = this.systemTypeCode
        let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

        let url = '/report/chute/list'
        if (type == 'dws'){
            url = '/dws/report/dwsNo/list'
        }
        if (type == 'ffs'){
          url = '/ffs/report/chute/list'
      }

      return request({
        url: url,
        method: 'post',
        data: { startTime:this.form.start,endTime: this.form.end,dbCode:typeCode}
      }).then(resp => {
        this.dataTotal = resp.data.result[resp.data.result.length - 1]
        this.tableData = resp.data.result
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || '加载失败！',
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    footerMethod({ columns, data }) {
      const maxs = []
      const sums = []
      maxs.push('最大值')
      maxs.push(this.dataTotal.quantityMaximum)
      maxs.push(this.dataTotal.grossWeightMaximum)

      sums.push('求和值')
      sums.push(this.dataTotal.quantityTotal)
      sums.push(this.dataTotal.grossWeightTotal)
      // const maxs = []
      // const sums = []
      // columns.forEach((column, columnIndex) => {
      //   if (columnIndex === 0) {
      //     maxs.push('最大值')
      //     sums.push('求和值')
      //   } else {
      //     let maxCell1 = null
      //     let maxCell2 = null
      //     let sumCell1 = null
      //     let sumCell2 = null
      //     switch (column.property) {
      //       case 'quantity':
      //         if (data.length === 0) {
      //           maxCell1 = 0
      //           maxs.push(maxCell1)
      //         } else {
      //           maxCell1 = (XEUtils.max(data, column.property)).quantity
      //           maxs.push(maxCell1)
      //         }
      //         sumCell1 = XEUtils.sum(data, column.property)
      //         sums.push(sumCell1)
      //         break
      //       case 'grossWeight':
      //         if (data.length === 0) {
      //           maxCell2 = 0
      //           maxs.push(maxCell2)
      //         } else {
      //           maxCell2 = (XEUtils.max(data, column.property)).grossWeight
      //           maxs.push(maxCell2)
      //         }
      //         sumCell2 = XEUtils.sum(data, column.property)
      //         sums.push(sumCell2)
      //         break
      //     }
      //   }
      // })
      // 返回一个二维数组的表尾合计
      return [maxs, sums]
    },
    footerColspanMethod({ _columnIndex }) {
      if (_columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3
        }
      }
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params:{paramName: 'ScadaConnInfo'}
      }).then((res) => {
        console.log(res,'测试')
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    },
    exportDataEvent() {
      //this.$refs.xTable.exportData({ filename: '格口分拣量（' + this.form.start + '至' + this.form.end + '）', type: 'csv', isFooter: false })
      
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;
      let url = '/report/chute/export'
      if (type == 'dws'){
          url = '/dws/report/dwsNo/export'
      }
      if (type == 'ffs'){
          url = '/ffs/report/chute/export'
      }
      return request({
        url: url,
        method: 'post',
        data: { startTime:this.form.start,endTime: this.form.end,dbCode:typeCode},
        responseType: 'blob'
      }).then(resp => {
        const blob = new Blob([resp], { type: 'application/vnd.ms-excel' })
        const a = document.createElement('a')
        a.href = URL.createObjectURL(blob)
        a.download = '格口分拣量（' + this.form.start + '至' + this.form.end + '）' + '.xlsx'
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(a.href)
        document.body.removeChild(a)
        this.$message({
          message: this.$t('common.BeginExport'),
          type: 'success',
          duration: 2 * 1000
        })
        //http://**************:8089/report/chute/export?startTime=2023/01/11 00:00:00&endTime=2023/01/30 10:02:36&step=3
      }).catch(error => {
        this.$message({
          message: error || this.$t('common.ExportFailed'),
          type: 'error',
          duration: 2 * 1000
        })
      })
    }
  }
}
</script>
