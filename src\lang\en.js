// 英语
const en = {
  login: {
    changelanguage: "Switch Language",
    index: "I'm six",
    title: "COGY Sorting System",
    Success: "Successful",
    plsuaername: "Please Input UserName",
    plspassword: "Please input a password",
    plscode: "Please input a Code",
    login: "login",
    code: "Code",
     chinese: "中文",
    english: "english",
    thai: "ภาษาไทย",
    ms: "Bahasa Melayu",
    vi: "Tiếng Việt",
    es: "Español",
    pt: "Bahasa Portugis",
    id: "Bahasa Indonesia",
    ph: "Filipino",
    SwipeRight: "Swipe Right To",
  },
  welcome: {
    title: "Welcome to the sorting log query system",
  },
  headers: {
    title: "Sorting data query system",
    loginout: "loginout",
  },
  sidebar: {
    Home: "Home Page",
    Syscfg: "System configuration",
    Userinfosup: "Package user management",
    User: "User managemen",
    Role: "Role management",
    Menu: "Menu management",
    Sysparam: "System parameters",
    Errchute: "Exception cell",
    Sysurl: "Log retention days",
    Sortingplan: "Sorting plan",
    Datamgr: "Data management",
    Runninglog: "Running log",
    Locklog: "Lock log",
    Sortinglog: "Sorting log",
    Packagenumberlog: "Historical package number",
    Sortingrepush: "Data re-push",
    Sortingreport: "Report statistics",
    Chutereport: "Sorting quantity",
    Sortingcountsup: "Quantity statistics (for package station)",
    Sortingcountuser: "Quantity statistics (for staff number)",
    Packagenumbercount: "Total package statistics",
    Sortingefficiency: "Efficiency statistics",
    Errorreport: "Type statistics",
    Monitor: "System monitoring",
    Scada: "SCADA monitoring",
    Job: "Scheduled task",
    Config: "Config",
    Dict: "Dict",
  },
  page: {
    homepage: {
      boardingcount: "Boarding count",
      scancount: "Scan count",
      cellcount: "Cell count",
      cancelleditems: "Cancelled items",
      rabbitinterceptions: "Rabbit interceptions",
      unmatchedcodes: "Unmatched Three-digit codes",
      overbounds: "Total number of overbounds",
      anomalies: "Total number of anomalies",
      sortingvolume: "Sorting volume",
      monday: "Monday",
      tuesday: "Tuesday",
      wednesday: "Wednesday",
      thursday: "Thursday",
      friday: "Friday",
      saturday: "Saturday",
      sunday: "Sunday",
    },
    Serviceusermanagement: {
      Refresh: "Refresh",
      AddUser: "Add User",
      BulkDelete: "Bulk Delete",
      FullName: "Full Name",
      EmployeeID: "Employee ID",
      Query: "Query",
      Import: "Import",
      OnlyExcel: "(Only Excel.xlsx format files can be imported.)",
      SerialNumber: "Serial Number",
      UserEmployeeID: "User Employee ID",
      UserName: "User Name",
      UserID: "User ID",
      Creator: "Creator",
      CreationTime: "Creation Time",
      Operation: "Operation",
      UserPassword: "User Password",
      ConfirmPassword: "Confirm Password",
      NewPassword: "New Password",
      Save: "Save",
      Cancel: "Cancel",
      UpdateUser: "Update User",
    },
    usermanagement: {
      UserID: "User ID",
      UserName: "User Name",
      UserAccount: "User Account",
      PlaseUserAccount: "Please input a Account",
      PhoneNumber: "Phone Number",
      PleasePhoneNumber: "Please input a Phone Number",
      Status: "Status",
      PleaseStatus: "Please input a Status",
      StartDate: "Start Date",
      EndDate: "End Date",
      CreationTime: "CreationTime",
      Operation: "Operation",
      UserGender: "User Gender",
      UserNickname: "User Nickname",
      Email: "Email",
      UserPassword: "User Password",
      UserGender: "User Gender",
      Active: "Active",
      Inactive: "Inactive",
      Role: "Role",
      Note: "Note",
      Search: "Search",
      Reset: "Reset",
      Add: "Add",
      Delete: "Delete",
      Update: "Update",
      More: "More",
      ResetPassword: "Reset Password",
    },
  },
  common: {
    Arrival: "Arrival",
    Departure: "Departure",
    ArrivalAndDeparture: "ArrivalAndDeparture",
    CloseOther: "Close Other",
    CloseAll: "Close All",
    RoleName: "Role Name",
    PlaseRoleName: "Plase Role Name",
    PermissionCharacters: "Permission Characters",
    PlasePermissionCharacters: "Plase Permission Characters",
    Order: "Order",
    Status: "Status",
    Search: "Search",
    Reset: "Reset",
    Delete: "Delete",
    Update: "Update",
    More: "More",
    CreationTime: "CreationTime",
    Operation: "Operation",
    Delete: "Delete",
    Update: "Update",
    Add: "Add",
    UpdateCache: "UpdateCache",
    Select: "Select",
    Import: "Import",
    Export: "Export",
    SerialNumber: "Serial Number",
    RoleNumber: "Role Number",
    Memo: "Memo",
    UpdateDate: "Update Date",
    ParameterName: "Parameter Name",
    ParameterValue: "Parameter Value",
    PleaseSelectARole: "Please select a role",
    PleaseInputnickname: "Please Inputni ckname",
    PleaseInputPhoneNumber: "Please Input Phone Number",
    PleaseInputEmail: "Please Input Email",
    PleaseInputUserName: "Please Input UserName",
    PleaseInputPassWord: "Please Input PassWord",
    RoleStatus: "Role Status",
    RoleCode: "Role Code",
    PlaseInputRoleCode: "Plase Input RoleCodeRole Code",
    CreatorID: "Creator ID",
    Determine: "Determine",
    Cancellation: "Cancellation",
    Task: "Task",
    PlaseTask: "Plase Task Name",
    TaskCode: "Plase Task Name",
    PlaseTaskCode: "Plase Task Code",
    MenuName: "Menu Name",
    PlaseMenuName: "Plase Menu Name",
    MenuStatus: "Menu Status",
    ExpandAndCollapse: "Expand/Collapse",
    SelectAllDonteSelectAll: "Select All/Don't Select All",
    ParentChildLinkage: "Parent-child linkage",
    MenuPermissions: "Menu permissions",
    PleaseEnterContent: "Please enter content",
    Icon: "Icon",
    ComponentPath: "Component Path",
    SchemeID: "Scheme ID",
    ModeType: "Mode Type",
    PlanName: "Plan Name",
    PlanCode: "Plan Code",
    PlanDesc: "PlanDesc",
    PlanFlag: "PlanFlag",
    IsSelected: "IsSelected",
    UpdateDate: "UpdateDate",
    Detail: "Detail",
    Enable: "Enable",
    Date: "Date",
    Grade: "Grade",
    Source: "Source",
    Message: "Message",
    ExtraData: "ExtraData",
    plcType: "PlcType",
    Number: "Number",
    Component: "Component",
    Chute: "Chute",
    Supply: "Supply",
    dwsNo: "DwsNo",
    ExceptionCode: "Exception Code",
    Code: "BarCode",
    ScanTime: "Scan time",
    BoardingTime: "Boarding Time",
    DropTime: "Drop time",
    NextPieceTime: "Next Piece Time",
    passBackTime: "PassBack Time",
    arrivalTime: "Arrival Time",
    PacketNumber: "Packet Number",
    TimeType: "TimeType",
    turns: "Turns",
    Image: "Image",
    UpperLevel: "Upper Level",
    LowerLevel: "Lower Level",
    LayerNumber: "Layer Number",
    PleaseEnterTheDictionaryLabel: "Please Enter the Dictionary Label",
    DictionaryName: "Dictionary Name",
    DictionaryId: "Dictionary ID",
    DictionaryType: "Dictionary Type",
    PleaseEnterTheDictionaryType: "Please Enter the Dictionary Type",
    PleaseEnterTheDictionaryName: "Please Enter the Dictionary Name",
    BagBindingOfficer: "Package Binding Officer",
    DictionaryEncoding: "Dictionary Encoding",
    DictionaryTag: "Dictionary Tag",
    DictionaryValue: "Dictionary Value",
    DictionarySort: "Dictionary Sort Order",
    DictionaryRemark: "Remarks",
    DictionaryCreateTime: "Creation Time",
    DataTag: "Data Tag",
    PleaseEnterTheDataLabel: "Please Enter the Data Label",
    DataKey: "Data Key",
    StyleAttribute: "Style Attribute",
    DisplayOrder: "Display Order",
    EchoStyle: "Echo Style",
    ListClass: "List Class",
    PleaseEnterTheDataKey: "Please Enter the Data Key",
    PleaseEnterTheStyleAttribute: "Please Enter the Style Attribute",
    PleaseEnterTheDisplayOrder: "Please Enter the Display Order",
    PleaseEnterTheEchoStyle: "Please Enter the Echo Style",
    PleaseEnterTheListClass: "Please Enter the List Class",
    PleaseEnterTheRemark: "Please Enter Remarks",
    PleaseEnterTheContent: "Please Enter the Content",
    TheAddressNeedsToBeHttp:
      "If it's an external link, the address should start with http(s)://",
    TheAddressNeedsToBeHttpUser:
      "The accessed route address, e.g., user. For external addresses to be accessed internally, they should start with http(s)://",
    TheAddressNeedsToBeHttpCatalogue:
      "The accessed component path, e.g., system/user/index, by default under the views directory",
    TheDefaultPassingParametersForTheRoute:
      "The default parameters for the accessed route, e.g., {'id': 1, 'name': 'ry'}",
    TheComponentWillBeCachedByKeepAlive:
      "If selected, it will be cached by keep-alive. The component's name and address need to match",
    SelectHiddenThenTheRoute:
      "If hidden is selected, the route will not appear in the sidebar but can still be accessed",
    SelectDisableThenTheRouteSidebar:
      "If disabled is selected, the route will not appear in the sidebar and cannot be accessed",
    PleaseEnterTheRouteParameters: "Please Enter Route Parameters",
    Yes: "Yes",
    No: "No",
    PermissionCharactersString: "String of permissions defined in the controller, example: @PreAuthorize(`@ss.hasRole('admin')`)",
    Cache1: "Cache",
    NoCache: "No Cache",
    AddUser: "Add User",
    BatchCancelAuthorization: "Batch Cancel Authorization",
    Close: "Close",
    CancelAuthorization: "Cancel Authorization",
    View: "View",
    UserType: "User Type",
    PleaseSelectUserType: "Please Select User Type",
    Forward: "Forward",
    Reverse: "Reverse",
    Lock: "Lock",
    Unlock: "Unlock",
    SendMessage: "Send Message",
    TaskGroup: "Task Group Name",
    PleaseSelectTaskGroup: "Please Select Task Group Name",
    TaskStatus: "Task Status",
    PleaseSelectTaskStatus: "Please Select Task Status",
    Log: "Log",
    InvokeTarget: "Invoke Target String",
    CronExpression: "Cron Execution Expression",
    ExecuteOnce: "Execute Once",
    TaskDetails: "Task Details",
    DispatchLog: "Dispatch Log",
    BeanCallExample: "Bean Call Example: ryTask.ryParams('ry')",
    ClassCallExample:
      "Class Call Example: com.ruoyi.quartz.task.RyTask.ryParams('ry')",
    ParameterDescription:
      "Parameter Description: Supports strings, booleans, long integers, floating-point numbers, integers",
    PleaseInputInvokeTarget: "Please Input Invoke Target String",
    PleaseInputCronExpression: "Please Input Cron Execution Expression",
    GenerateExpression: "Generate Expression",
    ExecuteStrategy: "Execution Strategy",
    MisfirePolicy: "Misfire Policy",
    ImmediateExecution: "Immediate Execution",
    DelayExecution: "Delayed Execution",
    AbandonExecution: "Abandon Execution",
    PleaseSelectExecuteStrategy: "Please Select Execution Strategy",
    Concurrent: "Concurrent",
    Allow: "Allow",
    Prohibit: "Prohibit",
    PleaseSelectConcurrent: "Please Select Concurrent",
    CronExpressionGenerator: "Cron Expression Generator",
    NextExecutionTime: "Next Execution Time",
    TaskGroup1: "Task Group",
    DefaultStrategy: "Default Strategy",
    ExecuteStatus: "Execution Status",
    PleaseSelectExecuteStatus: "Please Select Execution Status",
    ExecutionTime: "Execution Time",
    PleaseSelectExecutionTime: "Please Select Execution Time",
    Clear: "Clear",
    JobLogId: "Job Log ID",
    JobMessage: "Job Message",
    Detail1: "Details",
    DispatchLogDetails: "Dispatch Log Details",
    PleaseSelect: "Please Select",
    SelectStartTime: "Select Start Time",
    SelectEndTime: "Select End Time",
    DebugStart: "Debug (Start)",
    DebugClose: "Debug (Close)",
    ClearPacketNumber: "Clear Packet Number",
    Upload: "Upload",
    TotalQuery: "Total Queried",
    property: "images",
    all: "All",
    UnloadingToDeliveryScanning: "Unloading to Delivery Scanning",
    BuildingPackageScanning: "Package Building Scanning",

    SelectUser: "Select User",
    InvokeTargetMethod: "Invoke Target Method",
    PleaseSelectTaskGroup: "Please Select Task Group",
    CronExpression1: "cron expression",
    ExceptionInfo: "Exception Information",

    Edit: "Edit",
    ScopeOfAuthority: "Scope of Authority",
    DataPermission: "Data Permission",
    Confirm: "Confirm",
    StartDate: "Start Date",
    EndDate: "End Date",
    Weight: "Weight",
    length: "Length",
    width: "Width",
    heigth: "Heigth",
    CarNumber: "CarNumber",
    RequestedGate: "Requested Gate",
    PhysicalGrid: "Physical grid",
    TerminalDispatchCode: "TerminalDispatchCode",
    FallingGridTime: "Falling Grid Time",
    PackageGrade: "PackageGrade",
    ChipNumber: "ChipNumber",
    BindingTime: "BindingTime",
    BindingPersonnel: "BindingPersonnel",
    ScanType: "ScanType",
    NextStationNumber: "NextStationNumber",
    Rfid: "rfid",
    TimeInterval: "TimeInterval",
    SortingQuantity: "Sorting Quantity",
    TotalSortingWeight: "Total Sorting Weight",
    NumberOfScannedBarcodeRecords: "Number Of Scanned Barcode Records",
    RecordTheNumberOfPackagesLoadedOntoTheVehicle:
      "Record TheNumber Of Packages Loaded Onto TheVehicle",
    NumberOfDropFeedbackRecords: "NumberOfDropFeedbackRecords",
    scanQuantity: "Scan Quantity",
    arrivalQuantity: "Arrival Quantity",
    passBackQuantity: "PassBack Quantity",
    TotalNumberOfPackages: "Total Number Of Packages",
    Packagenumb: "Package number (quantity of parts during this period)",
    Type: "Type",
    Count: "Count",
    SystemType: "System Type",
    EmployeeID: "Employee ID",
    FullName: "Full Name",
    Password: "Password",
    ConfirmPassword: "Confirm Password",
    SelectGender: "Select Gender",
    Active: "Active",
    Inactive: "Inactive",
    Male: "Male",
    Female: "Female",
    Unknown: "Unknown",
    RoleOrder: "Role Order",
    Show: "Show",
    Hide: "Hide",
    Default: "Default",
    System: "System",
    Success: "Success",
    Failure: "Failure",
    AddMenu: "Add Menu",
    EditMenu: "Edit Menu",
    ParentMenu: "Parent Menu",
    MenuType: "Menu Type",
    Directory: "Directory",
    Menu: "Menu",
    Button: "Button",
    MenuIcon: "Menu Icon",
    SelectIcon: "Select Icon",
    RouteAddress: "Route Address",
    DisplayOrder: "Display Order",
    ExternalLink: "External Link",
    DisplayStatus: "Display Status",
    MenuStatus: "Menu Status",
    RouteParameters: "Route Parameters",
    Cache: "Cache",
    ComponentPath: "Component Path",
    AddRole: "Add Role",
    EditRole: "Edit Role",
    AddPlan: "Add Plan",
    Cancel: "Cancel",
    FirstSegmentCode: "First Segment Code",
    SecondSegmentCode: "Second Segment Code",
    ThirdSegmentCode: "Third Segment Code",
    NextStopCode: "Next Stop Code",
    NextStopName: "Next Stop Name",
    ModificationTime: "Modification Time",
    BulkDelete: "Bulk Delete",
    AddDetails: "Add Details",
    ByServicePackage: "By Service Package",
    ByDwsNo: "By Dws No",
    Granularity: "Granularity",
    LogicCode: "Logic Code",
    PacketType: "PacketType",
    AddSuccess: "Addition Successful",
    EditSuccess: "Modification Successful",
    DelSuccess: "Deletion Successful",
    ImportSuccessful: "Import Successful",
    BeginExport: "Begin Export",
    ModificationFailed: "Modification Failed",
    AddFailed: "Add Failed",
    OperationSuccessful: "Operation Successful",
    OperationFailed: "Operation Failed",
    OperationCancellation: "Operation Cancellation",
    ExportFailed: "Export Failed",
    LoginOut:
      "You have been logged out, you can cancel staying on this page or log in again",
    ConfirmLogout: "Confirm Logout",
    LogAgain: "Log Again",
    Remark: "Remark",
    DerivedData: "Derived Data",
    NoData: "No Data Available",
    SearchData: "Search Data",
    ViewDWSPeakEffect: "View DWS Peak Effect",
    peakEffect: "Peak Efficiency",
    DwsNo: "DwsNo",
    BigBarRate: "The proportion of big bags",
    Quantity: "Quantity",
    SmallBarRate: "The proportion of small bags",
    Notempty: "Account password or verification code cannot be empty",
    Notpassword: "Account password cannot be empty",
    Id: "Id",
    Parameter: "Parameter",
    PlParameter: "Please enter a parameter name",
    ParameterKey: "ParameterKey",
    PlParameterKey: "Please enter the parameter key name",
    ParameterValue: "ParameterValue",
    PlParameterValue: "Please enter the parameter key value",
    Group: "Group",
    PlGroup: "Please select a group name",
  },
  scada: {
    DeviceRunningStatus: "Device Running Status",
    DeviceStopped: "Device Stopped",
    DeviceRunning: "Device Running",
    StartTime: "Start Time",
    RunningSpeed: "Running Speed",
    CartOccupancyRate: "Cart Rate", //"Cart Occupancy Rate",
    TotalDistanceTraveledByDevice: "Total Distance", //"Total Distance Traveled by Device",
    DistanceTraveledInCurrentRun: "Distance Traveled", //"Distance Traveled in Current Run",
    TotalDistanceTraveled: "Total Distance", //"Total Distance Traveled",
    Scans: "Scans",
    PendingStart: "Pending Start",
    FullScreen: "Full Screen",
    ExitFull: "Exit Full",
    UpperLevelRunningSpeed: "Upper Speed", //"Upper Level Running Speed",
    UpperLevelDeviceRunningStatus: "Upper Status", //"Upper Level Device Running Status",
    UpperLevelStartTime: "Upper Time", //"Upper Level Start Time",
    UpperLevelCartOccupancyRate: "Upper Rate", //"Upper Level Cart Occupancy Rate",
    UpperLevelByDevice: "Upper Device", //"Upper Level Total Distance Traveled by Device",
    LowerLevelDeviceRunningStatus: "Lower Status", //"Lower Level Device Running Status",
    LowerLevelStartTime: "Lower Time", //"Lower Level Start Time",
    LowerLevelRunningSpeed: "Lower Speed", //"Lower Level Running Speed",
    LowerLevelCartOccupancyRate: "Lower Rate", //"Lower Level Cart Occupancy Rate",
    LowerLevelTotalDistanceTraveledByDevice: "Lower Device", //"Lower Level Total Distance Traveled by Device",
    UpperLevelScans: "Upper Level Scans",
    LowerLevelScans: "Lower Level Scans",
    AbnormalQuantity: "Abnormal Quantity",
    NumberOfSlotsOccupied: "Number Occupied", //"Number of Slots Occupied",
    FailedRepushQuantity: "Failed Quantity", //"Failed Repush Quantity",
    InterceptedQuantity: "Intercepted Quantity",
    ExcessiveCirclesQuantity: "Excessive Quantity", //"Excessive Circles Quantity",
    UnconfiguredThreeSegmentCodeSlots: "Unconfigured Slots", //"Unconfigured Three-Segment Code Slots",
    ComprehensiveExceptionSlots: "Comprehensive", //"Comprehensive Exception Slots",
    CancelledItems: "Cancelled Items",
    UnobtainedThreeSegmentCodeInformation: "Unobtained Information", //"Unobtained Three-Segment Code Information",
    WebSocketStatus: "WebSocket Status",
    WCSCommunicationStatus: "WCS Status",
    SortingStatus: "Sorting Status",
    Connected: "Connected",
    NotConnected: "NotConnected",
    Idle: "Idle",
    Loaded: "Loaded",
    CartStatus: "Cart Status",
    LittleThingsAreQuick:'Small Kwai',
    Headscratcher: 'Top sweep',
    OnLine:'On Line',
    Offline:'off-line',
    Locked: "Locked",
    FullPackage: "Full",
    SlotStatus: "Slot Status",
    InterceptedItem: "Exit",
    ExceptionSlot: "Exception",
    PendingCommunication: "Pending",
    Max: "Max",
    Cancellation: "Cancel",
    UnProgramme: "UnProgramme",
    UnThree: "UnThree",
    CartOperation: "Car Operate",
    OneKeyUnlock: "UnlockAll",
    OneKeyLockSlot: "LockAll",
    CartNumber: "Cart Number",
    FloorNumber: "Floor Number",
    UpperLevel: "Upper Level",
    LowerLevel: "Lower Level",
    Lock: "Lock",
    Unlock: "Unlock",
    ManualSwitchStart: "Manual Switch (Start)",
    ManualSwitchClose: " Manual Switch (Close)",
    Run: "Run",
    Close: "Close",
    DisabledList: "Disabled",
    AbnormalAlarm: "Abnormal",
    DisableCart: "Disable Cart",
    UpperLevelCart: "Upper Level Cart",
    LowerLevelCart: "Lower Level Cart",
    VerificationPassword: "erification Password",
    Confirm: "Confirm",
    ClearTheKilometers: "Clear the kilometers",
    PleaseSelectClearKilometers: "Please select Clear kilometers",
    FaultLevel: "Fault Level",
    CriticalAlarm: "Critical Alarm",
    GeneralAlarm: "General Alarm",
    MinorAlarm: "Minor Alarm",
    StartTime: "Start Time",
    EndTime: "End Time",
    SearchCriteria: "Search Criteria",
    Time: "Time",
    UpperLayerPLCDisconnect: "Upper LayerPLCDisconnect",
    LowerLevelPLCDisconnect: "Lower LayerPLCDisconnect",
    UpperLayerPLCConnectionStatus: "Upper Layer PLC Connection Status",
    LowerLevelPLCConnectionStatus: "Lower Level PLC Connection Status",
    Select: "Select",
    PleaseEnterContent: "Please enter content",
    AlarmHelpLink: "Help Document",
    AlarmType: "Alarm Type",
    Content: "Content",
    Operation: "Operation",
    AlarmSource: "Alarm Source",
    PleaseEnterContent: "Please enter content",
  },
};
export default en;
