<!-- YYYYYYYYYYYY----需要搬 -->
<template>
  <el-drawer
    :visible.sync="showDrawer"
    :title="moduleTitle"
    :size="size"
    @close="handleClose"
    custom-class="echartTable"
  >
    <div
      v-loading="loadingB"
      element-loading-text="Loading..."
      :element-loading-spinner="svg"
    >
      <div style="font-weight: 600;padding-left: 30px" class="flexCenter">
        {{ $t("common.SearchData") }}：<el-date-picker
          v-model="echartsDate"
          type="date"
          :clearable="false"
          :placeholder="$t('common.SearchData')"
          @change="handleChangeDate"
          :size="size"
        />
      </div>
      <div v-if="chartAllDataVal[0]">
        <!-- class="echartTable" -->
        <div class="lineChart" style="padding-left: 30px;">
          <LineChartComponent
            :key="chartAllDataVal[0]"
            v-if="chartAllDataVal[0]"
            :line-title="$t('common.peakEffect')"
            :lineData="chartAllDataVal[0]"
            :lineXAxis="chartAllDataVal[1]"
          />
          <div v-else style="width: 100%; height: 200px" class="flexCenter">
            {{ $t("common.NoData") }}
          </div>
        </div>
        <div class="line" />
        <div style="position: relative;padding-top: 40px; padding-left: 30px">
          <div class="btnBoxSty">
            <el-button
              icon="el-icon-download"
              @click="exportExcel"
              class="btnDownload"
              >{{ $t("common.DerivedData") }}</el-button
            >
            <!-- <div class="btnSty">
              <el-button
                icon="el-icon-arrow-up"
                @click="changeSort('arrowUp')"
                class="btnSort"
              ></el-button>
              <el-button
                icon="el-icon-arrow-down"
                @click="changeSort('arrowDown')"
                class="btnSort"
              ></el-button>
            </div> -->
          </div>
          <!-- echart表格  380-->
          <vxe-table
            ref="xTable"
            :height="tableHeight"
            resizable
            show-overflow
            align="center"
            header-align="center"
            border
            :sort-method="remoteSort"
            :footer-span-method="footerColspanMethod"
            :data="chartTableVal"
            @sort-change="handleSortChange"
            :sort-config="{ remote: true }"
            :loading="loadingB"
            v-if="chartAllDataVal[0]"
          >
            <!-- 序号列 -->
            <vxe-table-column
              :resizable="true"
              type="seq"
              field=""
              :title="$t('common.SerialNumber')"
              width="120"
            />

            <!-- DwsNo 列 -->
            <vxe-table-column
              :resizable="true"
              field="dwsNo"
              :title="$t('common.DwsNo')"
            />
            <!-- peakEffect 列 -->
            <vxe-table-column
              :resizable="true"
              sortable
              field="quantity"
              :title="$t('common.peakEffect')"
            />
            <vxe-table-column
              :resizable="true"
              field="bigBarRate"
              :title="
                $t('common.BigBarRate') + ' (' + $t('common.Quantity') + ')'
              "
            />
            <vxe-table-column
              :resizable="true"
              field="smallBarRate"
              :title="
                $t('common.SmallBarRate') + ' (' + $t('common.Quantity') + ')'
              "
            />
          </vxe-table>
        </div>
      </div>
      <div
        v-else
        style="width: 100%; height: 80vh; font-size: 20px;flex-direction: column; text-align: center"
        class="flexCenter"
      >
        <img src="../../assets/noData.png" class="user-avatar" />
        <div style="margin-top: 20px;color:rgb(100,100,100)">
          {{ $t("common.NoData") }}
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import moment from "moment";
import request from "@/utils/request";
import LineChartComponent from "../../components/Echarts/Line/index.vue";

export default {
  name: "ShowDrawerC",
  components: {
    LineChartComponent
  },
  props: {
    echartsDate: {
      type: String,
      default: ""
    },
    drawerOpen: {
      type: Boolean,
      default: false
    },
    moduleTitle: {
      type: String,
      default: ""
    },
    size: {
      type: String,
      default: "50%"
    },
    chartAllData: {
      type: Array,
      default: () => []
    },
    isWcs: {
      type: Boolean,
      default: false
    },
    chartTable: {
      type: Array,
      default: () => []
    },
    dbCode: {
      type: String,
      default: "1"
    },
    sort: {
      type: Boolean,
      default: false
    },
    drawerLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableHeight: window.innerHeight - 650,
      showDrawer: false,
      chartAllDataVal: this.chartAllData,
      loadingB: this.drawerLoading, // 正在加载中……
      sortVal: this.sort,
      isWcsVal: this.isWcs,
      chartTableVal: this.chartTable,
      clickNum: 0,
      getDom: null,
      remoteSort: false // 远程处理
    };
  },
  beforeDestroy() {
    // this.handleChangeDate(moment().format("YYYY/MM/DD"))
    // this.loadingB = false;
    // this.showDrawer = false
  },
  // mounted() {
  //   this.handleChangeDate(moment().format("YYYY/MM/DD"))
  // },
  methods: {
    setDomStyle() {
      // 使用$el来获取当前组件的根DOM元素
      // const elDrawerTitle = this.$el.querySelector('.el-drawer__title');
      // if (elDrawerTitle !== null) {
      //   elDrawerTitle.style.marginBottom = '0';
      // }
    },
    // 修改排序
    // changeSort(type) {
    //   if (type === "arrowDown") {
    //     this.sortVal = false;
    //   } else {
    //     this.sortVal = true;
    //   }

    //   this.$emit("changeDate", this.sortVal, this.clickNum, this.loadingB);
    //   this.clickNum = 1;
    // },
    handleSortChange({ column, prop, order }) {
      // 当排序变化时触发，你可以在这里向后端发送请求，并传递排序参数
      // this.fetchData({ prop, order });
      this.sortVal = !this.sortVal;
      // console.log("this.sortVal", this.sortVal);
      // console.log("column", column);
      // console.log("prop", prop);
      // console.log("order", order);
      if (order === "desc" || order === "asc") {
        this.$emit("changeDate", this.sortVal, this.clickNum, this.loadingB);
        this.clickNum = 1;
      }
    },
    // 导出表格
    exportExcel() {
      if (this.chartTableVal && this.chartTableVal.length <= 0) {
        return this.$message({
          message: "暂无数据！",
          type: "warning",
          duration: 2 * 1000
        });
      }
      return request({
        url: "/dws/report/efficiency/peak/export",
        method: "post",
        data: {
          curDate: this.echartsDate,
          dbCode: this.dbCode,
          sort: this.sort
        },
        responseType: "blob"
      })
        .then(resp => {
          const blob = new Blob([resp], { type: "application/vnd.ms-excel" });
          const a = document.createElement("a");
          a.href = URL.createObjectURL(blob);
          a.download = "峰值效率.xlsx";
          a.style.display = "none";
          document.body.appendChild(a);
          a.click();
          URL.revokeObjectURL(a.href);
          document.body.removeChild(a);
          this.$message({
            message: this.$t("common.BeginExport"),
            type: "success",
            duration: 2 * 1000
          });
        })
        .catch(error => {
          this.$message({
            message: error || this.$t("common.ExportFailed"),
            type: "error",
            duration: 2 * 1000
          });
        });
    },
    // 修改时间
    handleChangeDate(value) {
      this.echartsDate = moment(value).format("YYYY-MM-DD");
      this.$emit("send-data", this.echartsDate, false);
    },

    footerColspanMethod({ _columnIndex }) {
      if (_columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3
        };
      }
    },
    handleClose() {
      this.$emit("close", false);
      // this.echartsDate = moment(value).format("YYYY-MM-DD");
      this.$emit("send-data", moment().format("YYYY/MM/DD"), true);
    }
  },
  watch: {
    echartsDate: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.echartsDate = newVal;
        }
      }
    },
    drawerOpen: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.showDrawer = newVal;
        }
      }
    },
    chartTable: {
      immediate: true,
      handler(newVal) {
        this.chartTableVal = newVal;
      }
    },
    drawerLoading: {
      immediate: true,
      handler(newVal) {
        this.loadingB = newVal;
      }
    },
    chartAllData: {
      immediate: true,
      handler(newVal) {
        this.chartAllDataVal = newVal;
      }
    },
    echartsDate: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.echartsDate = newVal;
        }
      }
    },
    chartAllData: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.chartAllDataVal = newVal;
        }
      }
    }
  },

  mounted() {
    this.setDomStyle();
    if (this.drawerOpen) {
      this.showDrawer = this.drawerOpen;
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer.rtl) {
  background: rgb(16, 55, 94);
}
:deep(.el-drawer__body) {
  background: "rgb(16, 55, 94) !important";
}
:deep(.el-buttom) {
  background: skyblue !important;
}
:deep(.vxe-table--body tbody td) {
  text-align: center;
}
.drawerSty {
  // background: pink !important;
}

.fileNameInput {
  display: flex;
  margin: 0 10px;
}
.chartPie {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.lineChart {
  paddingleft: 15px;
  height: 88%;
}
.form-content-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.flexCenter {
  display: flex;
  justify-content: center;
  align-items: center;
}
.btnBoxSty {
  position: absolute;
  top: 0;
  right: 40px;
  z-index: 9;
  display: flex;
  flex-direction: column;
  justify-content: end;
}
.line {
  border: 2px solid rgba(0, 0, 0, 0.1);
  margin-top: -20px;
  margin-bottom: 25px;
}
.btnSty {
  display: flex;
  align-items: end;
  flex-direction: column;
  button {
    background: rgb(255, 255, 255);
    margin-left: 0;
    width: 44px;
    border: none;
    font-size: 16px;
    i {
      color: rgb(0, 0, 0);
      font-size: 20px;
    }
  }
  .btnSort {
    :deep(.el-button) {
      border: none;
    }
  }
}
:deep(.vxe-cell) {
  display: flex;
  justify-content: center;
}
.echartTable {
  background: pink;
  #el-drawer__title {
    margin-bottom: 0 !important;
  }
  .el-drawer__header {
    background: pink !important;
    z-index: 99999;
  }
}
:deep(.el-button + .el-button) {
  background: skyblue !important;
  margin-left: 0;
}
:deep(.el-drawer__header) {
  margin-bottom: 0;
}
.btnDownload {
  margin-bottom: 25px;
}
</style>
