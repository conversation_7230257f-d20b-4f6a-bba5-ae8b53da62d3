<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="ic_sy_smsl">
<rect id="Rectangle 9" width="60" height="60" rx="4" fill="#FFFAF1"/>
<g id="Video">
<g id="&#228;&#186;&#186;&#232;&#132;&#184;&#232;&#175;&#134;&#229;&#136;&#171;">
<mask id="mask0_382_18280" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="12" y="12" width="36" height="36">
<path id="&#232;&#183;&#175;&#229;&#190;&#132;" fill-rule="evenodd" clip-rule="evenodd" d="M12.5 12.5H47.5V47.5H12.5V12.5Z" fill="white"/>
</mask>
<g mask="url(#mask0_382_18280)">
<path id="&#232;&#183;&#175;&#229;&#190;&#132;_2" fill-rule="evenodd" clip-rule="evenodd" d="M41.2031 17.6101C41.2031 16.0637 39.9495 14.8101 38.4031 14.8101H17.4031C15.8567 14.8101 14.6031 16.0637 14.6031 17.6101V39.3101C14.6031 40.8565 15.8567 42.1101 17.4031 42.1101H38.4031C39.9495 42.1101 41.2031 40.8565 41.2031 39.3101V17.6101Z" fill="url(#paint0_linear_382_18280)"/>
<g id="&#232;&#183;&#175;&#229;&#190;&#132;_3" filter="url(#filter0_bi_382_18280)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M46.1016 22.1289C46.1016 20.5825 44.848 19.3289 43.3016 19.3289H21.9078C20.3614 19.3289 19.1078 20.5825 19.1078 22.1289V42.9899C19.1078 44.5363 20.3614 45.7899 21.9078 45.7899H43.3016C44.848 45.7899 46.1016 44.5363 46.1016 42.9899V22.1289Z" fill="#FDDFA1" fill-opacity="0.4"/>
<path d="M43.3016 19.5039H21.9078C20.4581 19.5039 19.2828 20.6791 19.2828 22.1289V42.9899C19.2828 44.4397 20.4581 45.6149 21.9078 45.6149H43.3016C44.7513 45.6149 45.9266 44.4397 45.9266 42.9899V22.1289C45.9266 20.6791 44.7513 19.5039 43.3016 19.5039Z" stroke="url(#paint1_linear_382_18280)" stroke-width="0.35"/>
</g>
<g id="&#229;&#136;&#134;&#231;&#187;&#132; 1" filter="url(#filter1_d_382_18280)">
<path id="&#232;&#183;&#175;&#229;&#190;&#132; (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M24.8385 35.8334C25.2412 35.8334 25.5677 36.1598 25.5677 36.5625V39.4756H28.4807C28.8834 39.4756 29.2099 39.802 29.2099 40.2047C29.2099 40.6074 28.8834 40.9339 28.4807 40.9339H24.8385C24.4358 40.9339 24.1094 40.6074 24.1094 40.2047V36.5625C24.1094 36.1598 24.4358 35.8334 24.8385 35.8334Z" fill="white"/>
<path id="&#232;&#183;&#175;&#229;&#190;&#132; (Stroke)_2" fill-rule="evenodd" clip-rule="evenodd" d="M39.4182 35.8334C39.8209 35.8334 40.1474 36.1598 40.1474 36.5625V40.2047C40.1474 40.6074 39.8209 40.9339 39.4182 40.9339H35.776C35.3733 40.9339 35.0469 40.6074 35.0469 40.2047C35.0469 39.802 35.3733 39.4756 35.776 39.4756H38.6891V36.5625C38.6891 36.1598 39.0155 35.8334 39.4182 35.8334Z" fill="white"/>
<path id="&#232;&#183;&#175;&#229;&#190;&#132; (Stroke)_3" fill-rule="evenodd" clip-rule="evenodd" d="M35.0469 25.625C35.0469 25.2223 35.3733 24.8959 35.776 24.8959H39.4182C39.8209 24.8959 40.1474 25.2223 40.1474 25.625V29.2672C40.1474 29.6699 39.8209 29.9964 39.4182 29.9964C39.0155 29.9964 38.6891 29.6699 38.6891 29.2672V26.3542H35.776C35.3733 26.3542 35.0469 26.0277 35.0469 25.625Z" fill="white"/>
<path id="&#232;&#183;&#175;&#229;&#190;&#132; (Stroke)_4" fill-rule="evenodd" clip-rule="evenodd" d="M24.1094 25.625C24.1094 25.2223 24.4358 24.8959 24.8385 24.8959H28.4807C28.8834 24.8959 29.2099 25.2223 29.2099 25.625C29.2099 26.0277 28.8834 26.3542 28.4807 26.3542H25.5677V29.2672C25.5677 29.6699 25.2412 29.9964 24.8385 29.9964C24.4358 29.9964 24.1094 29.6699 24.1094 29.2672V25.625Z" fill="white"/>
<path id="&#232;&#183;&#175;&#229;&#190;&#132; (Stroke)_5" fill-rule="evenodd" clip-rule="evenodd" d="M29.725 36.0428C29.4395 35.7618 28.9803 35.7637 28.6971 36.0478C28.4128 36.333 28.4136 36.7947 28.6988 37.079L29.2135 36.5625C28.6988 37.079 28.6986 37.0788 28.6988 37.079L28.6997 37.0799L28.7007 37.0809L28.703 37.0831L28.7089 37.0889L28.7257 37.105C28.7391 37.1176 28.757 37.1341 28.7792 37.154C28.8236 37.1936 28.8858 37.2465 28.9648 37.3082C29.1226 37.4314 29.3501 37.5912 29.6414 37.7498C30.2239 38.0669 31.0734 38.3855 32.1339 38.3855C33.1946 38.3855 34.0418 38.0668 34.6223 37.749C34.9126 37.5901 35.1389 37.43 35.2958 37.3065C35.3745 37.2446 35.4362 37.1916 35.4805 37.1518C35.5026 37.1319 35.5204 37.1153 35.5337 37.1026L35.5505 37.0864L35.5563 37.0806L35.5587 37.0783L35.5597 37.0773C35.5599 37.0771 35.5605 37.0764 35.0432 36.5625L35.5605 37.0764C35.8444 36.7907 35.8428 36.329 35.5571 36.0452C35.2725 35.7625 34.8133 35.763 34.5293 36.0453C34.5296 36.045 34.5294 36.0452 34.5293 36.0453L34.5285 36.0461L34.5274 36.0472L34.5267 36.0479C34.5277 36.0469 34.5283 36.0463 34.5293 36.0453C34.5289 36.0457 34.5282 36.0464 34.5274 36.0472C34.524 36.0505 34.5166 36.0573 34.5055 36.0673C34.4832 36.0873 34.4458 36.1197 34.3941 36.1604C34.2904 36.2419 34.1312 36.3553 33.922 36.4698C33.5035 36.6989 32.896 36.9271 32.1339 36.9271C31.3714 36.9271 30.7608 36.6988 30.3388 36.469C30.1279 36.3542 29.9671 36.2405 29.8622 36.1586C29.8099 36.1178 29.772 36.0853 29.7493 36.0651C29.738 36.055 29.7305 36.048 29.727 36.0447C29.7261 36.0438 29.7254 36.0432 29.725 36.0428C29.725 36.0428 29.725 36.0428 29.725 36.0428Z" fill="white"/>
<path id="&#232;&#183;&#175;&#229;&#190;&#132; (Stroke)_6" fill-rule="evenodd" clip-rule="evenodd" d="M32.1339 28.5417C32.5366 28.5417 32.863 28.8682 32.863 29.2709V32.5522C32.863 33.1567 32.5652 33.6972 32.1875 34.0711C31.8097 34.4451 31.268 34.736 30.6682 34.736H30.3073C29.9046 34.736 29.5781 34.4096 29.5781 34.0069C29.5781 33.6041 29.9046 33.2777 30.3073 33.2777H30.6682C30.7976 33.2777 30.9887 33.2058 31.1615 33.0348C31.3343 32.8637 31.4047 32.6768 31.4047 32.5522V29.2709C31.4047 28.8682 31.7311 28.5417 32.1339 28.5417Z" fill="white"/>
<path id="&#232;&#183;&#175;&#229;&#190;&#132; (Stroke)_7" fill-rule="evenodd" clip-rule="evenodd" d="M35.776 28.5417C36.1787 28.5417 36.5052 28.8682 36.5052 29.2709V30.0037C36.5052 30.4064 36.1787 30.7329 35.776 30.7329C35.3733 30.7329 35.0469 30.4064 35.0469 30.0037V29.2709C35.0469 28.8682 35.3733 28.5417 35.776 28.5417Z" fill="white"/>
<path id="&#232;&#183;&#175;&#229;&#190;&#132; (Stroke)_8" fill-rule="evenodd" clip-rule="evenodd" d="M28.487 28.5417C28.8897 28.5417 29.2161 28.8682 29.2161 29.2709V30.0037C29.2161 30.4064 28.8897 30.7329 28.487 30.7329C28.0843 30.7329 27.7578 30.4064 27.7578 30.0037V29.2709C27.7578 28.8682 28.0843 28.5417 28.487 28.5417Z" fill="white"/>
</g>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_bi_382_18280" x="13.8594" y="14.0789" width="37.4922" height="36.9611" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2.625"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_382_18280"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_382_18280" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.2625"/>
<feGaussianBlur stdDeviation="1.3125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.91 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_382_18280"/>
</filter>
<filter id="filter1_d_382_18280" x="22.7094" y="23.4959" width="18.8391" height="18.838" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.7"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.999788 0 0 0 0 0.726825 0 0 0 0 0.193693 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_382_18280"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_382_18280" result="shape"/>
</filter>
<linearGradient id="paint0_linear_382_18280" x1="41.2031" y1="14.8101" x2="13.9123" y2="41.4011" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAB836"/>
<stop offset="1" stop-color="#FEDD93"/>
</linearGradient>
<linearGradient id="paint1_linear_382_18280" x1="32.6047" y1="45.7899" x2="32.6047" y2="19.3289" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDB930" stop-opacity="0.23"/>
<stop offset="0.574544" stop-color="white" stop-opacity="0.53"/>
</linearGradient>
</defs>
</svg>
