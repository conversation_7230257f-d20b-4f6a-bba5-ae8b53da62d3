<template>
  <div class="dashboard-editor-container">
    <!-- <github-corner class="github-corner" /> -->

    <panel-group @handleSetLineChartData="handleSetLineChartData" />

    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <line-chart :chart-data="lineChartData" />
    </el-row>

    <!-- <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <raddar-chart />
        </div>
      </el-col>
        <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <pie-chart />
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <bar-chart />
        </div>
      </el-col> -->
    <!-- </el-row> -->

    <!-- <el-row :gutter="8">
      <el-col :xs="{span: 24}" :sm="{span: 24}" :md="{span: 24}" :lg="{span: 12}" :xl="{span: 12}" style="padding-right:8px;margin-bottom:30px;">
        <transaction-table />
      </el-col>
      <el-col :xs="{span: 24}" :sm="{span: 12}" :md="{span: 12}" :lg="{span: 6}" :xl="{span: 6}" style="margin-bottom:30px;">
        <todo-list />
      </el-col>
      <el-col :xs="{span: 24}" :sm="{span: 12}" :md="{span: 12}" :lg="{span: 6}" :xl="{span: 6}" style="margin-bottom:30px;">
        <box-card />
      </el-col>
    </el-row> -->
  </div>
</template>

<script>
// import GithubCorner from '@/components/GithubCorner'
import PanelGroup from './components/PanelGroup'
import LineChart from './components/LineChart'
import request from '@/utils/request'
// import RaddarChart from './components/RaddarChart'
// import PieChart from './components/PieChart'
// import BarChart from './components/BarChart'
// import TransactionTable from './components/TransactionTable'
// import TodoList from './components/TodoList'
// import BoxCard from './components/BoxCard'

const lineChartData = {
  car: {
    expectedData: []
  },
  scanning: {
    expectedData: []
  },
  fallFeedBack: {
    expectedData: []
  },
  totalFail: {
    expectedData: []
  },
  cancel: {
    expectedData: []
  },
  intercept: {
    expectedData: []
  },
  noMatchThreeSegment: {
    expectedData: []
  },
  superCycle: {
    expectedData: []
  }
  
}

export default {
  name: 'DashboardAdmin',
  components: {
    // GithubCorner,
    PanelGroup,
    LineChart
    // RaddarChart
    // PieChart,
    // BarChart,
    // TransactionTable,
    // TodoList,
    // BoxCard
  },
  data() {
    return {     
      lineChartData: lineChartData.car
    }
  },
  methods: {
    handleSetLineChartData(type,systemTypeCode) {
      return request({
        url: '/scada/quantity/list',
        method: 'post',
        data: { dataType : type,dbCode: systemTypeCode }
      }).then(resp => {
        switch(type){
          case "car":
            lineChartData.car.expectedData = resp.data.result
            this.lineChartData = lineChartData.car
            break
          case "scanning":
            lineChartData.scanning.expectedData = resp.data.result
            this.lineChartData = lineChartData.scanning
            break
          case "fallFeedBack":
            lineChartData.fallFeedBack.expectedData = resp.data.result
            this.lineChartData = lineChartData.fallFeedBack
            break            
          case "totalFail":
            lineChartData.totalFail.expectedData = resp.data.result
            this.lineChartData = lineChartData.totalFail
            break
          case "cancel":
            lineChartData.cancel.expectedData = resp.data.result
            this.lineChartData = lineChartData.cancel
            break            
          case "intercept":
            lineChartData.intercept.expectedData = resp.data.result
            this.lineChartData = lineChartData.intercept
            break
          case "noMatchThreeSegment":
            lineChartData.noMatchThreeSegment.expectedData = resp.data.result
            this.lineChartData = lineChartData.noMatchThreeSegment
            break
          case "superCycle":
            lineChartData.superCycle.expectedData = resp.data.result
            this.lineChartData = lineChartData.superCycle
            break
        }
        
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || '加载失败！',
          type: 'error',
          duration: 2 * 1000
        })
      })
      // this.lineChartData = lineChartData[type]
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .github-corner {
    position: absolute;
    top: 0px;
    border: 0;
    right: 0;
  }

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
