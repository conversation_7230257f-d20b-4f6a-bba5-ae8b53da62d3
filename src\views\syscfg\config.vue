<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="$t('common.Parameter')" prop="paramRemark">
        <el-input v-model="queryParams.paramRemark" :placeholder="$t('common.PlParameter')" clearable
          style="width: 240px" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.ParameterKey')" prop="paramName">
        <el-input v-model="queryParams.paramName" :placeholder="$t('common.PlParameterKey')" clearable
          style="width: 240px" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item> -->


      <el-form-item :label="$t('common.SystemType')" prop="SystemType">
        <el-select v-model="systemTypeCode" style="width:120px;display: block;">
          <el-option v-for="item in systemOptions" :key="item.dbCode" :label="item.dbName" :value="item.dbCode" />
        </el-select>
      </el-form-item>


      <el-form-item :label="$t('common.Group')" prop="paramGroup">
        <el-select v-model="queryParams.paramGroup" :placeholder="$t('common.PlGroup')" clearable>
          <el-option v-for="dict in dict.type.ParamGroup" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('common.Search')
        }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('common.Reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">{{ $t('common.Add')
        }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">{{
          $t('common.Update') }}</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
          >导出</el-button>
        </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-refresh" size="mini" @click="handleRefreshCache">{{
          $t('common.UpdateCache') }}</el-button>
      </el-col>
      <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>

    <el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column :label="$t('common.Id')" align="center" prop="id" />
      <el-table-column :label="$t('common.Parameter')" align="center" prop="paramRemark"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('common.ParameterKey')" align="center" prop="paramName"
        :show-overflow-tooltip="true" />
      <el-table-column :label="$t('common.ParameterValue')" align="center" prop="paramValue"
        :show-overflow-tooltip='true' />
      <el-table-column :label="$t('common.Group')" align="center" prop="paramGroup" :show-overflow-tooltip='true' />
      <!-- <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" /> -->
      <el-table-column :label="$t('common.Operation')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">{{ $t('common.Update')
          }}</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">{{
            $t('common.Delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('common.Parameter')" prop="paramRemark">
          <el-input v-model="form.paramRemark" :placeholder="$t('common.PlParameter')" />
        </el-form-item>
        <el-form-item :label="$t('common.ParameterKey')" prop="paramName">
          <el-input v-model="form.paramName" :disabled="isEditing" :placeholder="$t('common.PlParameterKey')" />
        </el-form-item>
        <el-form-item :label="$t('common.ParameterValue')" prop="paramValue">
          <!-- <el-input v-model="form.paramValue" placeholder="请输入参数键值" /> -->
          <el-select v-if="form.modeTypelist" v-model="form.paramValue" clearable transfer>
            <el-option v-for="item in form.modeTypelist2" :key="item.key" :value="item.key" :label="item.label" />
          </el-select>
          <el-input v-else v-model="form.paramValue" :placeholder="$t('common.PlParameterValue')" />
        </el-form-item>

        <el-form-item :label="$t('common.Group')" prop="paramGroup">
          <el-select v-model="form.paramGroup" :placeholder="$t('common.PlGroup')">
            <el-option v-for="dict in dict.type.ParamGroup" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
          </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('common.Determine') }}</el-button>
        <el-button @click="cancel">{{ $t('common.Cancellation') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
//import { listConfig, getConfig, delConfig, addConfig, updateConfig, refreshCache } from "@/api/system/config";

export default {
  name: "Config",
  dicts: ['ParamGroup'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      configList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        paramRemark: undefined,
        paramName: undefined,
        configType: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        paramRemark: [
          { required: true, message: this.$t('common.PlParameter'), trigger: "blur" }
        ],
        paramName: [
          { required: true, message: this.$t('common.PlParameterKey'), trigger: "blur" }
        ],
        paramValue: [
          { required: true, message: this.$t('common.ParameterValue'), trigger: "blur" }
        ]
      },
      systemOptions:this.getBoxValue() ,
      systemTypeCode: "1",
      isEditing: false
    };
  },
  created () {
    this.systemOptions = 
    this.getList();
  },
  methods: {
    /** 查询参数列表 */
  async   getList() {

      let url = '/cfg/param/list'
      let type = ''
      if ( await this.systemOptions) {
        console.log( await this.systemOptions,'测试')
        type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;
      }


      if (type == 'ffs') {
        url = '/ffs' + url
      }


      this.loading = true;

      return request({
        url: url,
        method: 'post',
        data: { curPage: this.queryParams.pageNum, pageSize: this.queryParams.pageSize, dbCode: this.systemTypeCode, paramName: this.queryParams.paramName, paramRemark: this.queryParams.paramRemark, paramGroup: this.queryParams.paramGroup }
      }).then(response => {
        this.configList = response.data.result.records
        this.configList.forEach(data => {
          let emptyArr = []
          if (data.modeTypelist !== undefined && data.modeTypelist !== "" && data.modeTypelist !== null) {
            emptyArr = data.modeTypelist.split(',').map(item => ({ key: item.split(';')[0], value: item.split(';')[1] }))
            data.modeTypelist2 = emptyArr.map(e => {
              return { label: e.value, value: e.value, key: e.key }
            })
          }
          data.paramGroup = this.selectDictLabel(this.dict.type.ParamGroup, data.paramGroup)
        })
        this.total = response.data.result.total;
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error,
          type: 'error',
          duration: 2 * 1000
        })
      })


      // listConfig(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
      //     this.configList = response.rows;
      //     this.total = response.total;
      //     this.loading = false;
      //   }
      // );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        paramRemark: undefined,
        paramName: undefined,
        paramValue: undefined,
        configType: "Y",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.isEditing = false
      this.open = true;
      this.title = "Add";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      this.loading = true
      row.dbCode = this.systemTypeCode

      let url = '/cfg/param/' + id + '/' + this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;

      if (type == 'ffs') {
        url = '/ffs' + url
      }


      return request({
        url: url,
        method: 'get',
        params: {}
      }).then(response => {
        this.form = response.data.result

        let emptyArr = []
        if (this.form.modeTypelist !== undefined && this.form.modeTypelist !== "" && this.form.modeTypelist !== null) {
          emptyArr = this.form.modeTypelist.split(',').map(item => ({ key: item.split(';')[0], value: item.split(';')[1] }))
          this.form.modeTypelist2 = emptyArr.map(e => {
            return { label: e.value, value: e.value, key: e.key }
          })
        }

        this.isEditing = true
        this.open = true;
        this.title = "Update";
        this.loading = false
      }).catch(error => {
        this.isEditing = false
        this.loading = false
        this.$message({
          message: error,
          type: 'error',
          duration: 2 * 1000
        })
      })

      // getConfig(id).then(response => {
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "修改参数";
      // });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.dbCode = this.systemTypeCode
          if (this.form.id != undefined) {

            let url = '/cfg/param/edit'
            let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;

            if (type == 'ffs') {
              url = '/ffs' + url
            }


            return request({
              url: url,
              method: 'post',
              data: this.form
            }).then(response => {
              this.open = false;
              this.getList();
              this.$message({
                message: this.$t('common.EditSuccess'),
                type: 'success',
                duration: 2 * 1000
              })
            }).catch(error => {
              this.open = false;
              this.$message({
                message: error,
                type: 'error',
                duration: 2 * 1000
              })
            })
          } else {
            let url = '/cfg/param/insert'
            let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;

            if (type == 'ffs') {
              url = '/ffs' + url
            }
            return request({
              url: url,
              method: 'post',
              data: this.form
            }).then(response => {
              this.open = false;
              this.getList();
              this.$message({
                message: this.$t('common.AddSuccess'),
                type: 'success',
                duration: 2 * 1000
              })
            }).catch(error => {
              this.open = false;
              this.$message({
                message: error,
                type: 'error',
                duration: 2 * 1000
              })
            })
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      let typeCode = this.systemTypeCode
      this.$modal.confirm('Are you sure to delete parameter number as"' + ids + '"Data？', 'prompt', {
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        type: 'warning'
      }).then(function () {

        let url = '/cfg/param/delete/' + ids + '/' + typeCode
        let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;

        if (type == 'ffs') {
          url = '/ffs' + url
        }

        return request({
          url: url,
          method: 'get',
          params: {}
        })
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(this.$t('common.DelSuccess'));
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {

      let url = 'system/config/export'
      let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;

      if (type == 'ffs') {
        url = 'ffs/' + url
      }

      this.download('system/config/export', {
        ...this.queryParams
      }, `config_${new Date().getTime()}.xlsx`)
    },
    /** 刷新缓存按钮操作 */
    handleRefreshCache() {
      this.$confirm('Update Cache will immediately update WCS system parameters. Please confirm whether to continue execution？', 'prompt', {
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        type: 'warning'
      }).then(() => {
        this.loading = true

        let url = '/cfg/param/update/cache'
        let type = this.systemOptions.find(option => option.dbCode === this.systemTypeCode).dbType;

        if (type == 'ffs') {
          url = '/ffs' + url
        }

        return request({
          url: url,
          method: 'get',
          params: { dbCode: this.systemTypeCode }
          //data: this.tableData
        }).then(response => {
          this.loading = false
          this.$message({
            message: response.msg,
            type: 'success',
            duration: 2 * 1000
          })
        }).catch(error => {
          this.loading = false
          this.$message({
            message: error,
            type: 'error',
            duration: 2 * 1000
          })
        })
      })
      // refreshCache().then(() => {
      //   this.$modal.msgSuccess("刷新成功");
      // });
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params: { paramName: 'ScadaConnInfo' }
      }).then((res) => {
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    },
  }
};
</script>

<style>

.el-select-dropdown__list{
    display: block !important;
}
</style>