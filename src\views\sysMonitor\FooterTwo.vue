<template>
    <div>
      <div id="gauge" style="width:500px;height:350px;"></div>
    <!-- <Footer></Footer> -->
    </div>
  </template>
  
  <script>
  //import Footer from '@/views/sysMonitor/Footer.vue';
  import echarts from 'echarts'
  //require('echarts/theme/macarons') // echarts theme
  export default {
    // components: {Footer},
    name: 'Footer',
    mounted() {
      this.SetGaugeEchart(0,0,0,0);
    },
    methods: {
      SetGaugeEchart(str1,str2,str3,str4) {
        let myChart = echarts.init(document.getElementById("gauge"));
        var option = {
          tooltip: {
            // a 系列名称  b  数据项名称  c  数值
            formatter: "{a} <br/>{c} {b}"
          },
          toolbox: {
            show: true,
            feature: {
              restore: { show: false },
              saveAsImage: { show: false }
            }
          },
          series: [
            {
              name: "上层运行速度",
              type: "gauge",
              center: ["25%", "36%"], // 默认全局居中
              radius: "55%", // 圆半径
              min: 0,
              max: 10,
              startAngle:200,
              // 结束角度
              endAngle: -20,
              // 定义居于上层，否则会被覆盖
              z: 3,
              // 分成多少等份
              splitNumber: 10,
              axisLine: {
                // 坐标轴线
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  width: 8
                }
              },
              axisTick: {
                // 坐标轴小标记
                length: 12, // 属性length控制线长
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  color: "auto"
                }
              },
              splitLine: {
                // 分隔线
                length: 20, // 属性length控制线长
                lineStyle: {
                  // 属性lineStyle（详见lineStyle）控制线条样式
                  color: "auto"
                }
              },
              // 指针
              pointer: {
                width: 5
              },
              // 仪表盘内刻度提示显示样式
            //   axisLabel: {
            //     backgroundColor: "auto",
            //     borderRadius: 2,
            //     color: "#eee",
            //     padding: 3,
            //     textShadowBlur: 2,
            //     textShadowOffsetX: 1,
            //     textShadowOffsetY: 1,
            //     textShadowColor: "#222"
            //   },
              // 仪表盘内 单位 样式 km/h
              title: {
                // 其余属性默认使用全局文本样式，详见TEXTSTYLE
                fontWeight: "bolder",
                fontSize: 14,
                // 文字倾斜样式
                fontStyle: "italic"
              },
              detail: {
                show: false
              },
              // 当前的 值 和 单位
              data: [{ value: 0.2, name: "m/s" }]
            },
            {
              name: "下层运行速度",
              type: "gauge",
              center: ["25%", "76%"], // 默认全局居中
              radius: "55%", // 圆半径
              min: 0,
              max: 10,
              startAngle:200,
              // 结束角度
              endAngle: -20,
              // 定义居于上层，否则会被覆盖
              z: 3,
              // 分成多少等份
              splitNumber: 10,
              axisLine: {
                // 坐标轴线
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  width: 8
                }
              },
              axisTick: {
                // 坐标轴小标记
                length: 12, // 属性length控制线长
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  color: "auto"
                }
              },
              splitLine: {
                // 分隔线
                length: 20, // 属性length控制线长
                lineStyle: {
                  // 属性lineStyle（详见lineStyle）控制线条样式
                  color: "auto"
                }
              },
              // 指针
              pointer: {
                width: 5
              },
              // 仪表盘内刻度提示显示样式
            //   axisLabel: {
            //     backgroundColor: "auto",
            //     borderRadius: 2,
            //     color: "#eee",
            //     padding: 3,
            //     textShadowBlur: 2,
            //     textShadowOffsetX: 1,
            //     textShadowOffsetY: 1,
            //     textShadowColor: "#222"
            //   },
              // 仪表盘内 单位 样式 km/h
              title: {
                // 其余属性默认使用全局文本样式，详见TEXTSTYLE
                fontWeight: "bolder",
                fontSize: 14,
                // 文字倾斜样式
                fontStyle: "italic"
              },
              detail: {
                show: false
              },
              // 当前的 值 和 单位
              data: [{ value: 0.2, name: "m/s" }]
            },
            {
              name: "上层小车占有率",
              type: "gauge",
              // 圆心位置
              center: ["75%", "36%"], // 默认全局居中
              radius: "55%", // 圆半径
              min: 0,
              max: 100,
              startAngle:200,
              // 结束角度
              endAngle: -20,
              // 分成多少等份
              splitNumber: 10,
              axisLine: {
                // 坐标轴线
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  width: 8
                }
              },
              axisTick: {
                // 坐标轴小标记
                length: 12, // 属性length控制线长
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  color: "auto"
                }
              },
              splitLine: {
                // 分隔线
                length: 20, // 属性length控制线长
                lineStyle: {
                  // 属性lineStyle（详见lineStyle）控制线条样式
                  color: "auto"
                }
              },
              // 指针
              pointer: {
                width: 5
              },
              title: {
                // 位置
                //offsetCenter: [0, "-30%"] // x, y，单位px
              },
              detail: {
                show: false
                // 其余属性默认使用全局文本样式，详见TEXTSTYLE
                //fontWeight: "bolder"
              },
              data: [{ value: 0.2, name: "%" }]
            },
            {
              name: "下层小车占有率",
              type: "gauge",
              // 圆心位置
              center: ["75%", "76%"], // 默认全局居中
              radius: "55%", // 圆半径
              min: 0,
              max: 100,
              //clockWise : false,
              startAngle: 200,
              endAngle: -20,
              // 分成多少等份
              splitNumber: 10,
              axisLine: {
                // 坐标轴线
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  width: 8
                }
              },
              axisTick: {
                // 坐标轴小标记
                length: 12, // 属性length控制线长
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  color: "auto"
                }
              },
              splitLine: {
                // 分隔线
                length: 20, // 属性length控制线长
                lineStyle: {
                  // 属性lineStyle（详见lineStyle）控制线条样式
                  color: "auto"
                }
              },
              // 指针
              pointer: {
                width: 5
              },
              title: {
                // 位置
                //offsetCenter: [0, "-30%"] // x, y，单位px
              },
              detail: {
                show: false
                // 其余属性默认使用全局文本样式，详见TEXTSTYLE
                //fontWeight: "bolder"
              },
              data: [{ value: 0.2, name: "%" }]
            },
          ]
        };
  
        option.series[0].data[0].value = str1;
          option.series[1].data[0].value = str2;
          option.series[2].data[0].value = str3;
          option.series[3].data[0].value = str4;
          myChart.setOption(option, true);
        // setInterval(function() {
        //   option.series[0].data[0].value = (Math.random() * 2).toFixed(2) - 0;
        //   option.series[1].data[0].value = (Math.random() * 7).toFixed(2) - 0;
        //   // option.series[2].data[0].value = (Math.random() * 2).toFixed(2) - 0;
        //   // option.series[3].data[0].value = (Math.random() * 2).toFixed(2) - 0;
        //   myChart.setOption(option, true);
        // }, 2000);
      }
    }
  };
  </script>