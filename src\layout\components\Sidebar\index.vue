<template>
  <div :class="{ 'has-logo': showLogo }">
    <div class="system-title" :class="{ collapsed: isCollapse }">
      <div class="title-bg"></div>
      <span class="title-text" v-show="!isCollapse">COGY氪技</span>
    </div>
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        :background-color="'#fff !important'"
        :text-color="'rgb(0,0,0)'"
        :active-text-color="'#2D89FF'"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in processedRoutes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>


<script>
import { mapGetters } from "vuex";
import Logo from "./Logo.vue";
import SidebarItem from "./SidebarItem.vue";
import variables from "@/styles/variables.scss";
// import Cookies from "js-cookie";

export default {
  components: { SidebarItem, Logo },

  computed: {
    ...mapGetters(["sidebar"]),
    processedRoutes() {
      const currentPath = this.$route.path;
      console.log('路由文件',this.$router.options.routes)
      return this.$router.options.routes.map((route) => {
        // if (route.children) {
        //   return {
        //     ...route,
        //     children: route.children.map((child) => {
        //       const str = currentPath;
        //       const lastIndex = str.lastIndexOf("/");
        //       const lastPart = str.substring(lastIndex + 1);

        //       const isActive = lastPart === child.path;
        //       const newChild = {
        //         ...child,
        //         meta: {
        //           ...child.meta,
        //           icon:
        //             child.meta && child.meta.icon
        //               ? isActive
        //                 ? child.meta.icon + "1"
        //                 : child.meta && child.meta.icon
        //               : child.meta && child.meta.icon,
        //         },
        //       };

        //       return newChild;
        //     }),
        //   };
        // }
        return route;
      });
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // 确保在刷新后，activeMenu能正确反映当前路由
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
  mounted() {
    this.applyActiveItemStyle();
  },
  watch: {
    isCollapse: {
      handler(newVal) {
        this.$nextTick(() => {
          const systemTitle = document.querySelector(".system-title");
          const titleBg = document.querySelector(".title-bg");
          systemTitle.style.padding = newVal ? "0 12px" : "0px 20px";
          titleBg.style.marginRight = newVal ? "0px" : "10px";
        });

        if (!newVal) {
          this.applyActiveItemStyle();
        }
      },
      immediate: true, // 确保组件创建时也执行一次
    },
    $route() {
      this.applyActiveItemStyle();
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
    applyActiveItemStyle() {
      const currentHome = !!this.$route.meta.currentHome;
      this.$nextTick(() => {
        const activeElement = document.querySelector(".active");
        if (activeElement) {
          // activeElement.style.setProperty(
          //   "width",
          //   "calc(100% - 10px)",
          //   "important"
          // );
          activeElement.style.setProperty("border-radius", "0", "important");
          activeElement.style.setProperty("display", "block", "important");
        }
        const el = document.querySelectorAll(".el-scrollbar__view");
        el.forEach((item) => {
          item.style.display = "flex";
        });
        const activeLink = document.querySelector(
          ".router-link-exact-active.router-link-active"
        );
        if (activeLink) {
          // activeLink.style.setProperty(
          //   "width",
          //   "calc(100% - 10px)",
          //   "important"
          // );
          activeLink.style.setProperty("background-color", "#EDF4FD", "important");
          activeLink.style.setProperty("border-radius", "0", "important");
          activeLink.style.setProperty("display", "block", "important");
        }

        const aa = document.querySelector(".active");
        if (aa) {
          aa.style.setProperty("width", "130px");
          aa.style.setProperty("borderRadius", "0px");
        }
        const activeA = document.querySelector(".submenu-title-noDropdown");
        if (activeA) {
          activeA.style.setProperty("padding-left", "20px", "important");
          // activeA.style.setProperty("background-color", "#EDF4FD", "important");
          activeA.style.setProperty("color", "#262626", "important");

          // 添加鼠标进入和离开事件监听器
          activeA.addEventListener("mouseenter", () => {
            activeA.style.setProperty(
              "background-color",
              "#F6F6F6",
              "important"
            );
          });

          activeA.addEventListener("mouseleave", () => {
            activeA.style.setProperty(
              "background-color",
              "#fff",
              "important"
            );
          });
        }

        if (currentHome) {
          const activeLink = document.querySelector(
            ".router-link-exact-active.router-link-active"
          );
          if (activeLink) {
            
            activeLink.style.backgroundColor = "#242424 !important";
            activeLink.style.setProperty("color", "#fff !important");
          }
        } else {
          const activeA = document.querySelector(
            ".router-link-exact-active.router-link-active"
          );
          if (activeA) {
            // activeA.style.width = "calc(100% - 10px)"; // 减去左右边距
            activeA.style.borderRadius = "8px"; // 添加圆角
            // activeLink.style.setProperty("background-color", "#EDF4FD", "important");
          }
          const activeItem = document.querySelector(".el-menu-item.is-active");
          if (activeItem) {
            activeItem.style.backgroundColor = "#fff"; // 假设片背景色为白色
            activeItem.style.borderRadius = "8px"; // 添加圆角
            activeItem.style.color = "#fff"; // 添加圆角
            // activeA.style.setProperty("background-color", "#EDF4FD", "important");
            activeA.style.setProperty("color", "#262626", "important");
            activeA.style.setProperty("position", "relative", "important");
            activeA.style.setProperty("z-index", "100", "important");
          }
          const activeB = document.querySelectorAll(".el-menu-item");
          activeB.forEach((item) => {
            item.style.paddingLeft = "40px"; // 添加左右边距
            // item.style.marginLeft = "10px";
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.system-title {
  height: 50px;
  background: -webkit-linear-gradient(left, #337dea, #0053cd);
  background: linear-gradient(to right, #337dea, #0053cd);
  display: flex;
  background-size: 100% 100%; /* 强制拉伸填满容器 */
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: padding 0.3s;

  &.collapsed {
    padding: 0 10px;

    .title-bg {
      margin-right: 0;
    }
  }

  .title-bg {
    width: 34px;
    height: 34px;
    background-image: url("../../../assets/user_images/logoASS.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border-radius: 4px;
    transition: margin-right 0.3s;
  }

  .title-text {
    color: #ffffff;
    font-family: "YouSheBiaoTiHei-Regular";
    font-size: 20px;
    white-space: nowrap;
    height: 30px;
    font-weight: 500;
  }
}

::v-deep .svg-icon {
  width: 20px !important; // 原来默认16px，增加4px
  height: 20px !important; // 原来默认16px，增加4px
}
::v-deep .el-submenu__title {
  background-color: #ffffff !important;
}

::v-deep .submenu-title-noDropdown {
  padding-left: 20px !important;
  // margin-left: 0 !important;
  background-color: #edf4fd !important;
  color: #fff !important;

  &:hover {
    background-color: #f6f6f6 !important;
  }

  &.el-menu-item {
    background-color: #edf4fd !important;
    color: #fff !important;

    &:hover {
      background-color: #f6f6f6 !important;
    }
  }
}

::v-deep .router-link-active {
  display: block;
  // width: calc(100% - 10px) !important;
  transition: all 0.3s ease;
  // background-color: #EDF4FD !important;
  background-color: transparent !important;

  &:hover {
    width: calc(100% - 0px) !important;
    border-radius: 0 !important;
    background-color: transparent !important;
  }
}
</style>
