<template>
  <div style="padding:5px">
    <el-form :model="form" style="margin:15px 0 15px 0">
      <el-form-item>
        <span>
          {{ $t('common.Date') }}:
          <el-date-picker v-model="form.start" :clearable="false" type="datetime" :placeholder="$t('page.usermanagement.StartDate')"
            value-format="yyyy-MM-dd HH:mm:ss" style="width:185px" />
          -
          <el-date-picker v-model="form.end" :clearable="false" type="datetime" :placeholder="$t('page.usermanagement.EndDate')"
            value-format="yyyy-MM-dd HH:mm:ss" style="width:185px" />
        </span>

        <span style="margin: 0 0 0 20px">
        {{$t('common.SystemType')}}:
        <el-select v-model="systemTypeCode" style="width:120px" @change="handleOptionChange">
          <el-option v-for="item in systemOptions" :key="item.dbCode" :label="item.dbName" :value="item.dbCode" />
        </el-select>
        </span>

        <span style="margin:0 0 0 20px">
          <el-button-group>
            <el-button style="margin-right:15px" icon="el-icon-search" @click="loadData" type="primary">{{ $t('common.Select') }}</el-button>
            <el-button style="margin-right:15px" icon="el-icon-refresh-left" @click="resetForm"
              type="warning">{{ $t('common.Reset') }}</el-button>
            <el-button icon="el-icon-download" @click="exportDataEvent">{{ $t('common.Export') }}</el-button>
          </el-button-group>
        </span>
      </el-form-item>
    </el-form>
    <vxe-table ref="xTable" show-footer resizable show-overflow :height="tableHeight" :loading="loading"
      :footer-span-method="footerColspanMethod" :footer-method="footerMethod" :data="tableData" 
       v-if="isWcs">
      <vxe-table-column :resizable="true" type="seq" :title="$t('common.SerialNumber')" width="120" />
      <vxe-table-column :resizable="true" field="timespan" :title="$t('common.TimeInterval')" />
      <vxe-table-column :resizable="true" field="supNo" :title="$t('common.Supply')" />
      <vxe-table-column :resizable="true" field="scanBarcodeQuantity" :title="$t('common.NumberOfScannedBarcodeRecords')" />
      <vxe-table-column :resizable="true" field="packageLoaderQuantity" :title="$t('common.RecordTheNumberOfPackagesLoadedOntoTheVehicle')" />
      <vxe-table-column :resizable="true" field="fallFeedbackQuantity" :title="$t('common.NumberOfDropFeedbackRecords')" />
    </vxe-table>

     
    <vxe-table
      ref="xTable"
      stripe
      resizable
      show-overflow
      :height="tableHeight"
      :loading="loading"
      :footer-span-method="footerColspanMethod"
      :footer-method="footerMethodDws"
      :data="tableData"
       v-if="!isWcs"
    >
      <vxe-table-column :resizable="true" type="seq" :title="$t('common.SerialNumber')" width="120" />
      <vxe-table-column :resizable="true" field="timespan" :title="$t('common.TimeInterval')" />
      <vxe-table-column :resizable="true" field="dwsNo" :title="$t('common.dwsNo')" />
      <vxe-table-column :resizable="true" field="scanQuantity" :title="$t('common.scanQuantity')" />
      <vxe-table-column :resizable="true" field="arrivalQuantity" :title="$t('common.arrivalQuantity')" />
      <vxe-table-column :resizable="true" field="passBackQuantity" :title="$t('common.passBackQuantity')" />
    </vxe-table>
  </div>
</template>

<script>
import request from '@/utils/request'
import XEUtils from 'xe-utils'

export default {
  name: 'SortingCountSup',
  data() {
    return {
      form: {
        start: this.getDate(0),
        end: this.getDate(1)
      },
      tableHeight: window.innerHeight - 180,
      loading: false,
      tableData: [],
      dataTotal: '',
      systemOptions:this.getBoxValue(),
      systemTypeCode:"1",
      isWcs : true
    }
  },
  methods: {
    getDate(format) {
      var today = new Date()
      var year = today.getFullYear()
      var month = today.getMonth() + 1
      var date = today.getDate()

      if (format === 0) {
        return year + '-' + month + '-' + date + ' 00:00:00'
      } else if (format === 1) {
        return year + '-' + month + '-' + date + ' 23:59:59'
      }
    },
    resetForm() {
      this.form = {
        start: this.getDate(0),
        end: this.getDate(1)
      }
      this.tableData = []
      this.tablePage = {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      }
    },
    loadData() {
      this.loading = true
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

      let url = '/report/sup/list'
      if (type == 'dws'){
          url = '/dws/report/sup/list'
      }
      if (type == 'ffs'){
          url = '/ffs/report/sup/list'
      }
      
      return request({
        url: url,
        method: 'post',
        data: { startTime: this.form.start, endTime: this.form.end,dbCode:typeCode }
        //method: 'post',
        //data: { queryParam: this.form }
      }).then(resp => {
        this.dataTotal = resp.data.result[resp.data.result.length - 1]
        //this.$delete(resp.data.result,resp.data.result.length-1)
        this.tableData = resp.data.result

        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || '加载失败！',
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    handleOptionChange(){
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;
      if (type == 'dws'){
          this.isWcs = false
      }else{
          this.isWcs = true
      }
      this.loadData()
    },
    footerMethod({ columns, data }) {
      const maxs = []
      const sums = []
      maxs.push('最大值')
      maxs.push(this.dataTotal.scanBarcodeQuantityMaximum)
      maxs.push(this.dataTotal.packageLoaderQuantityMaximum)
      maxs.push(this.dataTotal.fallFeedbackQuantityMaximum)

      sums.push('求和值')
      sums.push(this.dataTotal.scanBarcodeQuantityTotal)
      sums.push(this.dataTotal.packageLoaderQuantityTotal)
      sums.push(this.dataTotal.fallFeedbackQuantityTotal)
      return [maxs, sums]
    },
    footerMethodDws({ columns, data }) {
      const maxs = []
      const sums = []
      maxs.push('最大值')
      maxs.push(this.dataTotal.scanQuantityMaximum)
      maxs.push(this.dataTotal.arrivalQuantityMaximum)
      maxs.push(this.dataTotal.passBackQuantityMaximum)

      sums.push('求和值')
      sums.push(this.dataTotal.scanQuantityTotal)
      sums.push(this.dataTotal.arrivalQuantityTotal)
      sums.push(this.dataTotal.passBackQuantityTotal)
      
      return [maxs, sums]
    },
    footerColspanMethod({ _columnIndex }) {
      if (_columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3
        }
      }
    },
    exportDataEvent() {
      //this.$refs.xTable.exportData({ filename: '数量统计_供包台（' + this.form.start + '至' + this.form.end + '）', type: 'csv', isFooter: false })
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

      let url = '/report/sup/export'
      if (type == 'dws'){
          url = '/dws/report/sup/export'
      }
      if (type == 'ffs'){
          url = '/ffs/report/sup/export'
      }
      return request({
        url: url,
        method: 'post',
        data: { startTime:this.form.start,endTime: this.form.end,dbCode:typeCode},
        responseType: 'blob'
      }).then(resp => {
        const blob = new Blob([resp], { type: 'application/vnd.ms-excel' })
        const a = document.createElement('a')
        a.href = URL.createObjectURL(blob)
        a.download = '数量统计_供包台（' + this.form.start + '至' + this.form.end + '）' + '.xlsx'
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(a.href)
        document.body.removeChild(a)
        this.$message({
          message: this.$t('common.BeginExport'),
          type: 'success',
          duration: 2 * 1000
        })
      }).catch(error => {
        this.$message({
          message: error || this.$t('common.ExportFailed'),
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params:{paramName: 'ScadaConnInfo'}
      }).then((res) => {
        console.log(res,'测试')
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    }
  }
}
</script>
