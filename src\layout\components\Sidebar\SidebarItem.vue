<template>
  <div v-if="!item.hidden">
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
        (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
        !item.alwaysShow
      "
    >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{ 'submenu-title-noDropdown': !isNest, 'is-active-menu-item': $route.path === resolvePath(onlyOneChild.path) }"
        >
          <item
            :icon="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"
            :title="onlyOneChild.meta.title"
          />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      popper-append-to-body
      :class="{'is-active-submenu': isSubmenuActive(item)}"
    >
      <template slot="title">
        <item
          v-if="item.meta"
          :icon="item.meta && item.meta.icon"
          :title="item.meta.title"
        />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'

export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    this.onlyOneChild = null
    return {}
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      return path.resolve(this.basePath, routePath)
    },
    isSubmenuActive(item) {
      if (!item.children) return false;
      
      const currentPath = this.$route.path;
      return item.children.some(child => {
        if (child.children) {
          // 递归检查子菜单
          return this.isSubmenuActive(child);
        }
        const childPath = this.resolvePath(path.resolve(this.basePath, child.path));
        return currentPath === childPath || currentPath.startsWith(childPath + '/');
      });
    }
  }
}
</script>


<style scoped>
.is-active-menu-item::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 45%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #2D89FF;
  border-radius: 4px;
}

/* 为el-menu-item.is-active添加左侧指示器 */
.el-menu-item.is-active::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 45%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #2D89FF;
  border-radius: 4px;
}

/* 为激活的子菜单标题添加左侧指示器 */
.el-submenu.is-active-submenu .el-submenu__title::before {
  content: '';
  position: absolute;
  left: 12px;
  top: 45%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #2D89FF;
  border-radius: 4px;
}

/* 已经激活的子菜单 */
.is-active-submenu .el-submenu__title {
  color: #2D89FF !important;
}

/* 二级菜单选中样式 */
.nest-menu .el-menu-item.is-active::before {
  content: '';
  position: absolute;
  left: 56px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #2D89FF;
  border-radius: 4px;
}
</style>