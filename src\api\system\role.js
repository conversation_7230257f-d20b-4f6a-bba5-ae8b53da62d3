import request from '@/utils/request'

// 查询当前系统的下所有的角色
export function listRole(query) {
  return request({
    url: '/LoginPermission/GetAllRoleInfo',
    method: 'post'
  })
}
// 通过用户id查询当前用户的所有角色信息
export function QueryListRole(data) {
  return request({
    url: '/LoginPermission/GetRoleInfoByUserId',
    method: 'post',
    data
  })
}
// 查询当前用户的某个角色
export function QueryRoleByRoleId(data) {
  return request({
    url: `/LoginPermission/GetRolesByRoleId?roleid=${data}`,
    method: 'post'
  })
}
// 查询角色详细
export function getRole(data) {
  return request({
    url: '/LoginPermission/GetRoleInfoByRoleId',
    method: 'post',
    data
  })
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/LoginPermission/SaveRoleInfoByRoleId',
    method: 'post',
    data
  })
}

// 修改角色
export function updateRole(data) {
  return request({
    url: '/LoginPermission/SaveRoleInfoByRoleId',
    method: 'post',
    data
  })
}

// 角色数据权限
export function dataScope(data) {
  return request({
    url: '/system/role/dataScope',
    method: 'put',
    data: data
  })
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status
  }
  return request({
    url: '/system/role/changeStatus',
    method: 'put',
    data: data
  })
}

// 删除角色
export function delRole(roleId) {
  return request({
    url: '/LoginPermission/DeleteRoleInfoByRoleIds',
    method: 'post',
    data: roleId
  })
}

// 查询角色已授权用户列表
export function allocatedUserList(query) {
  return request({
    url: '/system/role/authUser/allocatedList',
    method: 'get',
    params: query
  })
}

// 查询角色未授权用户列表
export function unallocatedUserList(query) {
  return request({
    url: '/system/role/authUser/unallocatedList',
    method: 'get',
    params: query
  })
}

// 取消用户授权角色
export function authUserCancel(data) {
  return request({
    url: '/system/role/authUser/cancel',
    method: 'put',
    data: data
  })
}

// 批量取消用户授权角色
export function authUserCancelAll(data) {
  return request({
    url: '/system/role/authUser/cancelAll',
    method: 'put',
    params: data
  })
}

// 授权用户选择
export function authUserSelectAll(data) {
  return request({
    url: '/system/role/authUser/selectAll',
    method: 'put',
    params: data
  })
}
