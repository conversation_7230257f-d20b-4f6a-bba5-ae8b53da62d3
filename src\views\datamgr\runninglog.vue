<template>
  <div style="padding:5px">
    <el-form
      :model="form"
      style="margin:15px 0 15px 0"
    >
      <el-form-item>
        <span>
          <!-- {{ $t('common.Date') }}:
          <el-date-picker
            v-model="form.start"
            :clearable="false"
            type="datetime"
            :placeholder="$t('page.usermanagement.StartDate')"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:185px"
          />
          -
          <el-date-picker
            v-model="form.end"
            :clearable="false"
            type="datetime"
            :placeholder="$t('page.usermanagement.EndDate')"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width:185px"
          /> -->
          {{ $t('common.Date') }}:
          <el-date-picker v-model="form.start" type="datetime" :picker-options="minPickerOptions"
            @change="handleMinChange" value-format="yyyy-MM-dd HH:mm:ss" style="width: 185px"
            :placeholder="$t('page.usermanagement.StartDate')"></el-date-picker>
          -
          <el-date-picker v-model="form.end" type="datetime" :picker-options="maxPickerOptions" @change="handleMaxChange"
            value-format="yyyy-MM-dd HH:mm:ss" style="width: 185px" :placeholder="$t('page.usermanagement.EndDate')"></el-date-picker>
        </span>

        <span style="margin:0 0 0 20px">
          {{ $t('common.Grade') }}:
          <el-select v-model="form.logLevel" style="width:95px">
            <el-option
                    v-for="dict in dict.type.runLogLevel"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
          </el-select>
        </span>

        <span style="margin:0 0 0 20px">
          {{ $t('common.Source') }}:
          <el-select v-model="form.logSource" style="width:95px">
            <el-option
                    v-for="dict in dict.type.runLogSource"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
          </el-select>
        </span>

        
        <!-- <span style="margin:0 0 0 20px">
          {{ $t('common.plcType') }}:
          <el-select v-model="form.plcType" style="width:95px">
            <el-option
              v-for="item in plcTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </span> -->

        <span style="margin:0 0 0 20px">
          {{ $t('common.Message') }}:
          <el-input v-model="form.message" style="width: 136px" />
        </span>
        <span style="margin: 0 0 0 20px">
        {{$t('common.SystemType')}}:
          <el-select v-model="systemTypeCode" style="width:120px">
            <el-option
              v-for="item in systemOptions"
              :key="item.dbCode"
              :label="item.dbName"
              :value="item.dbCode"
            />
          </el-select>
        </span>

        <span style="margin:0 0 0 20px">
          <el-button-group>
            <el-button style="margin-right:15px" icon="el-icon-search" @click="setloadData" type="primary">{{ $t('common.Select') }}</el-button>
            <el-button style="margin-right:15px" icon="el-icon-refresh-left" @click="resetForm"  type="warning">{{ $t('common.Reset') }}</el-button>
            <!-- <el-button icon="el-icon-download" @click="exportDataEvent">{{ $t('common.Export') }}</el-button> -->
          </el-button-group>
        </span>
      </el-form-item>
    </el-form>

    <!-- <vxe-toolbar>
      <template v-slot:buttons>
        <vxe-button icon="vxe-icon--download" @click="exportDataEvent">导出数据</vxe-button>
      </template>
    </vxe-toolbar> -->

    <vxe-table
      ref="xTable"
      resizable
      show-overflow
      :height="tableHeight"
      row-id="id"
      :loading="loading"
      :data="tableData"
    >
      <vxe-table-column :resizable="true" type="seq" :title="$t('common.SerialNumber')" width="120" />
      <vxe-table-column :resizable="true" field="businessDate" :title="$t('common.Date')" />
      <!-- <vxe-table-column :resizable="true" field="message" :title="$t('common.Message')" /> -->

      <vxe-table-column :resizable="true" :title="$t('common.Message')">
        <template #default="{ row }">
          <div v-html="renderHighlightedMessage(row.message)"></div>
        </template>
      </vxe-table-column>

      <!-- <vxe-table-column :resizable="true" field="extraData" :title="$t('common.ExtraData')"  /> -->
      <!-- <vxe-table-column :resizable="true" field="plcType" :title="$t('common.plcType')"  /> -->
      <vxe-table-column :resizable="true" field="logLevel" :title="$t('common.Grade')" />
      <vxe-table-column :resizable="true" field="logSource" :title="$t('common.Source')" />
    </vxe-table>

    <vxe-pager
      background
      :loading="loading"
      :current-page="tablePage.currentPage"
      :page-size="tablePage.pageSize"
      :total="tablePage.totalResult"
      :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
      @page-change="handlePageChange"
    />
  </div>
</template>

<script>
import request from '@/utils/request'
import moment from 'moment'

export default {
  name: 'OperationLog',
  dicts: ['runLogLevel','runLogSource'],
  data() {
    return {
      // logLevelOptions: [
      //   { value: '', label: '所有' },
      //   { value: 'INFO' },
      //   { value: 'WARN' },
      //   { value: 'ERROR' }
      // ],
      // logSourceOptions: [
      //   { value: '', label: '所有' },
      //   { value: 'WCS' },
      //   { value: 'SCADA' },
      //   { value: 'SUP' }
      // ],
      plcTypeOptions: [
        { value: '', label: '所有' },
        { value: '2' , label: '上层' },
        { value: '1' , label: '下层' }
      ],
      form: {
        start: this.getDate(0),
        end: this.getDate(1),
        logLevel: '',
        logSource: '',
        plcType: '',
        deviceNo: '',
        message: ''
      },
      tableHeight: window.innerHeight - 210,
      loading: false,
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      },
      systemOptions:this.getBoxValue(),
      systemTypeCode:"1",
      minPickerOptions: {
      },
      maxPickerOptions: {
        disabledDate: time => {
          // 最大时间不能选择比最小时间早五天以上的时间
          if (this.form.start) {
            const minTimestamp = new Date(this.form.start).getTime()
            const maxTimestamp = time.getTime()
            return maxTimestamp < minTimestamp - 1000 || maxTimestamp > minTimestamp + 5 * 24 * 60 * 60 * 1000 - 1000
          }
          return false
        }
      },
    }
  },
  methods: {
    handleMinChange() {
      // 最小时间改变后，需要更新最大时间的可选范围
      if (this.form.start && this.form.end) {
        const minTimestamp = new Date(this.form.start).getTime()
        const maxTimestamp = new Date(this.form.end).getTime()
        if (maxTimestamp < minTimestamp || maxTimestamp > minTimestamp + 3 * 24 * 60 * 60 * 1000) {
          // 最大时间不符合要求，修改为符合要求的时间
          this.form.end = moment(new Date(Math.min(minTimestamp + 5 * 24 * 60 * 60 * 1000 - 1000, Date.now()))).format("YYYY-MM-DD HH:mm:ss");
        }
      }
    },
    handleMaxChange() {
      // 最大时间改变后，需要更新最小时间的可选范围
      if (this.form.start && this.form.end) {
        const minTimestamp = new Date(this.form.start).getTime()
        const maxTimestamp = new Date(this.form.end).getTime()
        if (maxTimestamp < minTimestamp || maxTimestamp > minTimestamp + 3 * 24 * 60 * 60 * 1000) {
          // 最大时间不符合要求，修改为符合要求的时间
          //this.form.end = moment(new Date(Math.min(minTimestamp + 5 * 24 * 60 * 60 * 1000, Date.now()))).format("YYYY-MM-DD hh:mm:ss");
        }
      }
    },
    getDate(format) {
      var today = new Date()
      var year = today.getFullYear()
      var month = today.getMonth() + 1
      var date = today.getDate()

      if (format === 0) {
        return year + '-' + month + '-' + date + ' 00:00:00'
      } else if (format === 1) {
        return year + '-' + month + '-' + date + ' 23:59:59'
      }
    },
    loadData() {
      this.loading = true
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

      let url = '/log/common/list'
      if (type == 'dws'){
          url = '/dws/log/common/list'
      }
      if (type == 'ffs'){
          url = '/ffs/log/common/list'
      }
      return request({
        url: url,
        method: 'post',
        data: {startTime:this.form.start,endTime: this.form.end,logLevel: this.form.logLevel,logSource: this.form.logSource,message: this.form.message,pageSize: this.tablePage.pageSize,curPage : this.tablePage.currentPage,dbCode:typeCode,businessTimeType:this.businessTimeType}
      }).then(resp => {
        this.tableData = resp.data.result && resp.data.result.content || []
        this.tablePage.currentPage = resp.data.result && resp.data.result.number + 1  || 1
        //this.tablePage.pageSize = resp.data.result.numberOfElements
        this.tablePage.totalResult = resp.data.result && resp.data.result.totalElements
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || '加载失败！',
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    setloadData() {
      this.tablePage.currentPage = 1
      this.loadData()
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.loadData()
    },
    resetForm() {
      this.form = {
        start: this.getDate(0),
        end: this.getDate(1),
        logLevel: '',
        logSource: '',
        plcType: '',
        deviceNo: ''
      }
      this.tableData = []
      this.tablePage = {
        currentPage: 1,
        pageSize: 20,
        totalResult: 0
      }
    },
    exportDataEvent() {
      this.loading = true
      
      let typeCode = this.systemTypeCode
      let type = this.systemOptions.find(option => option.dbCode === typeCode).dbType;

      let url = '/log/common/export'
      if (type == 'dws'){
          url = '/dws/log/common/export'
      }

      return request({
        url: url,
        method: 'post',
        data: {startTime:this.form.start,endTime: this.form.end,logLevel: this.form.logLevel,logSource: this.form.logSource,message: this.form.message,dbCode:typeCode},
        responseType: 'blob'
      }).then(resp => {
        const blob = new Blob([resp], { type: 'application/vnd.ms-excel' })
        const a = document.createElement('a')
        a.href = URL.createObjectURL(blob)
        a.download = '运行日志.xlsx'
        a.style.display = 'none'
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(a.href)
        document.body.removeChild(a)
        this.$message({
          message: this.$t('common.BeginExport'),
          type: 'success',
          duration: 2 * 1000
        })
        // this.$refs.xTable.exportData({
        //   filename: '运行日志',
        //   type: 'csv',
        //   data: resp.data.result
        // })
        this.loading = false
      }).catch(error => {
        this.loading = false
        this.$message({
          message: error || this.$t('common.ExportFailed'),
          type: 'error',
          duration: 2 * 1000
        })
      })
    },
    getBoxValue() {
      return request({
        url: '/dataSource/list',
        method: 'get',
        params:{paramName: 'ScadaConnInfo'}
      }).then((res) => {
        this.systemOptions = res.data.result
        this.systemTypeCode = this.systemOptions[0].dbCode
      });
    },
    renderHighlightedMessage(message) {
      return message;
    }
    
  }
}
</script>
