// 泰语
const th = {
  login: {
    changelanguage: "เปลี่ยนภาษา",
    index: "I'm six",
    title: "ระบบค้นหาการคัดแยกของ CoGY",
    Success: "เข้าสู่ระบบสำเร็จ",
    plsuaername: "กรุณาใส่ชื่อผู้ใช้",
    plspassword: "กรุณาใส่รหัส",
    plscode: "กรุณาใส่รหัสยืนยัน",
    login: "เข้าสู่ระบบ",
    code: "บาร์โค้ด",
    chinese: "中文",
    english: "english",
    thai: "ภาษาไทย",
    ms: "Bahasa Melayu",
    vi: "Tiếng Việt",
    es: "Español",
    pt: "Bahasa Portugis",
    id: "Bahasa Indonesia",
    ph: "Filipino",
    SwipeRight: "เลื่อนไปทางขวา",
  },
  welcome: {
    title: "ยินดีตอนรับสู่ระบบค้นหาข้อมูลการคัดแยก",
  },
  headers: {
    title: "ระบบค้นหาข้อมูลการคัดแยก",
    loginout: "ออกจากระบบ",
  },
  sidebar: {
    Home: "หน้าแรก",
    Syscfg: "การตั้งค่าระบบ",
    Userinfosup: "การจัดการผู้ใช้เครื่องจ่ายงาน",
    User: "การจัดการผู้ใช้",
    Role: "การจัดการการทำงาน",
    Menu: "การจัดการเมนู",
    Sysparam: "พารามิเตอร์ระบบ",
    Errchute: "ช่องผิดปกติ",
    Sysurl: "จำนวนวันในการเก็บบันทึก",
    Sortingplan: "แผนการคัดแยก",
    Datamgr: "การจัดการข้อมูล",
    Runninglog: "บันทึกการทำงาน",
    Locklog: "บันทึกการล็อค",
    Sortinglog: "บันทึกการคัดแยก",
    Packagenumberlog: "ประวัติหมายเลขกระสอบ",
    Sortingrepush: "การส่งข้อมูลซ้ำ",
    Sortingreport: "สถิติรายงาน",
    Chutereport: "ปริมาณช่องจัดเรียง",
    Sortingcountsup: "สถิติจำนวน (เครื่องจ่ายงาน)",
    Sortingcountuser: "สถิติจำนวน (รหัสพนักงาน)",
    Packagenumbercount: "สถิติกระสอบทั้งหมด",
    Sortingefficiency: "ผลสถิติประสิทธิภาพ",
    Errorreport: "ประเภทสถิติ",
    Monitor: "การตรวจสอบระบบ",
    Scada: "การควบคุม SCADA",
    Job: "งานตามกำหนดเวลา",
    Config: "การกำหนดค่า",
    Dict: "การจัดการพจนานุกรม",
  },
  page: {
    homepage: {
      boardingcount: "จำนวนรถ",
      scancount: "จำนวนการสแกน",
      cellcount: "จำนวนช่องลง",
      cancelleditems: "การยกเลิก",
      rabbitinterceptions: "จำนวนพัสดุสกัดกัน J&T",
      unmatchedcodes: "รหัสสามส่วนไม่ตรงกับช่องพัสดุ",
      overbounds: "จำนวนรวมรอบของพัสดุ",
      anomalies: "จำนวนทั้งหมดของความผิดปกติ",
      sortingvolume: "จำนวนที่คัดแยก",
      monday: "วันจันทร์",
      tuesday: "วันอังคาร",
      wednesday: "วันพุธ",
      thursday: "วันศุกร์",
      friday: "วันศุกร์",
      saturday: "วันเสาร์",
      sunday: "วันอาทิตย์",
    },
    Serviceusermanagement: {
      Refresh: "รีเฟรช",
      AddUser: "เพิ่มผู้ใช้งาน",
      BulkDelete: "ลบจำนวน",
      FullName: "ชื่อ",
      EmployeeID: "รหัสพนักงาน",
      Query: "ค้นหา",
      Import: "นำเข้า",
      OnlyExcel: "(สามารถนำเข้าได้เฉพาะไฟล์รูปแบบ Excel.xlsx เท่านั้น)",
      SerialNumber: "ลำดับ",
      UserEmployeeID: "รหัสพนักงานผู้ใช้",
      UserName: "ชื่อผู้ใช้",
      UserID: "ID ผู้ใช้",
      Creator: "ผู้สร้าง",
      CreationTime: "เวลาที่สร้าง",
      Operation: "การดำเนินการ",
      UserPassword: "รหัสผู้ใช้",
      ConfirmPassword: "ยืนยันรหัสผ่าน",
      NewPassword: "รหัสใหม่",
      Save: "บันทึก",
      Cancel: "ยกเลิก",
      UpdateUser: "แก้ไขผู้ใช้",
    },
    usermanagement: {
      UserID: "ID ผู้ใช้",
      UserName: "ชื่อผู้ใช้",
      UserAccount: "บัญชีผู้ใช้",
      PlaseUserAccount: "กรุณาใส่บัญชีผู้ใช้",
      PhoneNumber: "เบอร์โทรศัพท์",
      PleasePhoneNumber: "กรุณาใส่เบอร์โทรศัพท์",
      Status: "สถานะ",
      PleaseStatus: "กรุณาใส่สถานะ",
      StartDate: "วันที่เริ่ม",
      EndDate: "วันที่สิ้นสุด",
      CreationTime: "เวลาที่สร้าง",
      Operation: "การดำเนินการ",
      UserGender: "เพศของผู้ใช้",
      UserNickname: "ชื่อเล่นผู้ใช้",
      Email: "อีเมล",
      UserPassword: "รหัสผู้ใช้",
      Active: "สถานะปกติ",
      Inactive: "ไม่ได้ใช้งาน",
      Role: "บทบาททำงาน",
      Note: "หมายเหตุ",
      Search: "ค้นหา",
      Reset: "รีเฟรช",
      Add: "เพิ่ม",
      Delete: "ลบ",
      Update: "แก้ไข",
      More: "เพิ่มเติม",
      ResetPassword: "รีเฟรชรหัส",
    },
  },
  common: {
    DerivedData: "ส่งออกข้อมูล",
    ViewDWSPeakEffect: "ดตรวจสอบประสิทธิภาพสูงสุดของเครื่อง DWS",
    peakEffect: "ประสิทธิภาพสูงสุด",
    SearchData: "ค้นหาข้อมูล",
    NoData: "ไม่มีข้อมูล",
    Arrival: "ขาเข้า",
    Departure: "ขาออก",
    ArrivalAndDeparture: "ขาเข้าและขาออก",
    CloseOther: "ปิด",
    CloseAll: "ปิดทั้งหมด",
    RoleName: "ชื่อการทำงาน",
    PlaseRoleName: "กรุณาใส่ชื่อการทำงาน",
    PermissionCharacters: "สัญลักษณ์",
    PlasePermissionCharacters: "กรุณาใส่ OTP",
    Order: "คำสั่ง",
    Status: "สถานะ",
    Search: "ค้นหา",
    Reset: "รีเฟรช",
    Delete: "ลบ",
    Update: "แก้ไข",
    More: "เพิ่มเติม",
    CreationTime: "เวลาที่สร้าง",
    Operation: "การดำเนินการ",
    Add: "เพิ่ม",
    UpdateCache: "อัปเดตแคช",
    Select: "ค้นหา",
    Import: "นำเข้า",
    Export: "ส่งออก",
    SerialNumber: "ลำดับ",
    RoleNumber: "หมายเลขการทำงาน",
    Memo: "หมายเหตุ",
    UpdateDate: "เวลาอัปเดต",
    ParameterName: "ชื่อพารามิเตอร์",
    ParameterValue: "ค่าพารามิเตอร์",
    PleaseSelectARole: "กรุณาเลือกการทำงาน",
    PleaseInputnickname: "กรุณาใส่ชื่อเครื่องของผู้ใช้",
    PleaseInputPhoneNumber: "กรุณาใส่เบอร์โทรศัพท์",
    PleaseInputEmail: "กรุณาใส่อีเมล",
    PleaseInputUserName: "กรุณาใส่ชื่อผู้ใช้",
    PleaseInputPassWord: "กรุณาใส่รหัส",
    RoleStatus: "สถานะการทำงาน",
    RoleCode: "ID ผู้ใช้",
    PlaseInputRoleCode: "กรุณาใส่ID ผู้ใช้",
    CreatorID: "ID ผู้สร้าง",
    Determine: "ยืนยัน",
    Cancellation: "การยกเลิก",
    Task: "ชื่อคำสั่ง",
    PlaseTask: "กรุณาใส่ชื่องาน",
    TaskCode: "หมายเลขคำสั่ง",
    PlaseTaskCode: "กรุณาใส่ลำดับคำสั่งงาน",
    MenuName: "ชื่อเมนู",
    PlaseMenuName: "กรุณาใส่ชื่อเมนู",
    MenuStatus: "สถานะเมนู",
    ExpandAndCollapse: "ขยาย/ยุบ",
    SelectAllDonteSelectAll: "เลือกทั้งหมด/ไม่เลือกทั้งหมด",
    ParentChildLinkage: "การเชื่อมต่อบทบาท",
    MenuPermissions: "การอนุญาตเมนูเปิดอยู่",
    PleaseEnterContent: "กรุณาใส่ข้อมูล",
    Icon: "สัญลักษณ์",
    ComponentPath: "เส้นทาง",
    SchemeID: "แผน ID",
    ModeType: "ประเภทการคัดแยก",
    PlanName: "ชื่อแผน",
    PlanCode: "ลำดับแผน",
    PlanDesc: "คำอธิบายแผน",
    PlanFlag: "ประเภทแผน",
    IsSelected: "ถูกเลือกหรือไม่",
    UpdateDate: "เวลาการแก้ไข",
    Detail: "รายละเอียด",
    Enable: "เปิดใช้",
    Date: "วันที่",
    Grade: "ระดับ",
    Source: "แหล่งที่มา",
    Message: "ข้อความ",
    ExtraData: "ข้อมูลเพิ่ม",
    plcType: "ชั้น",
    Number: "ลำดับ",
    Component: "ส่วนประกอบ",
    Chute: "ช่อง",
    Supply: "เครื่องจ่ายงาน",
    dwsNo: "DWS ลำดับ",
    BigBarRate: "อัตราส่วนพัสดุใหญ่",
    Quantity: "จำนวน",
    SmallBarRate: "อัตราส่วนของกระสอบเล็ก",
    ExceptionCode: "รหัสข้อผิดพลาด",
    Code: "หมายเลขคำสั่ง",
    ScanTime: "เวลาสแกน",
    BoardingTime: "เวลาขึ้นรถ",
    DropTime: "เวลาลงช่อง",
    NextPieceTime: "เวลาบรรจุ",
    passBackTime: "เวลาส่งกลับ",
    arrivalTime: "เวลาวแกนถึง",
    PacketNumber: "หมายเลขกระสอบ",
    TimeType: "ประเภทเวลา",
    turns: "จำนวนรอบ",
    Image: "รูปภาพ",
    UpperLevel: "ชั้นบน",
    LowerLevel: "ชັ້ນล่าง",
    LayerNumber: "จำนวนชັ້ນ",
    PleaseEnterTheDictionaryLabel: "กรุณາใส่ป้ายชื่อพจนานุกรม",
    DictionaryName: "ชื่อพจนานุกรม",
    DictionaryId: "รหัสพจนานุกรม",
    DictionaryType: "ประเภทพจนานุกรม",
    PleaseEnterTheDictionaryType: "กรุณາใส่ประเภทพจนานุกรม",
    PleaseEnterTheDictionaryName: "กรุณາใส่ชื่อพจนานุกรม",
    BagBindingBfficer: "ผู้ผูกพက်แพັກ",
    DictionaryEncoding: "รหัสพจนานุกรม",
    DictionaryTag: "ป้ายชื่อพจนานุกรม",
    DictionaryValue: "ค่า键พจนานุกรม",
    DictionarySort: "การเรียงชนิดพจนานุกรม",
    DictionaryRemark: "หมายเหตุ",
    DictionaryCreateTime: "เวลาสร้าง",
    DataTag: "ป้ายชื่อข้อมูล",
    PleaseEnterTheDataLabel: "กรุณາใส่ป้ายชื่อข้อมูล",
    DataKey: "ค่า键ข้อมูล",
    StyleAttribute: "คุณสมบัติรูปแบบ",
    DisplayOrder: "การแสดงเรียงชนิด",
    EchoStyle: "รูปแบบการแสดงผล",
    ListClass: "รูปแบบรายการ",
    PleaseEnterTheDataKey: "กรุณາใส่ค่า键ข้อมูล",
    PleaseEnterTheStyleAttribute: "กรุณາใส่คุณสมบัติรูปแบบ",
    PleaseEnterTheDisplayOrder: "กรุณາใส่การแสดงเรียงชนิด",
    PleaseEnterTheEchoStyle: "กรุณາใส่รูปแบบการแสดงผล",
    PleaseEnterTheListClass: "กรุณາใส่รูปแบบรายการ",
    PleaseEnterTheRemark: "กรุณາใส่หมายเหตุ",
    PleaseEnterTheContent: "กรุณາใส่เนื้อหา",
    TheAddressNeedsToBehttp:
      "หากเป็นลิงค์ภายนอก ที่อยู่ต้องเริ่มต้นด้วย `http(s)://`",
    TheAddressNeedsToBehttpUser:
      "ที่อยู่ลิงค์ เช่น `user` หากเป็นลิงค์ภายนอกที่ต้องเข้าถึงภายในเครือเทคโนโลยี ให้เริ่มต้นด้วย `http(s)://`",
    TheAddressNeedsToBehttpCatalogue:
      "ที่อยู่ส่วนประกອບ เช่น `system/user/index` โดยเริ่มต้นภายใต้ `views`",
    TheDefaultPassingParametersForTheRoute:
      "พารามิเตอร์ส่งผ่านเริ่มต้นของลิงค์ เช่น `{'id': 1, 'name': 'ry'}`",
    TheComponentWillBeCachedByKeepAlive:
      "หากเลือก จะถูกแคชโดย `keep-alive` ต้องให้ `name` ຂອງส่วนประกອບตรงกับที่อยู่",
    SelectHiddenThenTheRoute:
      "หากเลือกซ่อน ลิงค์จะไม่ปรากฏในແถบຂ້າງ  ແຕ່ยังสามารถเข้าถึงได้",
    SelectDisableThenTheRouteSidebar:
      "หากเลือกปิดใช้งານ ลิงค์จะไม่ปรากฏໃນแถບข້າງ และไม่สามารถเข้าถึงได้",
    PleaseEnterTheRouteParameters: "กรุณາใส่พารາມิเตอร์ลิงค์",
    Yes: "ใช่",
    No: "ไม่",
    Cache: "แคช",
    NoCache: "ไม่แคช",
    AddUser: "เพิ่มผู้ใช้",
    BatchCancelAuthorization: "ยกเลิกการอนุญาตแบบกลุ่ม",
    Close: "ปิด",
    CancelAuthorization: "ยกเลิกการอนุญาต",
    View: "ดู",
    UserType: "ประเภทผู้ใช้",
    PleaseSelectUserType: "กรุณາเลือกประเภทผู้ໃช้",
    Forward: "กลับหน้า",
    Reverse: "ฝนกลับ",
    Lock: "ล็อค",
    Unlock: "ปลดล็อค",
    SendMessage: "ส่งข้อความ",
    TaskGroup: "ชื่อกลุ่มงาน",
    PleaseSelectTaskGroup: "กรุณາเลือกชื่อกลุ่มงาน",
    TaskStatus: "สถานะงาน",
    PleaseSelectTaskStatus: "กรุณາเลือกสถานะงาน",
    Log: "ลॉག",
    InvokeTarget: "строка вызова цели",
    CronExpression: "cron expression",
    ExecuteOnce: "เรียกใช้งານครั้งเดียว",
    TaskDetails: "รายละเอียดงาน",
    DispatchLog: "ลॉགการส่ง",
    InvokeTarget: "วิธีการเรียกใช้",
    BeanCallExample: "Bean call example: ryTask.ryParams('ry')",
    ClassCallExample:
      "Class call example: com.ruoyi.quartz.task.RyTask.ryParams('ry')",
    ParameterDescription:
      "Description of parameters: supports strings, booleans, long integers, floating point numbers, integers",
    PleaseInputInvokeTarget: "กรุณາใส่строка вызова цели",
    PleaseInputCronExpression: "กรุณາใส่cron expression",
    GenerateExpression: "สร้าง expression",
    ExecuteStrategy: "กลยຸทธ์การเรียกใช้งານ",
    MisfirePolicy: "นโยบាយการเรียกใช้งານ",
    ImmediateExecution: "รีแอ็กซ์ทูตอีเมດ",
    DelayExecution: "รีแอ็กซ์ทูตดีเลย์",
    AbandonExecution: "รีแอ็กซ์ทูตแอบนดอน",
    PleaseSelectExecuteStrategy: "กรุณາเลือกกลยุทธ์การเรียกใช้งານ",
    Concurrent: "ร่วมกัน",
    Allow: "อนุญาต",
    Prohibit: "ห้าม",
    PleaseSelectConcurrent: "กรุณາเลือกร่วมกัน",
    CronExpressionGenerator: "Cron expression generator",
    NextExecutionTime: "เวลาเรียกใช้งານครั้งต่อไป",
    TaskDetails: "รายละเอียดงาน",
    TaskGroup1: "กลุ่มงาน",
    DefaultStrategy: "กลยุทธ์เริ่มต้น",
    ExecuteStatus: "สถานะการเรียกใช้งານ",
    PleaseSelectExecuteStatus: "กรุณາเลือกสถานะการเรียกใช้งານ",
    ExecutionTime: "เวลาเรียกใช้งານ",
    PleaseSelectExecutionTime: "กรุณາเลือกเวลาเรียกใช้งານ",
    Clear: "ลบ",
    JobLogId: "รหัสलॉจ",
    JobMessage: "ข้อความลॉจ",
    Detail1: "รายละเอียด",
    DispatchLogDetails: "รายละเอียดการสົ່ງลॉจ",
    PleaseSelect: "กรุณາเลือก",
    SelectStartTime: "เลือกเวลาเริ่มต้น",
    SelectEndTime: "เลือกเวลาสิ้นสุด",
    DebugStart: "Debug(เริ่ม)",
    DebugClose: "Debug(ปิด)",
    ClearPacketNumber: "ลบหมายเลขแพັກ",
    Upload: "อัพโหลด",
    TotalQuery: "พบทั้งหมด",
    property: "รูป",
    all: "ทั้งหมด",
    UnloadingToDeliveryScanning: "สแกนจากรถถล่มຫາที่จัดส่ง",
    BuildingPackageScanning: "สแกนการจັດแพັກ",

    SelectUser: "เลือกผู้ใช้",
    InvokeTargetMethod: "วิธีการเรียกใช้วัตถุประสงค์",
    PleaseSelectTaskGroup: "เลือกกลุ่มงาน",
    CronExpression1: "cron expression",
    ExceptionInfo: "ข้อมูลข้อผิดพลาด",

    Edit: "แก้ไข",
    ScopeOfAuthority: "ขอบเขตสิทธิ์",
    DataPermission: "สิทธิ์ข้อมูล",
    Confirm: "ยืนยัน",
    StartDate: "วันที่เริ่มต้น",
    EndDate: "วันที่สิ้นสุด",
    Weight: "น้ำหนัก",
    length: "ความยาว",
    width: "ความกว้าง",
    heigth: "ความสูง",
    CarNumber: "หมายเลขรถเข็น",
    RequestedGate: "ช่องคัดแยกพัสดุเป้าหมาย",
    PhysicalGrid: "Physical grid",
    taskNo: "หมายเลขคำสั่ง",
    TerminalDispatchCode: "รหัสส่วนที่สาม",
    FallingGridTime: "เวลาลงช่อง",
    PackageGrade: "หมายเลขกระสอบ",
    ChipNumber: "หมายเลขชิป",
    BindingTime: "เวลาที่ผูกคำสั่ง",
    BindingPersonnel: "บุคคลที่ผูกคำสั่ง",
    ScanType: "ประเภทการแกน",
    NextStationNumber: "หมายเลขสถานีถัดไป",
    Rfid: "ใบลาเบล",
    TimeInterval: "ช่วงเวลา",
    SortingQuantity: "จำนวนการคัดแยก",
    TotalSortingWeight: "น้ำหนักรวมในการแกน (KG)",
    NumberOfScannedBarcodeRecords: "จำนวนที่สแกนลงกระสอบ",
    RecordTheNumberOfPackagesLoadedOntoTheVehicle: "จำนวนการบันทึกพัสดุบนรถ",
    NumberOfDropFeedbackRecords: "จำนวนที่ลงกระสอบ",
    scanQuantity: "จำนวนการสแกน",
    arrivalQuantity: "จำนวนการสแกนถึง",
    passBackQuantity: "จำนวนการส่งกลับ",
    TotalNumberOfPackages: "จำนวนกระสอบทั้งหมด",
    Packagenumb: "หมายเลขกระสอบ(จำนวนพัสดุ)",
    Type: "ประเภท",
    Count: "จำนวน",
    SystemType: "ประเภทระบบ",
    EmployeeID: "กรุณาใส่หมายเลข",
    FullName: "กรุณาใส่ชื่อ",
    Password: "กรุณาใส่รหัส",
    ConfirmPassword: "กรุณายืนยันรหัส",
    SelectGender: "กรุณาเลือกเพศ",
    Active: "สถานะปกติ",
    Inactive: "ไม่ได้ใช้งาน",
    Male: "ชาย",
    Female: "หญิง",
    Unknown: "ไม่ทราบ",
    RoleOrder: "ลำดับการทำงาน",
    Show: "แสดงผล",
    Hide: "ซ่อน",
    Default: "ค่าเริ่มต้น",
    System: "ระบบ",
    Success: "สำเร็จ",
    Failure: "ล้มเหลว",
    AddMenu: "เพิ่มเมนู",
    EditMenu: "แก้ไขเมนู",
    ParentMenu: "เมนูหลัก",
    MenuType: "ประเภทเมนู",
    Directory: "สารบบ",
    Menu: "เมนู",
    Button: "ปุ่มกด",
    MenuIcon: "ไอคอนเมนู",
    SelectIcon: "เลือกสัญลักษณ์",
    RouteAddress: "ที่อยู่เส้นทาง",
    DisplayOrder: "ลำดับการแสดงผล",
    ExternalLink: "ลิงก์ภายนอก",
    DisplayStatus: "สถานะการแสดง",
    MenuStatus: "สถานะเมนู",
    RouteParameters: "พารามิเตอร์เส้นทาง",
    Cache1: "แคช",
    ComponentPath: "เส้นทาง",
    AddRole: "เพิ่มบทบาทหน้าที่",
    EditRole: "แก้ไขบทบาท",
    AddPlan: "เพิ่มแผน",
    Cancel: "ยกเลิก",
    FirstSegmentCode: "รหัสส่วนแรก",
    SecondSegmentCode: "รหัสส่วนที่สอง",
    ThirdSegmentCode: "รหัสส่วนที่สาม",
    NextStopCode: "หมายเลขสถานีถัดไป",
    NextStopName: "ชื่อสถานีถัดไป",
    ModificationTime: "เวลาการแก้ไข",
    BulkDelete: "ลบจำนวน",
    AddDetails: "เพิ่มรายละเอียด",
    ByServicePackage: "แพ็คเกจการบริการ",
    ByDwsNo: "ตามหมายเลขของ DWS",
    Granularity: "ความละเอียด",
    LogicCode: "รหัสตรรกะ",
    PacketType: "ประเภทกระสอบ",
    IsUpload: "อัปโหลดหรือไม่",
    AddSuccess: "เพิ่มสำเร็จ",
    EditSuccess: "แก้ไขสำเร็จ",
    DelSuccess: "ลบสำเร็จ",
    ImportSuccessful: "นำเข้าสำเร็จ",
    BeginExport: "เริ่มต้นการส่งออกข้อมูล",
    ModificationFailed: "แก้ไขล้มเหลว",
    AddFailed: "เพิ่มไม่สำเร็จ",
    OperationSuccessful: "การดำเนินการสำเร็จ",
    OperationFailed: "การดำเนินการล้มเหลว",
    OperationCancellation: "การยกเลิกการดำเนินการ",
    ExportFailed: "ส่งออกล้มเหลว",
    LoginOut:
      "คุณออกจากระบบแล้วสามารถยกเลิกการอยู่ในเพจนี้หรือเข้าสู่ระบบอีกครั้ง",
    ConfirmLogout: "ออกจากระบบ",
    LogAgain: "เข้าสู่ระบบอีกครั้ง",
    Remark: "หมายเหตุ",
    DwsNo: "ลำดับเครื่องDWS",
    Notempty: "รหัสผ่านบัญชีหรือรหัสยืนยันไม่สามารถว่างได้!",
    Notpassword: "ชื่อผู้ใช้หรือรหัสผ่านไม่สามารถว่างได้!",
    Id: "คีย์หลักของพารามิเตอร์",
    Parameter: "ชื่อพารามิเตอร์",
    PlParameter: "กรุณาใส่ชื่อพารามิเตอร์",
    ParameterKey: "คีย์พารามิเตอร์",
    PlParameterKey: "กรุณาใส่คีย์พารามิเตอร์",
    ParameterValue: "ค่าพารามิเตอร์",
    PlParameterValue: "กรุณาใส่ค่าพารามิเตอร์",
    Group: "ชื่อกลุ่ม",
    PlGroup: "กรุณาเลือกชื่อกลุ่ม",
  },
  scada: {
    DeviceRunningStatus: "สถานะการทำงานของอุปกรณ์",
    DeviceStopped: "อุปกรณ์หยุดทำงาน",
    DeviceRunning: "อุปกรณ์กำลังทำงาน",
    StartTime: "เวลาที่เริ่ม",
    RunningSpeed: "ความเร็วในการทำงาน",
    CartOccupancyRate: "อัตราการใช้งานรถเข็น",
    TotalDistanceTraveledByDevice: "ระยะทางรวมที่เครื่องจักรเดินเครื่อง",
    DistanceTraveledInCurrentRun: "ระยะทางที่เดินทาง",
    TotalDistanceTraveled: "ระยะทางรวมที่วิ่ง",
    Scans: "จำนวนการสแกน",
    PendingStart: "อยู่ระหว่างดำเนินการ",
    FullScreen: "เต็มจอ",
    ExitFull: "ออกจากเต็มจอ",
    UpperLevelRunningSpeed: "ความเร็วชั้นบน",
    UpperLevelDeviceRunningStatus: "สถานะการทำงานชั้นบน",
    UpperLevelStartTime: "เวลาที่ชั้นบนเริ่มทำงาน",
    UpperLevelCartOccupancyRate: "อัตราการใช้งานรถไฟชั้นบน",
    UpperLevelByDevice: "ระยะทางรวมที่เครื่องจักรชั้นบนวิ่ง",
    LowerLevelDeviceRunningStatus: "สถานะการทำงานชั้นล่าง",
    UpperLayerPLCDisconnect: "การเชื่อมต่อPLC ชั้นบนล้มเหลว",
    LowerLevelPLCDisconnect: "ชั้นล่าง PLC ล้มเหลว",
    UpperLayerPLCConnectionStatus: "สถานะการเชื่อมต่อ PLC ชั้นบน",
    LowerLevelPLCConnectionStatus: "สถานะการเชื่อมต่อ PLC ชั้นล่าง",
    LowerLevelStartTime: "เวลาเริ่มทำงานชั้นล่าง",
    LowerLevelRunningSpeed: "ความเร็วชั้นล่าง",
    LowerLevelCartOccupancyRate: "อัตราการใช้งานรถเข็น",
    LowerLevelTotalDistanceTraveledByDevice: "ระยะทางรวมที่อุปกรณ์วิ่งชั้นล่าง",
    UpperLevelScans: "จำนวนที่ชั้นบนสแกน",
    LowerLevelScans: "จำนวนการสแกนชั้นล่าง",
    AbnormalQuantity: "จำนวนที่ผิดปกติ",
    NumberOfSlotsOccupied: "จำนวนช่องลงกระสอบ",
    FailedRepushQuantity: "จำนวนการล้มเหลว",
    InterceptedQuantity: "จำนวนพัสดุสกัดกัน",
    ExcessiveCirclesQuantity: "จำนวนรอบพัสดุลงกระสอบ",
    UnconfiguredThreeSegmentCodeSlots: "ไม่ได้กำหนดช่องพัสดุรหัสสามส่วน",
    ComprehensiveExceptionSlots: "จำนวนที่ผิดปกติ",
    CancelledItems: "การยกเลิก",
    UnobtainedThreeSegmentCodeInformation: "ไม่มีข้อมูลรหัสส่วนที่สาม",
    WebSocketStatus: "สถานะ WebSocket",
    WCSCommunicationStatus: "สถานะการสื่อสาร WCS",
    Connected: "เชื่อมต่อแล้ว",
    NotConnected: "ไม่เชื่อมต่อ",
    SortingStatus: "สถานะการคัดแยก",
    Idle: "ไม่ได้ใช้งาน",
    Loaded: "กำลังโหลดงาน",
    CartStatus: "สถานะรถเข็น",
    LittleThingsAreQuick:'เจ้าตัวเล็ก',
    Headscratcher: 'กวาด',
    OnLine:'ออนไลน์',
    Offline:'ปิดการเชื่อมต่อ',
    Locked: "ล็อค",
    FullPackage: "เต็ม",
    SlotStatus: "สถานะช่อง",
    InterceptedItem: "รายการที่ถูกสกัดกั้น",
    ExceptionSlot: "ข้อยกเว้น",
    PendingCommunication: "รอดำเนินการ",
    Max: "สูงสุด",
    Cancellation: "การยกเลิก",
    UnProgramme: "ไม่มีโปรแกรม",
    UnThree: "ไม่สามารถจับคู่กับรหัสสามส่วนได้",
    CartOperation: "การดำเนินการใช้รถเข็น",
    OneKeyUnlock: "ปลดล็อคทั้งหมด",
    OneKeyLockSlot: "ล็อคทั้งหมด",
    CartNumber: "หมายเลขรถเข็น",
    FloorNumber: "หมายเลขชั้น",
    UpperLevel: "ชั้นบน",
    LowerLevel: "ระดับต่ำ",
    Lock: "ล็อค",
    Unlock: "ปลอล็อค",
    ManualSwitchStart: "สวิตช์แบบแมนนวล (เริ่ม)",
    ManualSwitchClose: "สวิตช์แบบแมนนวล (ปิด)",
    Run: "การทำงาน",
    Close: "ปิด",
    DisabledList: "ปิดใช้งาน",
    AbnormalAlarm: "การแจ้งเตือนล้มเหลว",
    DisableCart: "ปิดการใช้งานรถเข็น",
    UpperLevelCart: "รถไฟชั้นบน",
    LowerLevelCart: "รถเข็นระดับต่ำ",
    VerificationPassword: "กรุณาใส่รหัสยืนยัน",
    Confirm: "ยืนยัน",
    ClearTheKilometers: "รีเช็ตระยะทาง",
    PleaseSelectClearKilometers: "เลือกการลบข้อมูลระยะทาง",
    FaultLevel: "ระดับความผิดพลาด",
    StartTime: "เวลาที่เริ่ม",
    EndTime: "เวลาสิ้นสุด",
    CriticalAlarm: "สัญญาณเตือนภัยวิกฤต",
    GeneralAlarm: "สัญญาณเตือนภัยทั่วไป",
    MinorAlarm: "สัญญาณเตือนระดับต่ำ",
    SearchCriteria: "เงื่อนไขการค้นหา",
    Time: "วันที่",
    Select: "ค้นหา",
    PleaseEnterContent: "กรุณาใส่ข้อมูล",
    SerialNumber: "ลำดับ",
    Operation: "การดำเนินการ",
    AlarmHelpLink: "เอกสารช่วยเหลือ",
    AlarmType: "ประเภทสัญญาณเตือน",
    AlarmSource: "แหล่งสัญญาณเตือนภัย",
    Content: "เนื้อหา",
  },
};
export default th;
