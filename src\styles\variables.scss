// // sidebar
// $menuText:#bfcbd9;
// $menuActiveText:#409EFF;
// $subMenuActiveText:#f4f4f5; //https://github.com/ElemeFE/element/issues/12951

// $menuBg:#304156;
// $menuHover:#263445;

// $subMenuBg:#1f2d3d;
// $subMenuHover:#001528;

// $sideBarWidth: 220px;

// // the :export directive is the magic sauce for webpack
// // https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
// :export {
//   menuText: $menuText;
//   menuActiveText: $menuActiveText;
//   subMenuActiveText: $subMenuActiveText;
//   menuBg: $menuBg;
//   menuHover: $menuHover;
//   subMenuBg: $subMenuBg;
//   subMenuHover: $subMenuHover;
//   sideBarWidth: $sideBarWidth;
// }
// sidebar
// sidebar
$menuText: #262626;
$menuActiveText: #409eff;
$subMenuActiveText: #262626; //https://github.com/ElemeFE/element/issues/12951

$menuBg: #edf4fd;
$menuHover: #F6F6F6;

$subMenuBg: #ffffff;
$subMenuHover: #edf4fd;

$sideBarWidth: 220px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}


